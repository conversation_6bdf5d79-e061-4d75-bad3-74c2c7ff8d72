﻿using Abbott.Onepdt.Plugins.Models;
using Abbott.Onepdt.Plugins.Service;
using Microsoft.Xrm.Sdk.Query;
using Microsoft.Xrm.Sdk;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using static Abbott.Onepdt.Plugins.Models.CardTypeModel;

namespace Abbott.Onepdt.Plugins.Actions
{
    public class ImplantVerificationAction : IPlugin
    {
        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService service = serviceFactory.CreateOrganizationService(null); // 权限
            ITracingService tracingService = (ITracingService)serviceProvider.GetService(typeof(ITracingService));
            try
            {
                List<ImplantModel> implantModels = new List<ImplantModel>();
                string jsonbody = (string)context.InputParameters["JsonBody"];
                var parsedJson = Newtonsoft.Json.JsonConvert.DeserializeObject<JArray>(jsonbody);
                tracingService.Trace(jsonbody);
                var productQuery = new QueryExpression("onepdt_t_product_mdata");
                productQuery.ColumnSet.AddColumns(
                    "onepdt_name",
                    "onepdt_isbypassverify");
                productQuery.Criteria.AddCondition("onepdt_isbypassverify", ConditionOperator.Equal, true);
                EntityCollection bypassProductEntities = service.RetrieveMultiple(productQuery);

                foreach (var implant in parsedJson) {
                    DateTime implantDate = DateTime.Parse(implant["ImplantDate"].ToString());
                    string sn = implant["SN"].ToString();
                    string model = implant["Model"].ToString();
                    string nonmainland = implant["Nonmainland"].ToString();
                    string id = implant["Id"].ToString();
                    string operationId = implant["MainId"].ToString();
                    if (bypassProductEntities.Entities.Any(e => e.GetAttributeValue<string>("onepdt_name") == model)){

                        ImplantModel implantModel = new ImplantModel
                        { 
                            SN = sn,
                            model = model
                        };
                        if(model.Equals(sn)){
                            implantModel.ResultType = "S";
                        }else{
                            implantModel.ResultType = "E";
                            implantModel.Message = "该产品序列号和型号请填写一致！";
                        }
                        implantModels.Add(implantModel);
                    }
                    else if (nonmainland == "Y") {
                        implantModels.Add(ImplantVerficationService.GetImplantNonmainlandModel(service,tracingService, sn, model, implantDate , id, operationId));
                    }
                    else
                    {
                        implantModels.Add(ImplantVerficationService.GetImplantSAPModel(service,tracingService, sn, model, implantDate, id, operationId));
                    }


                }


                context.OutputParameters["Result"] = Newtonsoft.Json.JsonConvert.SerializeObject(implantModels);

            }
            catch (Exception ex)
            {
                tracingService.Trace(ex.Message + ex.StackTrace);
                throw;
            }
        }
    }
}
