[{"ContainingType": "Abt.Epdt.WebApis.Controller.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/auth/login", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sign<PERSON><PERSON>l", "Type": "System.String", "IsRequired": false}, {"Name": "errorurl", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AuthController", "Method": "LoginSkipSsoSuccess", "RelativePath": "api/auth/login/skip", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sign<PERSON><PERSON>l", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "errorurl", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AuthController", "Method": "LoginSuccess", "RelativePath": "api/auth/login/success", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sign<PERSON><PERSON>l", "Type": "System.String", "IsRequired": false}, {"Name": "errorurl", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AuthController", "Method": "Me", "RelativePath": "api/auth/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AuthController", "Method": "GetSSOAccessToken", "RelativePath": "api/auth/ssotoken", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AuthController", "Method": "GetAccessToken", "RelativePath": "api/auth/token", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "appid", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.EmployeeController", "Method": "GetEmployees", "RelativePath": "api/employee/GetEmployees", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startmodifiedon", "Type": "System.String", "IsRequired": false}, {"Name": "endmodifiedon", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Abt.Epdt.WebApis.Model.ResModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Abt.Epdt.WebApis.Controller.EmployeeController", "Method": "WechatUser", "RelativePath": "api/employee/WechatUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "employee", "Type": "Abt.Epdt.WebApis.Model.EmployeeInfo", "IsRequired": true}], "ReturnTypes": [{"Type": "Abt.Epdt.WebApis.Model.ResModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "BatchDelete", "RelativePath": "api/epdt/<PERSON>ch<PERSON>te", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Abt.Epdt.WebApis.Model.CardTypeModel+BatchDeleteModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "CkeckOperation", "RelativePath": "api/epdt/CkeckOperation", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Abt.Epdt.WebApis.Model.OperationEditData", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "Delete", "RelativePath": "api/epdt/Delete", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "entityname", "Type": "System.String", "IsRequired": false}, {"Name": "entityid", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "EditOperation", "RelativePath": "api/epdt/EditOperation", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Abt.Epdt.WebApis.Model.OperationEditData", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "GetCardTypes", "RelativePath": "api/epdt/GetCardTypes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "operationid", "Type": "System.String", "IsRequired": false}, {"Name": "IsPreview", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "GetConfigFile", "RelativePath": "api/epdt/GetConfigFile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "GetDapplications", "RelativePath": "api/epdt/GetDapplications", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": false}, {"Name": "operationid", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "GetHCPs", "RelativePath": "api/epdt/GetHCPs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": false}, {"Name": "isout", "Type": "System.Boolean", "IsRequired": false}, {"Name": "hospitalcode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "GetHistoryOperation", "RelativePath": "api/epdt/GetHistoryOperation", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "GetHospitals", "RelativePath": "api/epdt/GetHospitals", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "GetOnepdtConfig", "RelativePath": "api/epdt/GetOnepdtConfig", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tablename", "Type": "System.String", "IsRequired": false}, {"Name": "fieldname", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "GetOnePDTProduct", "RelativePath": "api/epdt/GetOnePDTProduct", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "GetOperation", "RelativePath": "api/epdt/GetOperation", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "operationid", "Type": "System.String", "IsRequired": false}, {"Name": "email", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "GetOperationQuery", "RelativePath": "api/epdt/GetOperationQuery", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleid", "Type": "System.String", "IsRequired": false}, {"Name": "proxyemail", "Type": "System.String", "IsRequired": false}, {"Name": "email", "Type": "System.String", "IsRequired": false}, {"Name": "pagesize", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageindex", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchText", "Type": "System.String", "IsRequired": false}, {"Name": "productname", "Type": "System.String", "IsRequired": false}, {"Name": "submittername", "Type": "System.String", "IsRequired": false}, {"Name": "province", "Type": "System.String", "IsRequired": false}, {"Name": "implantType", "Type": "System.String", "IsRequired": false}, {"Name": "starttime", "Type": "System.String", "IsRequired": false}, {"Name": "endtime", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "GetPhoto", "RelativePath": "api/epdt/GetPhoto", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "GetSAPProduct", "RelativePath": "api/epdt/GetSAPProduct", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": false}, {"Name": "isNonMainlandProduct", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "GetTodoList", "RelativePath": "api/epdt/GetTodoList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": false}, {"Name": "type", "Type": "System.Int32", "IsRequired": false}, {"Name": "pagesize", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageindex", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchText", "Type": "System.String", "IsRequired": false}, {"Name": "implantType", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "hospital", "Type": "System.String", "IsRequired": false}, {"Name": "starttime", "Type": "System.String", "IsRequired": false}, {"Name": "endtime", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "GetUserInfo", "RelativePath": "api/epdt/GetUserInfo", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "GetUserManual", "RelativePath": "api/epdt/GetUserManual", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "LoginAndGetRole", "RelativePath": "api/epdt/LoginAndGetRole", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "SubmitOperation", "RelativePath": "api/epdt/SubmitOperation", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "operationid", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "SwitchRole", "RelativePath": "api/epdt/SwitchRole", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userid", "Type": "System.String", "IsRequired": false}, {"Name": "roleid", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.AppController", "Method": "UploadFileAsync", "RelativePath": "api/epdt/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "operationid", "Type": "System.String", "IsRequired": false}, {"Name": "photoid", "Type": "System.String", "IsRequired": false}, {"Name": "classify", "Type": "System.String", "IsRequired": false}, {"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Abt.Epdt.WebApis.Controller.HcpController", "Method": "GetHcps", "RelativePath": "api/hcp/GetHcps", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startmodifiedon", "Type": "System.String", "IsRequired": false}, {"Name": "endmodifiedon", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Abt.Epdt.WebApis.Model.ResModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Abt.Epdt.WebApis.Controller.PolicyController", "Method": "CheckPolicy", "RelativePath": "api/policy/CheckPolicy", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "type", "Type": "System.Int32", "IsRequired": false}, {"Name": "applicationID", "Type": "System.String", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Abt.Epdt.WebApis.Model.ResModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Abt.Epdt.WebApis.Controller.PolicyController", "Method": "CheckPolicyByUser", "RelativePath": "api/policy/CheckPolicyByUser", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "type", "Type": "System.Int32", "IsRequired": false}, {"Name": "applicationID", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Abt.Epdt.WebApis.Model.ResModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Abt.Epdt.WebApis.Controller.PolicyController", "Method": "Consent", "RelativePath": "api/policy/Consent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "param", "Type": "Abt.Epdt.WebApis.Model.ConsentModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Abt.Epdt.WebApis.Model.ResModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Abt.Epdt.WebApis.Controller.PolicyController", "Method": "ConsentByUser", "RelativePath": "api/policy/ConsentByUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "param", "Type": "Abt.Epdt.WebApis.Model.ConsentModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Abt.Epdt.WebApis.Model.ResModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Abt.Epdt.WebApis.Controller.PolicyController", "Method": "GetPolicies", "RelativePath": "api/policy/GetPolicies", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "applicationID", "Type": "System.String", "IsRequired": false}, {"Name": "policyID", "Type": "System.String[]", "IsRequired": false}], "ReturnTypes": [{"Type": "Abt.Epdt.WebApis.Model.ResModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Abt.Epdt.WebApis.Controller.PolicyController", "Method": "RevokePolicy", "RelativePath": "api/policy/RevokePolicy", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "type", "Type": "System.Int32", "IsRequired": false}, {"Name": "applicationID", "Type": "System.String", "IsRequired": false}, {"Name": "userID", "Type": "System.String", "IsRequired": false}, {"Name": "policyID", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Abt.Epdt.WebApis.Model.ResModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Abt.Epdt.WebApis.Controller.PolicyController", "Method": "RevokePolicyByUser", "RelativePath": "api/policy/RevokePolicyByUser", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "type", "Type": "System.Int32", "IsRequired": false}, {"Name": "applicationID", "Type": "System.String", "IsRequired": false}, {"Name": "policyID", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Abt.Epdt.WebApis.Model.ResModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Abt.Epdt.WebApis.Controller.SFTPController", "Method": "SyncSAPProduct", "RelativePath": "api/sftp/sync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}]