﻿using Microsoft.Xrm.Sdk.Workflow;
using Microsoft.Xrm.Sdk;
using System;
using System.Activities;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Collections;
using System.Runtime.Remoting.Messaging;

namespace Abbott.Onepdt.CodeActivities
{
    public class SyncEmployee : CodeActivity
    {
        private string BaseURL = "";
        private string AccessToken = "";

        [Input("API")]
        public InArgument<string> APIArgument { get; set; }
        protected override void Execute(CodeActivityContext context)
        {
            // 获取服务
            IWorkflowContext workcontext = context.GetExtension<IWorkflowContext>();
            IOrganizationServiceFactory factory = context.GetExtension<IOrganizationServiceFactory>();
            ITracingService tracingService = context.GetExtension<ITracingService>();
            IOrganizationService serviceAdmin = factory.CreateOrganizationService(null);
            IOrganizationService service = factory.CreateOrganizationService(workcontext.UserId);

            var API = APIArgument.Get(context);
            tracingService.Trace("APINAME:" + API);

            // 获取URL并创建token
            GetURL(service);
            tracingService.Trace("BaseURL:" + BaseURL);
            CreateToken();
            tracingService.Trace("AccessToken:" + AccessToken);

            var userId = workcontext.UserId.ToString(); // 获取触发插件的用户ID

            // 使用 QueryExpression 查询数据
            var apiLogs = RetrieveApiLogs(serviceAdmin, userId);
            tracingService.Trace("apiLogs.Count:" + apiLogs.Count);

            if (apiLogs.Count > 0)
            {
                // 如果查询到符合条件的记录
                throw new InvalidPluginExecutionException("您已发起同步数据请求,正在执行中...");
            }
            else
            {
                // 如果没有找到记录，创建新的记录
                CreateMQ(serviceAdmin, API, userId, tracingService);
            }
        }

        private void GetURL(IOrganizationService service)
        {
            var queryExpression = new QueryExpression("onepdt_t_configuration")
            {
                ColumnSet = new ColumnSet("onepdt_value"),
                Criteria = new FilterExpression
                {
                    Conditions =
                {
                    new ConditionExpression("onepdt_name", ConditionOperator.Equal, "WechatBaseURL")
                }
                }
            };

            var response = service.RetrieveMultiple(queryExpression);

            if (response.Entities.Count > 0)
            {
                BaseURL = response.Entities[0].GetAttributeValue<string>("onepdt_value");
            }
            else
            {
                throw new InvalidPluginExecutionException("未找到[WechatBaseURL]参数配置");
            }
        }

        // 获取Token
        private void CreateToken()
        {
            var url = $"{BaseURL}/api/auth/createtoken";
            var requestBody = new
            {
                key = "228592F52FC3CB588B32AC2B1EC495D78AC4D1016C32F392F8258E0586C0D8EA"
            };

            try
            {
                using (var client = new HttpClient())
                {
                    var content = new StringContent(JsonConvert.SerializeObject(requestBody), Encoding.UTF8, "application/json");
                    var response = client.PostAsync(url, content).Result;

                    if (!response.IsSuccessStatusCode)
                    {
                        throw new InvalidPluginExecutionException($"请求Token失败: {response.ReasonPhrase}");
                    }

                    var responseData = response.Content.ReadAsStringAsync().Result;
                    dynamic data = JsonConvert.DeserializeObject(responseData);
                    AccessToken = data?.data;
                }
            }
            catch (Exception ex)
            {
                throw new InvalidPluginExecutionException($"请求Token失败: {ex.Message}", ex);
            }
        }

        // 查询epdt_t_api_log记录（使用QueryExpression）
        private List<Entity> RetrieveApiLogs(IOrganizationService service, string query)
        {
            var queryExpression = new QueryExpression("epdt_t_api_log")
            {
                ColumnSet = new ColumnSet("epdt_name", "epdt_api_body", "epdt_api_statuscode"),
                Criteria = new FilterExpression
                {
                    Conditions =
                {
                    new ConditionExpression("epdt_name", ConditionOperator.Equal, "UserOrgDataAll"), // api 名称
                    new ConditionExpression("epdt_api_statuscode", ConditionOperator.Equal, "进行中")
                }
                }
            };

            // 使用 Criteria 来定义复杂的查询条件
            var condition1 = new ConditionExpression("epdt_api_body", ConditionOperator.Equal, query); // 使用日志的用户ID进行匹配
            queryExpression.Criteria.AddCondition(condition1);

            // 执行查询
            var result = service.RetrieveMultiple(queryExpression);
            return result.Entities.ToList();
        }

        // 创建MQ记录
        private void CreateMQ(IOrganizationService service, string api, string userId, ITracingService tracingService)
        {
            var data = new Entity("epdt_t_api_log");
            data["epdt_name"] = api;
            data["epdt_api_name"] = api;
            data["epdt_api_description"] = api == "UserOrgDataAll" ? "销售架构数据同步" : "医院销售关系同步";
            data["epdt_api_body"] = userId;
            data["epdt_api_statuscode"] = "进行中";

            var apiLogId = string.Empty;
            try
            {
                apiLogId = service.Create(data).ToString();
                tracingService.Trace($"创建 epdt_t_api_log 记录成功，ID: {apiLogId}");
            }
            catch (Exception ex)
            {
                //throw new InvalidPluginExecutionException($"创建MQ时发生错误: {ex.Message}", ex);
            }

            try
            {
                tracingService.Trace($"发起获取数据请求：{apiLogId}");
                // 发起获取数据请求
                GetDataAll(service, apiLogId.ToString(), api);
                tracingService.Trace($"发起获取数据请求完成");
            }
            catch (Exception ex)
            {

                //throw new InvalidPluginExecutionException($"发起获取数据请求时发生错误: {ex.Message}", ex);
            }
        }

        // 请求获取数据
        private void GetDataAll(IOrganizationService service, string logId, string api)
        {
            Task.Run(async () =>
            {
                using (HttpClient client = new HttpClient())
                {
                    try
                    {
                        var url = $"{BaseURL}/Api/{api}?logid={logId}";
                        HttpRequestMessage req = new HttpRequestMessage(HttpMethod.Get, url);
                        req.Headers.Add("Authorization", "Bearer " + AccessToken);
                        var task = await client.SendAsync(req);
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                }
            });
        }
    }
}
