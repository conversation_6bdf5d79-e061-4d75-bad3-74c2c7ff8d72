
using Abt.Epdt.WebApis.Model;
using Abt.Epdt.WebApis.Util;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Abt.Epdt.WebApis.Command
{
    public class EmployeeCommand : OrgService
    {

        private readonly string baseurl = "https://qyapi.weixin.qq.com/cgi-bin";
        private string corpid;
        private string corpsecret;
        public EmployeeCommand(IMemoryCache memoryCache) : base(memoryCache)
        {
            //Wechat
            corpid = AppHelper.ReadAppSettings("Wechat", "corpid");
            corpsecret = AppHelper.ReadKeyVaultSettings("Corpsecret");
            //corpsecret = AppHelper.ReadAppSettings("Wechat", "corpsecret");
        }

        /// <summary>
        /// 获取员工主数据
        /// </summary>
        /// <param name="startmodifiedon">开始修改时间</param>
        /// <param name="endmodifiedon">结束修改时间</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResModel GetEmployees(string startmodifiedon, string endmodifiedon)
        {
            var res = new ResModel();
            try
            {


                if (!string.IsNullOrWhiteSpace(startmodifiedon) && !DateTime.TryParse(startmodifiedon, out DateTime start))
                {
                    throw new OnePDTException("【startmodifiedon】格式错误;例：yyyy-MM-dd HH:mm", "200003");

                }
                if (!string.IsNullOrWhiteSpace(endmodifiedon) && !DateTime.TryParse(endmodifiedon, out DateTime end))
                {
                    throw new OnePDTException("【endmodifiedon】格式错误;例：yyyy-MM-dd HH:mm", "200003");
                }

                res.flag = "S";
                res.code = "200000";
                res.message = "success";
                var list = new List<EmployeeModel>();
                //设置分页循环查询
                var pageindex = 0;
                //是否有更多数据
                var hasmore = true;

                while (hasmore)
                {
                    pageindex++;
                    var qe = new QueryExpression(EmployeeModel.getLogicName());
                    qe.ColumnSet.AllColumns = true;
                    if (!string.IsNullOrWhiteSpace(startmodifiedon))
                    {
                        qe.Criteria.AddCondition("modifiedon", ConditionOperator.OnOrAfter, startmodifiedon);
                    }
                    if (!string.IsNullOrWhiteSpace(endmodifiedon))
                    {
                        qe.Criteria.AddCondition("modifiedon", ConditionOperator.OnOrBefore, endmodifiedon);

                    }


                    qe.PageInfo = new PagingInfo()
                    {
                        PageNumber = pageindex,
                        Count = 5000,
                        ReturnTotalRecordCount = true
                    };

                    var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                    if (ec != null && ec.Entities.Count > 0)
                    {
                        hasmore = ec.MoreRecords;

                        foreach (var item in ec.Entities)
                        {
                            var model = new EmployeeModel();

                            if (item.GetAttributeValue<OptionSetValue>("statecode").Value == 1)
                            {
                                model.sign = "D";
                            }
                            else
                            {
                                var modifiedon = item.GetAttributeValue<DateTime>("modifiedon").AddHours(8).ToString("yyyy-MM-dd HH:mm");
                                var createdon = item.GetAttributeValue<DateTime>("createdon").AddHours(8).ToString("yyyy-MM-dd HH:mm");
                                if (modifiedon == createdon)
                                {
                                    model.sign = "I";
                                }
                                else
                                {
                                    model.sign = "U";
                                }
                            }


                            model.gender = item.GetAttributeValue<string>("onepdt_gender");
                            model.code = item.GetAttributeValue<string>("onepdt_code");
                            model.name = item.GetAttributeValue<string>("onepdt_name");
                            if (item.Contains("onepdt_buid"))
                                model.bu = item.GetAttributeValue<EntityReference>("onepdt_buid").Name;
                            model.code511 = item.GetAttributeValue<string>("onepdt_code511");
                            model.comment = item.GetAttributeValue<string>("onepdt_comment");
                            model.employmentstatus = item.GetAttributeValue<string>("onepdt_employment_status");
                            if (item.Contains("onepdt_hiredate"))
                                model.hiredate = item.GetAttributeValue<DateTime>("onepdt_hiredate").AddHours(8).ToString("yyyy-MM-dd HH:mm");
                            model.mail = item.GetAttributeValue<string>("onepdt_mail");
                            model.phone = item.GetAttributeValue<string>("onepdt_phone");
                            if (item.Contains("onepdt_position_id"))
                                model.position = item.GetAttributeValue<EntityReference>("onepdt_position_id").Name;
                            if (item.Contains("onepdt_role"))
                                model.rolename = item.GetAttributeValue<EntityReference>("onepdt_role").Name; ;// empty need to check
                            if (item.Contains("onepdt_terminaldate"))
                                model.terminaldate = item.GetAttributeValue<DateTime>("onepdt_terminaldate").AddHours(8).ToString("yyyy-MM-dd HH:mm");
                            if (item.Contains("onepdt_std_position_id"))
                                model.stdposition = item.GetAttributeValue<EntityReference>("onepdt_std_position_id").Name;
                            // model.EmploymentStatus = item.GetAttributeValue<OptionSetValue>("onepdt_employment_status").Value;
                            list.Add(model);
                        }
                    }
                    else
                    {
                        hasmore = false;
                    }
                }

                res.data = JsonConvert.SerializeObject(list);
                CommandHelper.CreateApiLog("获取员工主数据", $"startmodifiedon={startmodifiedon}&endmodifiedon={endmodifiedon}", JsonConvert.SerializeObject(res), "获取医生主数据", "成功", OrganizationServiceAdmin);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                res.flag = "E";
                res.message = ex.Message;
                //接口日志
                CommandHelper.CreateApiLog("获取员工主数据", $"startmodifiedon={startmodifiedon}&endmodifiedon={endmodifiedon}", JsonConvert.SerializeObject(res), "获取医生主数据", "失败", OrganizationServiceAdmin);

            }
            return res;
        }

        /// <summary>
        /// 企微-同步员工信息
        /// </summary>
        /// <param name="employeeid"></param>
        /// <returns></returns>
        public async Task<ResModel> WechatUser(EmployeeInfo employee)
        {
            var res = new ResModel();

            try
            {
                if (employee == null || string.IsNullOrWhiteSpace(employee.userid))
                {
                    throw new Exception("员工信息为空");
                }
                if (string.IsNullOrWhiteSpace(employee.status))
                {
                    throw new Exception("员工就职状态为空");
                }
                if (string.IsNullOrWhiteSpace(employee.department))
                {
                    throw new Exception("员工部门为空");
                }
                var accessToken = await GetAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    throw new Exception("Failed to get access token");
                }
                
                //获取CRM和EP部门成员
                //var userlist_crm = await GetUserlistAsync(accessToken, 7);
                //var userlist_ep = await GetUserlistAsync(accessToken, 8);
                //var userlist_zc = await GetUserlistAsync(accessToken, 15);
                //var userlist = userlist_crm.Union(userlist_ep).Union(userlist_zc).ToList();

                //var employee = OrganizationServiceAdmin.Retrieve("onepdt_t_employee_basic_mdata", new Guid(employeeid), new ColumnSet(true));
                //校验员工信息
                //CheckEmployeeInfo(employee);

                var status = employee.status;
                var bu = employee.department;
                var userid = employee.userid;
                //var isexist = userlist.Any(b => b.userid == userid);//企微是否已存在员工
                //查询是否存在
                var isexist = await GetUserAsync(accessToken, employee.userid);

                if (status == "在职")
                {
                    var department = new List<int>();
                    if (bu == "CRM")
                    {
                        department.Add(7);
                    }
                    else if (bu == "AF")
                    {
                        department.Add(8);
                    }
                    else
                    {
                        department.Add(15);
                    }
                    var obj = new
                    {
                        userid = userid,
                        name = employee.name,
                        mobile = employee.mobile,
                        gender = employee.gender == "男" ? "1" : "2",
                        email = employee.email,
                        position = employee.position,
                        department = department
                    };
                    if (isexist)
                    {
                        await UpdateUserAsync(accessToken, obj);
                    }
                    else
                    {
                        await CreateUserAsync(accessToken, obj);
                    }
                }
                else if (status == "离职")
                {
                    if (isexist)
                    {
                        var batchdelete = new { useridlist = new string[] { userid } };
                        await BatchDeleteUserAsync(accessToken, batchdelete);
                    }
                }
                //接口日志
                CommandHelper.CreateApiLog("企微-同步员工数据", JsonConvert.SerializeObject(employee), JsonConvert.SerializeObject(res), "企微-同步员工数据", "成功", OrganizationServiceAdmin);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                res.flag = "E";
                res.message = ex.Message;
                //接口日志
                CommandHelper.CreateApiLog("企微-同步员工数据", JsonConvert.SerializeObject(employee), JsonConvert.SerializeObject(res), "企微-同步员工数据", "失败", OrganizationServiceAdmin);
            }
            return res;

        }

        /// <summary>
        /// 校验员工信息
        /// </summary>
        /// <param name="employee"></param>
        public void CheckEmployeeInfo(Entity employee)
        {
            if (!employee.Contains("onepdt_code"))
            {
                throw new Exception("员工编码为空");
            }
            if (!employee.Contains("onepdt_employment_status"))
            {
                throw new Exception("员工就职状态为空");
            }
            if (!employee.Contains("onepdt_buid"))
            {
                throw new Exception("员工部门为空");
            }
        }

        /// <summary>
        /// 获取AccessToken
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetAccessTokenAsync()
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var response = await client.GetAsync($"{baseurl}/gettoken?corpid={corpid}&corpsecret={corpsecret}");
                    if (response.IsSuccessStatusCode)
                    {
                        var jsonResponse = await response.Content.ReadAsStringAsync();
                        var token = JsonConvert.DeserializeObject<dynamic>(jsonResponse);
                        CommandHelper.CreateApiLog("企微-获取Token", $"corpid={corpid}&corpsecret={corpsecret}", jsonResponse, "Get", "成功", OrganizationServiceAdmin, $"{baseurl}/gettoken");
                        return token.access_token;
                    }
                    return null;
                }
            }
            catch (Exception ex)
            {

                throw;
            }
        }

        /// <summary>
        /// 获取部门成员
        /// </summary>
        /// <returns></returns>
        public async Task<List<dynamic>> GetUserlistAsync(string token, int department_id)
        {
            using (var client = new HttpClient())
            {
                var response = await client.GetAsync($"{baseurl}/user/simplelist?access_token={token}&department_id={department_id}");
                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = await response.Content.ReadAsStringAsync();
                    var res = JsonConvert.DeserializeObject<dynamic>(jsonResponse);
                    if (res.errcode != 0)
                    {
                        CommandHelper.CreateApiLog("企微-获取部门成员", $"access_token={token}&department_id={department_id}", jsonResponse, "Get", "失败", OrganizationServiceAdmin, $"{baseurl}/user/simplelist");
                        throw new Exception((string)res.errmsg);
                    }
                    CommandHelper.CreateApiLog("企微-获取部门成员", $"access_token={token}&department_id={department_id}", jsonResponse, "Get", "成功", OrganizationServiceAdmin, $"{baseurl}/user/simplelist");
                    return res.userlist.ToObject<List<dynamic>>();
                }
                return null;
            }
        }

        /// <summary>
        /// 读取成员
        /// </summary>
        /// <returns></returns>
        public async Task<bool> GetUserAsync(string token, string userid)
        {
            using (var client = new HttpClient())
            {
                var response = await client.GetAsync($"{baseurl}/user/get?access_token={token}&userid={userid}");
                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = await response.Content.ReadAsStringAsync();
                    var res = JsonConvert.DeserializeObject<dynamic>(jsonResponse);
                    CommandHelper.CreateApiLog("企微-读取成员", $"access_token={token}&userid={userid}", jsonResponse, "Get", res.errcode == 0 ? "成功" : "失败", OrganizationServiceAdmin, $"{baseurl}/user/userid");
                    return res.errcode == 0;
                }
                throw new Exception("企微-读取成员接口失败");
            }
        }

        /// <summary>
        /// 创建成员
        /// </summary>
        /// <returns></returns>
        public async Task CreateUserAsync(string token, dynamic user)
        {
            using (var client = new HttpClient())
            {
                // 创建请求内容
                var content = new StringContent(JsonConvert.SerializeObject(user), Encoding.UTF8, "application/json");
                var response = await client.PostAsync($"{baseurl}/user/create?access_token={token}", content);
                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = await response.Content.ReadAsStringAsync();
                    var res = JsonConvert.DeserializeObject<dynamic>(jsonResponse);
                    if (res.errcode != 0)
                    {
                        CommandHelper.CreateApiLog("企微-创建成员", JsonConvert.SerializeObject(user), jsonResponse, "Post", "失败", OrganizationServiceAdmin, $"{baseurl}/user/create");
                        throw new Exception((string)res.errmsg);
                    }
                    CommandHelper.CreateApiLog("企微-创建成员", JsonConvert.SerializeObject(user), jsonResponse, "Post", "成功", OrganizationServiceAdmin, $"{baseurl}/user/create");
                }
            }
        }

        /// <summary>
        /// 更新成员
        /// </summary>
        /// <returns></returns>
        public async Task UpdateUserAsync(string token, dynamic user)
        {
            using (var client = new HttpClient())
            {
                // 创建请求内容
                var content = new StringContent(JsonConvert.SerializeObject(user), Encoding.UTF8, "application/json");
                var response = await client.PostAsync($"{baseurl}/user/update?access_token={token}", content);
                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = await response.Content.ReadAsStringAsync();
                    var res = JsonConvert.DeserializeObject<dynamic>(jsonResponse);
                    if (res.errcode != 0)
                    {
                        CommandHelper.CreateApiLog("企微-更新成员", JsonConvert.SerializeObject(user), jsonResponse, "Post", "失败", OrganizationServiceAdmin, $"{baseurl}/user/update");
                        throw new Exception((string)res.errmsg);
                    }
                    CommandHelper.CreateApiLog("企微-更新成员", JsonConvert.SerializeObject(user), jsonResponse, "Post", "成功", OrganizationServiceAdmin, $"{baseurl}/user/update");
                }
            }
        }

        /// <summary>
        /// 批量删除成员
        /// </summary>
        /// <returns></returns>
        public async Task BatchDeleteUserAsync(string token, dynamic useridlist)
        {
            using (var client = new HttpClient())
            {
                // 创建请求内容
                var content = new StringContent(JsonConvert.SerializeObject(useridlist), Encoding.UTF8, "application/json");
                var response = await client.PostAsync($"{baseurl}/user/batchdelete?access_token={token}", content);
                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = await response.Content.ReadAsStringAsync();
                    var res = JsonConvert.DeserializeObject<dynamic>(jsonResponse);
                    if (res.errcode != 0)
                    {
                        CommandHelper.CreateApiLog("企微-批量删除成员", JsonConvert.SerializeObject(useridlist), jsonResponse, "Post", "失败", OrganizationServiceAdmin, $"{baseurl}/user/batchdelete");
                        throw new Exception((string)res.errmsg);
                    }
                    CommandHelper.CreateApiLog("企微-批量删除成员", JsonConvert.SerializeObject(useridlist), jsonResponse, "Post", "成功", OrganizationServiceAdmin, $"{baseurl}/user/batchdelete");
                }
            }
        }
    }
}


