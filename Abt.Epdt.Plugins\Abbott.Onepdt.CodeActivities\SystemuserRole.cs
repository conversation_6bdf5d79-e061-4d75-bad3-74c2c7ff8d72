﻿using Microsoft.Xrm.Sdk.Workflow;
using Microsoft.Xrm.Sdk;
using System;
using System.Activities;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Xrm.Sdk.Query;

namespace Abbott.Onepdt.CodeActivities
{
    public class SystemuserRole : CodeActivity
    {
        protected override void Execute(CodeActivityContext context)
        {
            // 获取服务
            IWorkflowContext workcontext = context.GetExtension<IWorkflowContext>();
            IOrganizationServiceFactory factory = context.GetExtension<IOrganizationServiceFactory>();
            ITracingService tracingService = context.GetExtension<ITracingService>();
            IOrganizationService serviceAdmin = factory.CreateOrganizationService(null);
            IOrganizationService service = factory.CreateOrganizationService(workcontext.UserId);

            GetSystemUsersWithRoles(serviceAdmin);
        }

        public void GetSystemUsersWithRoles(IOrganizationService _service)
        {
            // 1. 查询所有系统用户
            QueryExpression querySystemUsers = new QueryExpression("systemuser")
            {
                ColumnSet = new ColumnSet("systemuserid", "fullname", "internalemailaddress")
            };

            EntityCollection users = _service.RetrieveMultiple(querySystemUsers);

            foreach (var user in users.Entities)
            {
                Guid userId = user.Id;
                string userName = user.GetAttributeValue<string>("fullname");
                string userEmail = user.GetAttributeValue<string>("internalemailaddress");

                // 使用 HashSet 去重
                HashSet<string> roles = new HashSet<string>();

                // 2. 查询直接分配给用户的角色
                QueryExpression queryDirectRoles = new QueryExpression("systemuserroles")
                {
                    ColumnSet = new ColumnSet("roleid")
                };
                queryDirectRoles.Criteria.AddCondition("systemuserid", ConditionOperator.Equal, userId);
                queryDirectRoles.LinkEntities.Add(new LinkEntity("systemuserroles", "role", "roleid", "roleid", JoinOperator.Inner)
                {
                    Columns = new ColumnSet("name"),
                    EntityAlias = "role"
                });

                EntityCollection directRoles = _service.RetrieveMultiple(queryDirectRoles);

                foreach (var role in directRoles.Entities)
                {
                    string roleName = role.GetAttributeValue<AliasedValue>("role.name")?.Value as string;
                    if (!string.IsNullOrEmpty(roleName))
                    {
                        roles.Add(roleName);
                    }
                }

                // 3. 查询用户所属团队的安全角色
                QueryExpression queryTeamRoles = new QueryExpression("teammembership")
                {
                    ColumnSet = new ColumnSet("teamid")
                };
                queryTeamRoles.Criteria.AddCondition("systemuserid", ConditionOperator.Equal, userId);
                queryTeamRoles.LinkEntities.Add(new LinkEntity("teammembership", "teamroles", "teamid", "teamid", JoinOperator.Inner)
                {
                    LinkEntities =
                {
                    new LinkEntity("teamroles", "role", "roleid", "roleid", JoinOperator.Inner)
                    {
                        Columns = new ColumnSet("name"),
                        EntityAlias = "teamrole"
                    }
                }
                });

                EntityCollection teamRoles = _service.RetrieveMultiple(queryTeamRoles);

                foreach (var role in teamRoles.Entities)
                {
                    string roleName = role.GetAttributeValue<AliasedValue>("teamrole.name")?.Value as string;
                    if (!string.IsNullOrEmpty(roleName))
                    {
                        roles.Add(roleName);
                    }
                }

                // 4. 输出角色，去重后用英文逗号连接
                string rolesList = string.Join(",", roles);

                //判断只有EDPT角色的用户需要同步
                if (rolesList.Contains("EPDT"))
                {
                    //判断用户是否存在这个表
                    string sql2 = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
  <entity name='epdt_t_role_management'>
    <attribute name='epdt_t_role_managementid' />
    <attribute name='epdt_name' />
    <attribute name='createdon' />
    <attribute name='epdt_email' />
    <attribute name='epdt_role_permissions' />
    <attribute name='epdt_role' />
    <attribute name='epdt_user_name' />
    <attribute name='epdt_user_code' />
    <attribute name='epdt_linkeduser' />
    <order attribute='epdt_name' descending='false' />
    <filter type='and'>
      <condition attribute='epdt_email' operator='eq' value='{userEmail}' />
    </filter>
  </entity>
</fetch>";

                    var rolemanagelist = _service.RetrieveMultiple(new FetchExpression(sql2));
                    var trackingquery = new Entity("epdt_t_role_management");
                    trackingquery["epdt_name"] = "用户系统角色分配";
                    trackingquery["epdt_user_code"] = userEmail;
                    trackingquery["epdt_user_name"] = userName;
                    trackingquery["epdt_role"] = rolesList;
                    trackingquery["epdt_role_permissions"] = rolesList;
                    trackingquery["epdt_email"] = userEmail;
                    trackingquery["epdt_linkeduser"] = new EntityReference("systemuser", userId);

                    if (rolemanagelist.Entities.Count > 0)
                    {
                        var first = rolemanagelist.Entities.FirstOrDefault();
                        trackingquery.Id = first.Id;
                        _service.Update(trackingquery);
                    }
                    else
                    {
                        _service.Create(trackingquery);
                    }
                }


            }
        }
    }
}
