"use strict";
var Abt = window.Abt || {};
window.Abt = Abt;
Abt.epdt_t_special_business_scenario_Form = Abt.epdt_t_special_business_scenario_Form || {};

(function () {

  this.test = function (selcids) {
    Xrm.Navigation.openAlertDialog("test!!" + selcids);
  }

  this.onCustomExportExcel = function (formContext) {
    debugger;
    var formId = formContext.ui.formSelector.getCurrentItem().getId();
    //判断是否在导入导出的时间范围
    var starttime = formContext.getAttribute("epdt_export_start_time").getValue();
    var endtime = formContext.getAttribute("epdt_export_end_time").getValue();
    var exportid = formContext.getAttribute("epdt_export_person").getValue();
    var userid = Xrm.Utility.getGlobalContext().getUserId();
    if (date.isDuringDate(starttime, endtime)  && exportid.length>0 && exportid[0].id == userid) {

      var confirmStrings = { title: "隐私数据导出", text: "涉及患者隐私数据，请妥善保管，如果继续请确认！" };
      var confirmOptions = { height: 200, width: 300 };
      Xrm.Navigation.openConfirmDialog(confirmStrings, confirmOptions).then(function (success) {
        if (success.confirmed) {
          var pageInput = {
            pageType: "entitylist",
            entityName: "epdt_t_device_application",
            viewId: "e42a1805-974b-ed11-bba0-0017fa04bd1d",

          };
          var options = {
            target: 2,
            width: 900,
            height: 600,
            position: 1,
            title: "隐私数据导出"
          };
          Xrm.Navigation.navigateTo(pageInput, options).then(
            function success() {
              // Run code on success

            },
            function error() {
              // Handle errors
            }
          );
        }


      });
    }
    else {
      Xrm.Navigation.openAlertDialog("导出需要在允许的时间范围内进行操作。");
    }
  }

  this.onCustomExportExcelGrid = function () {
    debugger;
    var now = new Date().toISOString();
    var user = Xrm.Utility.getGlobalContext().getUserId().replace("{", "").replace("}", "");
    // and _epdt_export_person_value eq '" + user + "' 
    //"?$filter=epdt_export_start_time le '" + now + "' and epdt_export_end_time ge '" + now + "' &$top=1"
    Xrm.WebApi.retrieveMultipleRecords("epdt_t_special_business_scenario", "?$filter=epdt_export_start_time le '" + now + "' and epdt_export_end_time ge '" + now + "' and _epdt_export_person_value eq '" + user + "' &$top=1").then(
      function success(result) {
        console.log(result);
        if (result.entities.length > 0) {
          var confirmStrings = { title: "隐私数据导出", text: "涉及患者隐私数据，请妥善保管，如果继续请确认！" };
          var confirmOptions = { height: 200, width: 300 };
          Xrm.Navigation.openConfirmDialog(confirmStrings, confirmOptions).then(function (success) {
            if (success.confirmed) {
              var pageInput = {
                pageType: "entitylist",
                entityName: "epdt_t_device_application",
                viewId: "e42a1805-974b-ed11-bba0-0017fa04bd1d",

              };
              var options = {
                target: 2,
                width: 900,
                height: 600,
                position: 1,
                title: "隐私数据导出"
              };
              Xrm.Navigation.navigateTo(pageInput, options).then(
                function success() {
                  // Run code on success

                },
                function error() {
                  // Handle errors
                }
              );
            }
          });

        }
        else {
          Xrm.Navigation.openAlertDialog("导出需要在允许的时间范围内进行操作。");
        }




      },
      function (error) {
        // Xrm.Navigation.openErrorDialog({ message: `错误:${error.message}，请联系系统管理员!` });
      }
    );
  }

}).call(Abt.epdt_t_special_business_scenario_Form);

var date = {
  isDuringDate: function (beginDateStr, endDateStr) {
    var curDate = new Date(),
      beginDate = new Date(beginDateStr),
      endDate = new Date(endDateStr);
    if (curDate >= beginDate && curDate <= endDate) {
      return true;
    }
    return false;
  }
}


Date.prototype.format = function (format) {
  /*
   * eg:format="YYYY-MM-dd hh:mm:ss";

   */
  var o = {
    "M+": this.getMonth() + 1, // month
    "d+": this.getDate(), // day
    "h+": this.getHours(), // hour
    "m+": this.getMinutes(), // minute
    "s+": this.getSeconds(), // second
    "q+": Math.floor((this.getMonth() + 3) / 3), // quarter
    "S": this.getMilliseconds()
    // millisecond
  }
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (this.getFullYear() + "")
      .substr(4 - RegExp.$1.length));
  }
  for (var k in o) {
    if (new RegExp("(" + k + ")").test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k]
        : ("00" + o[k]).substr(("" + o[k]).length));
    }
  }
  return format;
}