﻿using Abt.Epdt.WebApis.Model;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System;
using Abt.Epdt.WebApis.Command;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Caching.Memory;

namespace Abt.Epdt.WebApis.Controller
{
    [ApiController]
    [Route("api/hcp")]
    [Authorize]
    public class HcpController : ControllerBase
    {
        private readonly IMemoryCache _memoryCache;
        private readonly HcpCommand com;

        public HcpController(IMemoryCache memoryCache)
        {
            _memoryCache = memoryCache;
            com = new HcpCommand(_memoryCache);
        }
        /// <summary>
        /// 获取医生主数据
        /// </summary>
        /// <param name="startmodifiedon">开始修改时间</param>
        /// <param name="endmodifiedon">结束修改时间</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [Authorize(AuthenticationSchemes = "JwtBearer")]
        [HttpGet]
        [Route("GetHcps")]
        public ResModel GetHcps(string startmodifiedon, string endmodifiedon)
        {
            return com.GetHcps(startmodifiedon, endmodifiedon);
        }
    }
}
