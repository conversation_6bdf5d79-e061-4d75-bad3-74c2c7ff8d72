﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.Onepdt.Plugins.Models
{

    public class ActionResult {
        public string Code { get; set; }
        public string Message { get; set; }
        public List<CardTypeModel> Data { get; set; }

     }


    public class ImplantModel
    {
        public string ResultType { get; set; }
        public string SN { get; set; }
        public string model { get; set; }
        public bool IsExpired { get; set; }
        public string Message { get; set; }

    }

    public class retrunMAT
    {

        public string MAT { get; set; }


        public string CardType { get; set; }
    }

        public class CardTypeModel
    {
        /// <summary>
        /// 主机产品植入ID
        /// </summary>
        public string onepdt_t_operation_implantid { get; set; }

        /// <summary>
        /// 识别卡号
        /// </summary>
        public string CardNo { get; set; }
        /// <summary>
        /// 手术编号
        /// </summary>
        public string Operationnumber { get; set; }
        /// <summary>
        /// 患者姓名
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 患者性别
        /// </summary>
        public string Gender { get; set; }
        /// <summary>
        /// 植入日期
        /// </summary>
        public string ImpDate { get; set; }
        /// <summary>
        /// 植入医生1
        /// </summary>
        public string Physician1 { get; set; }
        /// <summary>
        /// 植入医生2
        /// </summary>
        public string Physician2 { get; set; }
        /// <summary>
        /// 医院
        /// </summary>
        public string Hospital { get; set; }
        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalCode { get; set; }
        /// <summary>
        /// 医院地址
        /// </summary>
        public string HospitalAdd { get; set; }
        /// <summary>
        /// 医院电话
        /// </summary>
        /// 

        public string HospitalTel { get; set; }
        /// <summary>
        /// 卡类型（A/B/C）
        /// </summary>
        public string CardType { get; set; }

        /// <summary>
        /// MAT
        /// </summary>
        public string MAT { get; set; }
        /// <summary>
        /// 产品型号
        /// </summary>
        public string ModelNo { get; set; }
        /// <summary>
        /// 产品序列号
        /// </summary>
        public string SN { get; set; }
        /// <summary>
        /// 担保日期
        /// </summary>
        public string WarrantyPeriod { get; set; }
        /// <summary>
        /// 制造商
        /// </summary>
        public string Manufacturer { get; set; }
        /// <summary>
        /// 所选起搏模式
        /// </summary>
        public string SelectedMode { get; set; }
        /// <summary>
        /// 主要起搏模式
        /// </summary>
        public string MainMode { get; set; }
        /// <summary>
        /// 扫描强度
        /// </summary>
        public string ScanIntensity { get; set; }
        /// <summary>
        /// 是否显示核磁标识
        /// </summary>
        public string isShowScan { get; set; }

        /// <summary>
        /// 配件列表
        /// </summary>
        public List<Accessory> Accessories { get; set; }

        public class Accessory
        {
            /// <summary>
            /// 产品型号
            /// </summary>
            public string ModelNo { get; set; }
            /// <summary>
            /// 产品序列号
            /// </summary>
            public string SN { get; set; }
            /// <summary>
            /// 植入日期
            /// </summary>
            public string ImpDate { get; set; }
            /// <summary>
            /// 制造商
            /// </summary>
            public string Manufacturer { get; set; }
        }


    }
}