﻿
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.Onepdt.Plugins
{
    public class OperationPreValidation : IPlugin
    {
        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService service = serviceFactory.CreateOrganizationService(null); // 权限
            ITracingService tracingService = (ITracingService)serviceProvider.GetService(typeof(ITracingService));
            try
            {
                // 已弃用
                if (context.InputParameters.Contains("Target") && context.InputParameters["Target"] is Entity)
                {
                    Entity targetEntity = (Entity)context.InputParameters["Target"];

                    Guid productModelId = targetEntity.GetAttributeValue<EntityReference>("onepdt_product_mdata").Id;
                    Entity productModel = service.Retrieve("onepdt_t_product_mdata", productModelId, new ColumnSet(true));
                    string productModelName = productModel["onepdt_name"].ToString();
                    var sn = targetEntity["onepdt_name"];
                    var query = new QueryExpression("onepdt_t_operation_implant");
                    query.ColumnSet.AllColumns = true;
                    query.Criteria.AddCondition("onepdt_product_mdata", ConditionOperator.Equal, productModelId);
                    query.Criteria.AddCondition("onepdt_name", ConditionOperator.Equal, sn);
                    query.Criteria.AddCondition("statecode", ConditionOperator.Equal, "Active");

                    var results = service.RetrieveMultiple(query);

                    // 产品已使用
                    if (results.Entities.Any())
                    {
                        throw new InvalidPluginExecutionException($"该产品已有使用记录！(型号：{productModelName}；序列号：{sn})");
                    }


                    // 检查非大陆产品
                    if (targetEntity.Contains("onepdt_is_nonmainland") && (bool)targetEntity["onepdt_is_nonmainland"])
                    {

                        if (string.IsNullOrEmpty(productModelName))
                        {
                            throw new InvalidPluginExecutionException($"产品信息不完整!(型号：{productModelName}；序列号：{sn})");
                        }

                        var query_nonmainland_management = new QueryExpression("onepdt_t_nonmainland_management");
                        query_nonmainland_management.ColumnSet.AllColumns = true;
                        query_nonmainland_management.Criteria.AddCondition("onepdt_sn", ConditionOperator.Equal, sn);
                        query_nonmainland_management.Criteria.AddCondition("onepdt_name", ConditionOperator.Equal, productModelName);

                        // Execute the query
                        var results_nonmainland_management = service.RetrieveMultiple(query_nonmainland_management);

                        // If there are no results, it means the nonmainland entity does not exist
                        if (!results_nonmainland_management.Entities.Any())
                        {
                            throw new InvalidPluginExecutionException($"该产品信息不存在!(型号：{productModelName}；序列号：{sn})");
                        }
                    }
                }
            }



            catch (Exception ex)
            {
                tracingService.Trace(ex.Message + ex.StackTrace + ex.StackTrace);
                throw;
            }
        }
    }
}
