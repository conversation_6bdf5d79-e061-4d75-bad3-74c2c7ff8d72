﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>ExeLibrary</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RuntimeIdentifier>browser-wasm</RuntimeIdentifier>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <WasmMainJSPath>main.js</WasmMainJSPath>
    <BlazorEnableCompression>true</BlazorEnableCompression>
    <PublishTrimmed>true</PublishTrimmed>
    <TrimMode>link</TrimMode>
    <!-- 👇 New extension for ICU files -->
    <NewIcuFileExtension>.icu</NewIcuFileExtension>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="SkiaSharp" Version="2.88.8" />
    <PackageReference Include="SkiaSharp.NativeAssets.WebAssembly" Version="2.88.8" />
    <PackageReference Include="SkiaSharp.Views.Blazor" Version="2.88.8" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.8" />
    <PackageReference Include="ZXing.Net" Version="0.16.9" />
    <PackageReference Include="ZXing.Net.Bindings.SkiaSharp" Version="0.16.14" />
  </ItemGroup>


  <!-- 👇 After the application is build/published -->
  <Target Name="RenameIcuToAppBundle" AfterTargets="WasmBuildApp;WasmNestedPublishApp">
    <ItemGroup>
      <!-- 👇 Find all ICU files in AppBundle -->
      <IcuFiles Include="$(OutputPath)\**\*.dat" />
    </ItemGroup>

    <Move SourceFiles="@(IcuFiles)" OverwriteReadOnlyFiles="true" DestinationFiles="%(RelativeDir)%(Filename)$(NewIcuFileExtension)" />
    <!-- 👇 Change their extension to 'NewIcuFileExtension' -->
    <!-- <Move SourceFiles="@(IcuFiles)" OverwriteReadOnlyFiles="true" DestinationFiles="%(RelativeDir)%(Filename)$(NewIcuFileExtension)" /> -->
  </Target>
</Project>
