{"openapi": "3.0.1", "info": {"title": "雅培API", "version": "v1"}, "paths": {"/api/epdt/GetUserInfo": {"post": {"tags": ["App"], "summary": "获取用户信息", "requestBody": {"description": "用户id", "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"id": {"type": "string"}}}, "encoding": {"id": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/epdt/LoginAndGetRole": {"get": {"tags": ["App"], "summary": "检验用户邮箱并获取角色", "parameters": [{"name": "email", "in": "query", "description": "邮箱", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/GetTodoList": {"get": {"tags": ["App"], "summary": "获取待办列表", "parameters": [{"name": "email", "in": "query", "description": "邮箱", "schema": {"type": "string"}}, {"name": "type", "in": "query", "description": "待办类型", "schema": {"type": "integer", "format": "int32"}}, {"name": "pagesize", "in": "query", "description": "分页大小", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageindex", "in": "query", "description": "分页索引", "schema": {"type": "integer", "format": "int32"}}, {"name": "searchText", "in": "query", "description": "搜索内容", "schema": {"type": "string"}}, {"name": "implantType", "in": "query", "description": "植入类型", "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "状态", "schema": {"type": "string"}}, {"name": "hospital", "in": "query", "description": "医院", "schema": {"type": "string"}}, {"name": "starttime", "in": "query", "description": "植入开始时间", "schema": {"type": "string"}}, {"name": "endtime", "in": "query", "description": "植入结束时间", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/GetOnepdtConfig": {"get": {"tags": ["App"], "summary": "获取跟台配置", "parameters": [{"name": "tablename", "in": "query", "description": "配置表名", "schema": {"type": "string"}}, {"name": "fieldname", "in": "query", "description": "配置字段名", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/GetDapplications": {"get": {"tags": ["App"], "summary": "获取电子保卡", "parameters": [{"name": "email", "in": "query", "schema": {"type": "string"}}, {"name": "operationid", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/GetHospitals": {"get": {"tags": ["App"], "summary": "获取医院信息", "parameters": [{"name": "email", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/GetHCPs": {"get": {"tags": ["App"], "summary": "获取医生信息", "parameters": [{"name": "email", "in": "query", "schema": {"type": "string"}}, {"name": "isout", "in": "query", "description": "是否外部医生", "schema": {"type": "boolean"}}, {"name": "hospitalcode", "in": "query", "description": "医院编码", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/EditOperation": {"post": {"tags": ["App"], "summary": "跟台数据编辑", "requestBody": {"description": "跟台数据Model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationEditData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OperationEditData"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OperationEditData"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/epdt/GetSAPProduct": {"get": {"tags": ["App"], "summary": "根据产品序列号SAP产品", "parameters": [{"name": "name", "in": "query", "description": "序列号", "schema": {"type": "string"}}, {"name": "isNonMainlandProduct", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/GetOnePDTProduct": {"get": {"tags": ["App"], "summary": "根据产品序列号获取产品", "parameters": [{"name": "name", "in": "query", "description": "序列号", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/GetOperation": {"get": {"tags": ["App"], "summary": "获取跟台数据详情", "parameters": [{"name": "operationid", "in": "query", "description": "跟台数据id", "schema": {"type": "string"}}, {"name": "email", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/GetPhoto": {"get": {"tags": ["App"], "summary": "获取跟台图片", "parameters": [{"name": "id", "in": "query", "description": "跟台图片id", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/SubmitOperation": {"get": {"tags": ["App"], "summary": "提交跟台", "parameters": [{"name": "operationid", "in": "query", "description": "跟台数据id", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/Delete": {"get": {"tags": ["App"], "summary": "删除", "parameters": [{"name": "entityname", "in": "query", "description": "实体名", "schema": {"type": "string"}}, {"name": "entityid", "in": "query", "description": "实体id", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/BatchDelete": {"post": {"tags": ["App"], "summary": "批量删除", "requestBody": {"description": "删除对象", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchDeleteModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BatchDeleteModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BatchDeleteModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/epdt/GetCardTypes": {"get": {"tags": ["App"], "summary": "预览保卡", "parameters": [{"name": "operationid", "in": "query", "description": "跟台数据id", "schema": {"type": "string"}}, {"name": "IsPreview", "in": "query", "description": "是否预览", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/GetOperationQuery": {"get": {"tags": ["App"], "summary": "跟台查询", "parameters": [{"name": "roleid", "in": "query", "description": "角色id", "schema": {"type": "string"}}, {"name": "proxyemail", "in": "query", "description": "代理邮箱", "schema": {"type": "string"}}, {"name": "email", "in": "query", "description": "邮箱", "schema": {"type": "string"}}, {"name": "pagesize", "in": "query", "description": "分页大小", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageindex", "in": "query", "description": "分页索引", "schema": {"type": "integer", "format": "int32"}}, {"name": "searchText", "in": "query", "description": "搜索框", "schema": {"type": "string"}}, {"name": "productname", "in": "query", "description": "主机", "schema": {"type": "string"}}, {"name": "submittername", "in": "query", "description": "填报人", "schema": {"type": "string"}}, {"name": "province", "in": "query", "description": "省份", "schema": {"type": "string"}}, {"name": "implantType", "in": "query", "description": "植入类型", "schema": {"type": "string"}}, {"name": "starttime", "in": "query", "description": "植入时间起", "schema": {"type": "string"}}, {"name": "endtime", "in": "query", "description": "植入时间止", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/upload": {"post": {"tags": ["App"], "summary": "上传图片", "requestBody": {"description": "跟台id", "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"operationid": {"type": "string"}, "photoid": {"type": "string"}, "classify": {"type": "string"}, "file": {"type": "string", "format": "binary"}}}, "encoding": {"operationid": {"style": "form"}, "photoid": {"style": "form"}, "classify": {"style": "form"}, "file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/epdt/GetConfigFile": {"get": {"tags": ["App"], "summary": "获取参数文件", "parameters": [{"name": "id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/GetUserManual": {"get": {"tags": ["App"], "summary": "获取用户使用说明", "responses": {"200": {"description": "OK"}}}}, "/api/epdt/CkeckOperation": {"post": {"tags": ["App"], "summary": "跟台数据校验", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationEditData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OperationEditData"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OperationEditData"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/epdt/SwitchRole": {"get": {"tags": ["App"], "summary": "切换角色", "parameters": [{"name": "userid", "in": "query", "description": "员工id", "schema": {"type": "string"}}, {"name": "roleid", "in": "query", "description": "角色id", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/epdt/GetHistoryOperation": {"get": {"tags": ["App"], "summary": "查询历史跟台", "responses": {"200": {"description": "OK"}}}}, "/api/auth/token": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "SFE获取Token", "parameters": [{"name": "appid", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/auth/ssotoken": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取Token", "responses": {"200": {"description": "OK"}}}}, "/api/auth/login": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "sign<PERSON><PERSON>l", "in": "query", "schema": {"type": "string"}}, {"name": "errorurl", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/auth/login/skip": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "sign<PERSON><PERSON>l", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "errorurl", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/auth/login/success": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "sign<PERSON><PERSON>l", "in": "query", "schema": {"type": "string"}}, {"name": "errorurl", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/employee/GetEmployees": {"get": {"tags": ["Employee"], "summary": "获取员工主数据", "parameters": [{"name": "startmodifiedon", "in": "query", "description": "开始修改时间", "schema": {"type": "string"}}, {"name": "endmodifiedon", "in": "query", "description": "结束修改时间", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}}}}}}, "/api/employee/WechatUser": {"post": {"tags": ["Employee"], "summary": "企微-同步员工信息", "requestBody": {"description": "员工信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeInfo"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmployeeInfo"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmployeeInfo"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}}}}}}, "/api/hcp/GetHcps": {"get": {"tags": ["Hcp"], "summary": "获取医生主数据", "parameters": [{"name": "startmodifiedon", "in": "query", "description": "开始修改时间", "schema": {"type": "string"}}, {"name": "endmodifiedon", "in": "query", "description": "结束修改时间", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}}}}}}, "/api/policy/GetPolicies": {"get": {"tags": ["Policy"], "summary": "获取最新政策", "parameters": [{"name": "applicationID", "in": "query", "description": "应用ID", "schema": {"type": "string"}}, {"name": "policyID", "in": "query", "description": "政策ID", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}}}}}}, "/api/policy/Consent": {"post": {"tags": ["Policy"], "summary": "用户确认", "requestBody": {"description": "用户确认参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConsentModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConsentModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConsentModel"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}}}}}}, "/api/policy/CheckPolicy": {"get": {"tags": ["Policy"], "summary": "用户检查", "parameters": [{"name": "type", "in": "query", "description": "用户类别", "schema": {"type": "integer", "format": "int32"}}, {"name": "applicationID", "in": "query", "description": "应用ID", "schema": {"type": "string"}}, {"name": "id", "in": "query", "description": "用户id", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}}}}}}, "/api/policy/RevokePolicy": {"get": {"tags": ["Policy"], "summary": "用户授权撤销", "parameters": [{"name": "type", "in": "query", "description": "用户类别", "schema": {"type": "integer", "format": "int32"}}, {"name": "applicationID", "in": "query", "description": "应用id", "schema": {"type": "string"}}, {"name": "userID", "in": "query", "description": "用户id", "schema": {"type": "string"}}, {"name": "policyID", "in": "query", "description": "政策id", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}}}}}}, "/api/policy/ConsentByUser": {"post": {"tags": ["Policy"], "summary": "用户确认", "requestBody": {"description": "用户确认参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConsentModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConsentModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConsentModel"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}}}}}}, "/api/policy/CheckPolicyByUser": {"get": {"tags": ["Policy"], "summary": "用户检查", "parameters": [{"name": "type", "in": "query", "description": "用户类别", "schema": {"type": "integer", "format": "int32"}}, {"name": "applicationID", "in": "query", "description": "应用ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}}}}}}, "/api/policy/RevokePolicyByUser": {"get": {"tags": ["Policy"], "summary": "用户授权撤销", "parameters": [{"name": "type", "in": "query", "description": "用户类别", "schema": {"type": "integer", "format": "int32"}}, {"name": "applicationID", "in": "query", "description": "应用id", "schema": {"type": "string"}}, {"name": "policyID", "in": "query", "description": "政策id", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResModel"}}}}}}}, "/api/sftp/sync": {"get": {"tags": ["SFTP"], "summary": "同步SAP SFTP服务器上的产品数据", "description": "描述：\r\n    每日凌晨4点同步执行该任务，将SAP产品数据同步到Onepdt系统中\r\n\r\n参数:\r\n\r\n    SFTP Host： ***********\r\n    Port:22\r\n    认证：采用username和password的方式进行认证\r\n    \r\n文件命名规则:\r\n    - 前缀必须为: MDCN_Product_\r\n    - 后缀必须为: .csv\r\n    - 示例: MDCN_Product_20240318.csv\r\n    \r\nCSV文件格式:\r\n```\r\nEQUNR|ZMOD|SN|BATCH|VALTODATE|MANUFACTUREDATE|GTIN\r\nEQ001|MOD1|SN001|B001|2025-12-31|2024-03-18|GT001\r\n```", "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"BatchDeleteModel": {"type": "object", "properties": {"entityname": {"type": "string", "nullable": true}, "entityids": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ConsentModel": {"type": "object", "properties": {"type": {"type": "integer", "description": "用户类别", "format": "int32"}, "applicationID": {"type": "string", "description": "应用ID", "nullable": true}, "userID": {"type": "string", "description": "用户id", "nullable": true}, "policyID": {"type": "string", "description": "政策ID", "nullable": true}}, "additionalProperties": false}, "EmployeeInfo": {"type": "object", "properties": {"userid": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "mobile": {"type": "string", "nullable": true}, "gender": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "position": {"type": "string", "nullable": true}, "department": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FileModel": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "size": {"type": "integer", "format": "int32"}, "type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OnepdtConfig": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "configValue": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OperationEditData": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}, "deviceapplicationid": {"type": "string", "description": "关联系统信息——保卡", "nullable": true}, "devpatientName": {"type": "string", "description": "患者姓名", "nullable": true}, "devpatientGender": {"type": "string", "description": "患者性别", "nullable": true}, "devimplantHospital": {"type": "string", "description": "植入医院名字", "nullable": true}, "devimplantHospitalCode": {"type": "string", "description": "医院代码", "nullable": true}, "devimplantHospitalId": {"type": "string", "description": "植入医院id", "nullable": true}, "devimplantDate": {"type": "string", "description": "植入日期", "nullable": true}, "patientName": {"type": "string", "description": "患者姓名", "nullable": true}, "patientGender": {"type": "string", "description": "患者性别", "nullable": true}, "implantHospital": {"type": "string", "description": "植入医院名字", "nullable": true}, "implantHospitalCode": {"type": "string", "description": "医院代码", "nullable": true}, "implantHospitalId": {"type": "string", "description": "植入医院id", "nullable": true}, "implantDate": {"type": "string", "description": "植入日期", "nullable": true}, "firstSurgeon": {"type": "string", "description": "第一术者名称", "nullable": true}, "firstSurgeonid": {"type": "string", "description": "第一术者id", "nullable": true}, "secondSurgeon": {"type": "string", "description": "第二术者名称", "nullable": true}, "secondSurgeonid": {"type": "string", "description": "第二术者id", "nullable": true}, "thirdSurgeon": {"type": "string", "description": "第三术者名称", "nullable": true}, "thirdSurgeonid": {"type": "string", "description": "第三术者id", "nullable": true}, "surgicalAssistantType": {"type": "string", "description": "跟台类型名称", "nullable": true}, "surgicalAssistantTypeid": {"type": "string", "description": "跟台类型id", "nullable": true}, "surgicalAssistant": {"type": "string", "description": "跟台姓名", "nullable": true}, "surgeonDuration": {"type": "string", "description": "跟台耗时", "nullable": true}, "surgeonDurationid": {"type": "string", "description": "跟台耗时id", "nullable": true}, "hisPurkinjeSystemPacing": {"type": "string", "description": "希浦系统起搏", "nullable": true}, "hisPurkinjeSystemPacingid": {"type": "string", "description": "希浦系统起搏id", "nullable": true}, "xfsystem": {"type": "string", "description": "是否尝试心房生理性起搏", "nullable": true}, "xfsystemId": {"type": "string", "description": "是否尝试心房生理性起搏id", "nullable": true}, "success_tool_list": {"type": "string", "description": "成功植入使用的工具", "nullable": true}, "failed_tool_list": {"type": "string", "description": "尝试工具", "nullable": true}, "failtoolslist": {"type": "array", "items": {"$ref": "#/components/schemas/OnepdtConfig"}, "description": "尝试工具", "nullable": true}, "icdSurgeryProphylacticLevel": {"type": "string", "description": "ICD手术预防等级", "nullable": true}, "icdSurgeryProphylacticLevelid": {"type": "string", "description": "ICD手术预防等级id", "nullable": true}, "isIcdPatientWithCadHistory": {"type": "boolean", "description": "ICD患者是否有冠脉病史"}, "implantTypeid": {"type": "string", "description": "植入类型id", "nullable": true}, "implantType": {"type": "string", "description": "植入类型", "nullable": true}, "originaldeviceid": {"type": "string", "description": "原主机类型id", "nullable": true}, "originaldevice": {"type": "string", "description": "原主机类型", "nullable": true}, "replaceddeviceid": {"type": "string", "description": "现主机类型id", "nullable": true}, "replaceddevice": {"type": "string", "description": "现主机类型", "nullable": true}, "relatedoperationid": {"type": "string", "description": "历史跟台", "nullable": true}, "relatedoperationpatient": {"type": "string", "nullable": true}, "relatedoperationgender": {"type": "string", "nullable": true}, "relatedoperationdate": {"type": "string", "nullable": true}, "relatedoperationhospitalname": {"type": "string", "nullable": true}, "relatedoperationproduct_name": {"type": "string", "nullable": true}, "relatedoperationproduct_sn": {"type": "string", "nullable": true}, "isWarrantyCardMailed": {"type": "boolean", "description": "是否邮寄保卡"}, "ksheathvalue": {"type": "string", "description": "是否使用K鞘value", "nullable": true}, "ksheathlabel": {"type": "string", "description": "是否使用K鞘", "nullable": true}, "receType": {"type": "string", "description": "收件人类型", "nullable": true}, "address": {"type": "string", "description": "邮寄地址", "nullable": true}, "rece": {"type": "string", "description": "收件人姓名", "nullable": true}, "tel": {"type": "string", "description": "联系电话", "nullable": true}, "messageNote": {"type": "string", "description": "备注", "nullable": true}, "onepdt_submit_status_label": {"type": "string", "description": "提交状态", "nullable": true}, "rejectreason": {"type": "string", "description": "退回原因", "nullable": true}, "onepdt_approval_comment": {"type": "string", "description": "业务审批备注", "nullable": true}, "onepdt_approval_tag": {"type": "string", "description": "业务审批标签", "nullable": true}, "onepdt_address_approval_comment": {"type": "string", "description": "邮寄审批备注", "nullable": true}, "onepdt_address_approval_tag": {"type": "string", "description": "邮寄审批标签", "nullable": true}, "onepdt_approval_status": {"type": "integer", "description": "业务审批状态value", "format": "int32", "nullable": true}, "onepdt_approval_statuslabel": {"type": "string", "description": "业务审批状态label", "nullable": true}, "onepdt_address_approval_status": {"type": "integer", "description": "邮寄审批状态value", "format": "int32", "nullable": true}, "onepdt_address_approval_statuslabel": {"type": "string", "description": "邮寄审批状态label", "nullable": true}, "onepdt_is_returned": {"type": "boolean", "description": "是否退回"}, "authorizationLetter": {"type": "array", "items": {"$ref": "#/components/schemas/OpreationPhoto"}, "description": "授权书", "nullable": true}, "receiptForm": {"type": "array", "items": {"$ref": "#/components/schemas/OpreationPhoto"}, "description": "回执单", "nullable": true}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/OperationProductDetail"}, "description": "跟台植入产品明细", "nullable": true}, "deletePreviousProduct": {"type": "boolean", "description": "删除跟台产品"}, "isedit": {"type": "boolean", "description": "是否可编辑"}, "submitteremail": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OperationProductDetail": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "serialNumber": {"type": "string", "nullable": true}, "productid": {"type": "string", "nullable": true}, "model": {"type": "string", "nullable": true}, "bigType": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "isNonClinicalTrial": {"type": "boolean", "description": "是否非临床试验"}, "isWearAndTear": {"type": "boolean", "description": "是否损耗"}, "isNonMainlandProduct": {"type": "boolean", "description": "是否非大陆产品"}, "sapProduct": {"type": "array", "items": {"$ref": "#/components/schemas/SAPProduct"}, "description": "SAP产品", "nullable": true}, "deflag": {"type": "boolean", "description": "是否需要删除"}}, "additionalProperties": false}, "OpreationPhoto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "content": {"type": "string", "nullable": true}, "file": {"$ref": "#/components/schemas/FileModel"}}, "additionalProperties": false}, "ResModel": {"type": "object", "properties": {"flag": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "data": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SAPProduct": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "zmod": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}