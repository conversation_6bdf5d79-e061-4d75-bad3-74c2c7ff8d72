﻿using Microsoft.Crm.Sdk.Messages;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Workflow;
using Newtonsoft.Json;
using System;
using System.Activities;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.Onepdt.CodeActivities
{
    public class GetSFTPProductData : CodeActivity
    {
        [Input("SFTPFileId")]
        public InArgument<string> SFTPFileIdArgument { get; set; }

        [Output("Result")]
        public OutArgument<string> ResultArgument { get; set; }
        protected override void Execute(CodeActivityContext context)
        {
            // 获取服务
            IWorkflowContext workcontext = context.GetExtension<IWorkflowContext>();
            IOrganizationServiceFactory factory = context.GetExtension<IOrganizationServiceFactory>();
            ITracingService tracingService = context.GetExtension<ITracingService>();
            IOrganizationService serviceAdmin = factory.CreateOrganizationService(workcontext.UserId);

            var SFTPFileId = SFTPFileIdArgument.Get(context);

            var res = new ReturnModel();
            var list = new List<ProductModel>();
            try
            {
                //csv文件
                var base64 = DownloadFile(serviceAdmin, new EntityReference("onepdt_t_sftp_log", new Guid(SFTPFileId)), "onepdt_file");

                byte[] fileBytes = Convert.FromBase64String(base64);
                using (MemoryStream memoryStream = new MemoryStream(fileBytes))
                {
                    using (var reader = new StreamReader(memoryStream))
                    {
                        while (!reader.EndOfStream)
                        {
                            var line = reader.ReadLine();
                            if (!string.IsNullOrWhiteSpace(line))
                            {
                                var split = line.Split('|');
                                if (split != null && split.Length > 0)
                                {
                                    var model = new ProductModel();
                                    model.EQUNR = split.Length > 0 ? split[0] : "";
                                    model.ZMOD = split.Length > 1 ? split[1] : "";
                                    model.SN = split.Length > 2 ? split[2] : "";
                                    model.BATCH = split.Length > 3 ? split[3] : "";
                                    model.VALTODATE = split.Length > 4 ? split[4] : "";
                                    model.MANUFACTUREDATE = split.Length > 5 ? split[5] : "";
                                    model.GTIN = split.Length > 6 ? split[6] : "";
                                    list.Add(model);
                                }
                            }
                        }
                    }
                }

                res.data = JsonConvert.SerializeObject(list);
            }
            catch (Exception ex)
            {
                res.status = 500;
                res.message = ex.Message;
            }
            ResultArgument.Set(context, JsonConvert.SerializeObject(res));
        }

        /// <summary>
        /// Downloads a file or image
        /// </summary>
        /// <param name="service">The service</param>
        /// <param name="entityReference">A reference to the record with the file or image column</param>
        /// <param name="attributeName">The name of the file or image column</param>
        /// <returns></returns>
        public string DownloadFile(IOrganizationService service, EntityReference entityReference, string attributeName)
        {
            InitializeFileBlocksDownloadRequest initializeFileBlocksDownloadRequest = new InitializeFileBlocksDownloadRequest()
            {
                Target = entityReference,
                FileAttributeName = attributeName
            };

            var initializeFileBlocksDownloadResponse =
                  (InitializeFileBlocksDownloadResponse)service.Execute(initializeFileBlocksDownloadRequest);

            string fileContinuationToken = initializeFileBlocksDownloadResponse.FileContinuationToken;
            long fileSizeInBytes = initializeFileBlocksDownloadResponse.FileSizeInBytes;

            List<byte> fileBytes = new List<byte>((int)fileSizeInBytes);

            long offset = 0;
            // If chunking is not supported, chunk size will be full size of the file.
            long blockSizeDownload = fileSizeInBytes;

            // File size may be smaller than defined block size
            if (fileSizeInBytes < blockSizeDownload)
            {
                blockSizeDownload = fileSizeInBytes;
            }

            while (fileSizeInBytes > 0)
            {
                // Prepare the request
                DownloadBlockRequest downLoadBlockRequest = new DownloadBlockRequest()
                {
                    BlockLength = blockSizeDownload,
                    FileContinuationToken = fileContinuationToken,
                    Offset = offset
                };

                // Send the request
                var downloadBlockResponse =
                         (DownloadBlockResponse)service.Execute(downLoadBlockRequest);

                // Add the block returned to the list
                fileBytes.AddRange(downloadBlockResponse.Data);

                // Subtract the amount downloaded,
                // which may make fileSizeInBytes < 0 and indicate
                // no further blocks to download
                fileSizeInBytes -= (int)blockSizeDownload;
                // Increment the offset to start at the beginning of the next block.
                offset += blockSizeDownload;
            }

            return Convert.ToBase64String(fileBytes.ToArray());
        }

        public class ReturnModel
        {
            /// <summary>
            /// status
            /// </summary>
            public int status { get; set; } = 200;
            /// <summary>
            /// message
            /// </summary>
            public string message { get; set; } = "success";
            /// <summary>
            /// data
            /// </summary>
            public string data { get; set; } = string.Empty;
        }

        public class ProductModel
        {
            public string EQUNR { get; set; }
            public string ZMOD { get; set; }
            public string SN { get; set; }
            public string BATCH { get; set; }
            public string VALTODATE { get; set; }
            public string MANUFACTUREDATE { get; set; }
            public string GTIN { get; set; }
        }
    }
}
