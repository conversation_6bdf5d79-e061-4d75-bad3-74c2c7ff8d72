﻿using Abt.Epdt.WebApis.Command;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using System.Collections.Generic;
using System.Reflection.Metadata.Ecma335;

namespace Abt.Epdt.WebApis.Controller
{


    
    [ApiController]
    [Route("api/sftp")]
    public class SFTPController : ControllerBase
    {
        /// <summary>
        /// 同步SAP SFTP服务器上的产品数据
        /// </summary>
        /// <remarks>
        /// 
        /// 描述：
        ///     每日凌晨4点同步执行该任务，将SAP产品数据同步到Onepdt系统中
        /// 
        /// 参数:
        /// 
        ///     SFTP Host： ***********
        ///     Port:22
        ///     认证：采用username和password的方式进行认证
        ///     
        /// 文件命名规则:
        ///     - 前缀必须为: MDCN_Product_
        ///     - 后缀必须为: .csv
        ///     - 示例: MDCN_Product_20240318.csv
        ///     
        /// CSV文件格式:
        /// ```
        /// EQUNR|ZMOD|SN|BATCH|VALTODATE|MANUFACTUREDATE|GTIN
        /// EQ001|MOD1|SN001|B001|2025-12-31|2024-03-18|GT001
        /// ```
        /// </remarks>
        [HttpGet]
        [Route("sync")]
        public IActionResult SyncSAPProduct() {

            return Ok("");
            
        }
        //private readonly IMemoryCache _memoryCache;
        //private readonly SFTPCommand com;

        //public SFTPController(IMemoryCache memoryCache)
        //{
        //    _memoryCache = memoryCache;
        //    com = new SFTPCommand(_memoryCache);
        //}
        ///// <summary>
        ///// 获取SFTP文件
        ///// </summary>
        ///// <param name="env">环境</param>
        ///// <returns></returns>
        //[AllowAnonymous]
        //[HttpGet]
        //[Route("DownloadSftp")]
        //public IActionResult DownloadSftp(string env)
        //{
        //    try
        //    {
        //        var sftp = com.DownloadSftp(env);
        //        return Ok(sftp);
        //    }
        //    catch (System.Exception ex)
        //    {
        //        return BadRequest(ex.Message);
        //    }
        //}
    }
}
