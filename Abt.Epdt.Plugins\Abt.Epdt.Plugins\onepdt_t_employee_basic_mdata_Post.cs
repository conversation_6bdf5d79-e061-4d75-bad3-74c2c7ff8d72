﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Runtime.Remoting.Messaging;
using System.Runtime.Remoting.Metadata.W3cXsd2001;
using System.Text;
using System.Threading.Tasks;

namespace Abt.Epdt.Plugins
{
    public class onepdt_t_employee_basic_mdata_Post : IPlugin
    {
        public string WebapiUrl { get; set; }
        /// <summary>
        /// 员工创建更新同步至企微
        /// </summary>
        /// <param name="serviceProvider"></param>
        /// <exception cref="NotImplementedException"></exception>
        public async void Execute(IServiceProvider serviceProvider)
        {
            //获取执行上下文
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            //获取服务工厂
            IOrganizationServiceFactory factory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            //获取服务
            IOrganizationService service = factory.CreateOrganizationService(null);
            //获取调试服务
            ITracingService tracing = (ITracingService)serviceProvider.GetService(typeof(ITracingService));
            Entity entity = context.InputParameters["Target"] as Entity;

            try
            {
                if ((context.MessageName.ToLower() == "create" || context.MessageName.ToLower() == "update") && context.InputParameters["Target"] is Entity)
                {
                    var en = service.Retrieve("onepdt_t_employee_basic_mdata", entity.Id, new ColumnSet(true));
                    var info = new EmployeeInfo()
                    {
                        userid = en.GetAttributeValue<string>("onepdt_code"),
                        name = en.GetAttributeValue<string>("onepdt_name"),
                        mobile = en.GetAttributeValue<string>("onepdt_phone"),
                        gender = en.GetAttributeValue<string>("onepdt_gender"),
                        email = en.GetAttributeValue<string>("onepdt_mail"),
                        position = en.Contains("onepdt_position_id") ? en.GetAttributeValue<EntityReference>("onepdt_position_id").Name : "",
                        department = en.Contains("onepdt_buid") ? en.GetAttributeValue<EntityReference>("onepdt_buid").Name : "",
                        status = en.GetAttributeValue<string>("onepdt_employment_status")
                    };

                    var resultString = InvokeWebApi(HttpMethod.Post, $"api/employee/WechatUser", service, info, true, tracing);

                    var res = JsonConvert.DeserializeObject<ResModel>(resultString);
                    if (res.flag == "E")
                    {
                        throw new Exception(res.message);
                    }
                }
            }
            catch (Exception ex)
            {
                tracing.Trace($"同步企微用户失败：{ex.Message}", ex.StackTrace);
                throw new InvalidPluginExecutionException($"同步企微用户失败：{ex.Message}");
            }


        }

        /// <summary>
        /// 调用webapi
        /// </summary>
        /// <param name="method"></param>
        /// <param name="apiAddress"></param>
        /// <param name="paramDic"></param>
        /// <param name="needToken">默认需要认证</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public string InvokeWebApi(HttpMethod method, string apiAddress, IOrganizationService service, object paramDic = null, bool needToken = true, ITracingService tracing = null)
        {
            Dictionary<string, string> headers = null;
            if (needToken)
            {
                headers = GetWebApiTokenHeader(service);
            }
            tracing.Trace("获取完token");
            var responseMessage = new HttpResponseMessage();
            if (method == HttpMethod.Get)
            {
                tracing.Trace("DoGet");
                responseMessage = DoGet(apiAddress, headers);
            }
            else if (method == HttpMethod.Post)
            {

                responseMessage = DoPost(apiAddress, JsonConvert.SerializeObject(paramDic), headers);
            }
            else
            {
                throw new Exception("目前仅支持Get和Post");
            }
            tracing.Trace("DoGet 完成");
            var resultString = responseMessage.Content.ReadAsStringAsync().GetAwaiter().GetResult();
            tracing.Trace("DoGet resultString:" + resultString);
            return resultString;

        }

        /// <summary>
        /// Http get
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public HttpResponseMessage DoGet(string url, Dictionary<string, string> headers = null)
        {
            using (HttpClient client = new HttpClient())
            {
                try
                {
                    url = WebapiUrl + url;
                    HttpRequestMessage req = new HttpRequestMessage(HttpMethod.Get, url);
                    if (headers != null)
                    {
                        foreach (string key in headers.Keys)
                            req.Headers.Add(key, headers[key]);
                    }
                    var task = client.SendAsync(req);
                    task.Wait();
                    return task.Result;
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
        }

        public HttpResponseMessage DoPost(string url, string postContent, Dictionary<string, string> headers = null, string contentType = "application/json")
        {

            using (HttpClient client = new HttpClient())
            {
                try
                {
                    url = WebapiUrl + url;
                    HttpRequestMessage req = new HttpRequestMessage(HttpMethod.Post, url);
                    req.Content = new StringContent(postContent, Encoding.UTF8, contentType);
                    if (headers != null)
                    {
                        foreach (string key in headers.Keys)
                            req.Headers.Add(key, headers[key]);
                    }
                    var task = client.SendAsync(req);
                    task.Wait();
                    return task.Result;
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
        }

        /// <summary>
        /// 获取WebapiClient
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, string> GetWebApiTokenHeader(IOrganizationService service)
        {
            Dictionary<string, string> headers = new Dictionary<string, string>();
            var apiKey = InitApiKey(service);
            var url = InitApiUrl(service);
            var token = InitApiToken(apiKey, WebapiUrl);
            headers.Add("Authorization", "Bearer " + token);
            return headers;
        }

        /// <summary>
        /// 初始化apikey
        /// </summary>
        /// <param name="service"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private string InitApiKey(IOrganizationService service)
        {
            var apikey = "";
            #region 查询对应的API配置的Key
            string fetchXml = $@"<fetch mapping='logical' version='1.0' page='1' count='1'>
                                  <entity name='onepdt_t_configuration'>
                                    <attribute name='onepdt_value' />
                                    <filter>
                                      <condition attribute='onepdt_name' operator='eq' value='OnePDT_Token' />
                                      <condition attribute='statecode' operator='eq' value='0' />
                                      <condition attribute='onepdt_value' operator='not-null' />
                                    </filter>
                                  </entity>
                                </fetch>";
            var ec = service.RetrieveMultiple(new FetchExpression(fetchXml));

            if (ec?.Entities?.Count > 0)
            {
                apikey = ec.Entities.First().GetAttributeValue<string>("onepdt_value");
            }
            else
            {
                throw new Exception("全局配置[OnePDT_Token]缺失，请联系管理员");
            }
            if (string.IsNullOrWhiteSpace(apikey))
            {
                throw new Exception("全局配置[OnePDT_Token]的Key不正确，请联系管理员");
            }
            #endregion
            return apikey;
        }

        /// <summary>
        /// 初始化apiUrl
        /// </summary>
        /// <exception cref="Exception"></exception>
        private string InitApiUrl(IOrganizationService service)
        {
            #region 查询WebAPIUrl
            string fetchXml = $@"<fetch mapping='logical' version='1.0' page='1' count='1'>
                                  <entity name='onepdt_t_configuration'>
                                    <attribute name='onepdt_value' />
                                    <filter>
                                      <condition attribute='onepdt_name' operator='eq' value='WebAPIUrl' />
                                      <condition attribute='statecode' operator='eq' value='0' />
                                      <condition attribute='onepdt_value' operator='not-null' />
                                    </filter>
                                  </entity>
                                </fetch>";
            var ec = service.RetrieveMultiple(new FetchExpression(fetchXml));

            if (ec?.Entities?.Count > 0)
            {
                WebapiUrl = ec.Entities.First().GetAttributeValue<string>("onepdt_value");
            }
            else
            {
                throw new Exception("全局配置[WebAPIUrl]缺失，请联系管理员");
            }
            if (string.IsNullOrWhiteSpace(WebapiUrl))
            {
                throw new Exception("全局配置[WebAPIUrl]的配置值不正确，请联系管理员");
            }
            #endregion
            return WebapiUrl;

        }

        /// <summary>
        /// 初始化token
        /// </summary>
        /// <exception cref="Exception"></exception>
        private string InitApiToken(string apiKey, string webapiUrl)
        {
            try
            {
                var apiToken = DoGet($"/api/auth/token?appid={apiKey}").Content.ReadAsStringAsync().GetAwaiter().GetResult();
                return apiToken;
            }
            catch (Exception ex)
            {
                throw new Exception("初始化apitoken失败：" + ex.Message.ToString());
            }
        }
    }

    public class ResModel
    {
        public string flag { get; set; } = "S";
        public string code { get; set; }
        public string message { get; set; } = "成功";
        public string data { get; set; }
    }

    public class EmployeeInfo
    {
        public string userid { get; set; } = "";
        public string name { get; set; } = "";
        public string mobile { get; set; } = "";
        public string gender { get; set; } = "";
        public string email { get; set; } = "";
        public string position { get; set; } = "";
        public string department { get; set; } = "";
        public string status { get; set; } = "";
    }
}
