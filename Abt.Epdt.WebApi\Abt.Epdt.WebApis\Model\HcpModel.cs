﻿namespace Abt.Epdt.WebApis.Model
{
    public class ResModel
    {
        public string flag { get; set; } = "S";
        public string code { get; set; }
        public string message { get; set; } = "成功";
        public string data { get; set; }
    }
    public class HcpModel
    {
        /// <summary>
        /// 数据标识
        /// </summary>
        public string sign { get; set; } = "";
        /// <summary>
        /// 医生姓名
        /// </summary>
        public string name { get; set; } = "";
        /// <summary>
        /// 性别
        /// </summary>
        public string gender { get; set; } = "";
        /// <summary>
        /// 医生编码
        /// </summary>
        public string code { get; set; } = "";
        /// <summary>
        /// 医生称呼
        /// </summary>
        public string title { get; set; } = "";
        /// <summary>
        /// 医生分类
        /// </summary>
        public string classification { get; set; } = "";
        /// <summary>
        /// 电生理手术量
        /// </summary>
        public int electrophysiologynum { get; set; }
        /// <summary>
        /// 冠脉手术量
        /// </summary>
        public int coronarynum { get; set; }
        /// <summary>
        /// 结构性心脏病手术量
        /// </summary>
        public int heartdiseasenum { get; set; }
        /// <summary>
        /// 科室
        /// </summary>
        public string department { get; set; } = "";
        /// <summary>
        /// 客户类型
        /// </summary>
        public string customertype { get; set; } = "";
        /// <summary>
        /// 使用偏好
        /// </summary>
        public string preference { get; set; } = "";
        /// <summary>
        /// 是否VIP
        /// </summary>
        public bool isvip { get; set; }
        /// <summary>
        /// 为雅培讲课次数
        /// </summary>
        public string abbottlecturecount { get; set; } = "";
        /// <summary>
        /// 行政职务
        /// </summary>
        public string administrative { get; set; } = "";
        /// <summary>
        /// 学会职称
        /// </summary>
        public string institutionposition { get; set; } = "";
        /// <summary>
        /// 学术职称
        /// </summary>
        public string academictitle { get; set; } = "";
        /// <summary>
        /// 雅培份额排名
        /// </summary>
        public string abbottrank { get; set; } = "";
        /// <summary>
        /// 医生执业证书编号
        /// </summary>
        public string hcpcode { get; set; } = "";
        /// <summary>
        /// 主要执业机构编码
        /// </summary>
        public string sfehospitalid { get; set; } = "";
        /// <summary>
        /// 主要执业机构名称
        /// </summary>
        public string sfehospitalname { get; set; } = "";
        /// <summary>
        /// HV医生分类
        /// </summary>
        public string hvclassification { get; set; } = "";
        /// <summary>
        /// 申请人
        /// </summary>
        public string application { get; set; } = "";
        /// <summary>
        /// 备注
        /// </summary>
        public string comment { get; set; } = "";
        /// <summary>
        /// 修改时间
        /// </summary>
        public string modifiedon { get; set; } = "";
    }
}
