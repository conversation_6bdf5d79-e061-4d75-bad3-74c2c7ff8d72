﻿using System;
using System.Linq;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System.Activities;
using System.Collections.Generic;
using Abbott.Onepdt.Plugins.Models;
using Abbott.Onepdt.Plugins.Service;
using System.Security.Policy;
using System.Collections;


namespace Abbott.Onepdt.Plugins
{
    public class IdentifyCardTypeAction : IPlugin
    {

        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService service = serviceFactory.CreateOrganizationService(null); // 权限

            ITracingService tracingService = (ITracingService)serviceProvider.GetService(typeof(ITracingService));
            try
            {
                // 判断预览保卡还是查看已有保卡
                bool IsPreview = (bool)context.InputParameters["IsPreview"];


                string OpreationId = (string)context.InputParameters["OpreationId"];

                List<CardTypeModel> cardTypeModels = new List<CardTypeModel>();

                ActionResult actionResult = new ActionResult();
                if (IsPreview)
                {
                    cardTypeModels = IdentifyCardTypeService.GetCardTypeModels(service, OpreationId);
                    //判断跟台信息是否关联电子保卡申请表
                    Entity operation = service.Retrieve("onepdt_t_operation", new Guid(OpreationId), new ColumnSet("onepdt_device_application"));
                    if (operation.Contains("onepdt_device_application") && operation["onepdt_device_application"] != null)
                    {
                        Guid applicationId = ((EntityReference)operation["onepdt_device_application"]).Id;
                        foreach (CardTypeModel model in cardTypeModels)
                        {
                            string extendedWarranty = IdentifyCardTypeService.GetExtendedWarranty(service, applicationId, model.ModelNo,model.ImpDate);
                            model.WarrantyPeriod = model.WarrantyPeriod+extendedWarranty;
                        }
                    }

                }
                else
                {
                    // 查询识别卡列表
                    QueryExpression patientIdCardQuery = new QueryExpression("onepdt_t_patient_id_card");
                    patientIdCardQuery.ColumnSet.AllColumns = true;
                    patientIdCardQuery.Criteria.AddCondition("onepdt_t_operation", ConditionOperator.Equal, new Guid(OpreationId));
                    var patientIdCardQuery_onepdt_t_operation = patientIdCardQuery.AddLink("onepdt_t_operation", "onepdt_t_operation", "onepdt_t_operationid");
                    patientIdCardQuery_onepdt_t_operation.EntityAlias = "onepdt_t_operation";
                    patientIdCardQuery_onepdt_t_operation.Columns.AllColumns = true;
                    var query_epdt_t_device_mdata = patientIdCardQuery.AddLink("epdt_t_device_mdata", "onepdt_name", "epdt_name");
                    query_epdt_t_device_mdata.EntityAlias = "epdt_t_device_mdata";
                    query_epdt_t_device_mdata.Columns.AddColumns( "epdt_t_device_mdataid", "epdt_hospital_address", "epdt_hospital_phone", "epdt_implant_hospital_name");
                    EntityCollection results = service.RetrieveMultiple(patientIdCardQuery);

                    if (results.Entities.Count > 0)
                    {
                        EntityCollection HospitalBranchMappings = IdentifyCardTypeService.GetHospitalBranchMappings(service);
                        var patientIdCardEntites = results.Entities.ToList();
                        // 找到 MAT 对应卡的类型
                        QueryExpression cardtypeManagementQuery = new QueryExpression("onepdt_t_id_cardtype_management")
                        {
                            ColumnSet = new ColumnSet(true)
                        };
                        cardtypeManagementQuery.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                        EntityCollection cardtypeManagementEntites = service.RetrieveMultiple(cardtypeManagementQuery);


                        foreach (var entity in patientIdCardEntites)
                        {
                            CardTypeModel cardTypeModel = new CardTypeModel
                            {
                                onepdt_t_operation_implantid = entity.Id.ToString(),
                                CardNo = entity.GetAttributeValue<string>("onepdt_name"),
                                Name = ((AliasedValue)entity["onepdt_t_operation.onepdt_patient"]).Value.ToString(),
                                Gender = ((AliasedValue)entity["onepdt_t_operation.onepdt_gender"]).Value.ToString(),
                                ImpDate = ((DateTime)((AliasedValue)entity["onepdt_t_operation.onepdt_date"]).Value).ToString("yyyy-MM-dd"),
                                Physician1 = ((AliasedValue)entity["onepdt_t_operation.onepdt_hcp_text"]).Value.ToString(),
                                //Physician2 = entity.Contains("onepdt_t_operation.onepdt_secondary_hcp") ? ((EntityReference)((AliasedValue)entity["onepdt_t_operation.onepdt_secondary_hcp"]).Value).Name : string.Empty,
                                Hospital = ((AliasedValue)entity["epdt_t_device_mdata.epdt_implant_hospital_name"]).Value.ToString(),
                                HospitalAdd = ((AliasedValue)entity["epdt_t_device_mdata.epdt_hospital_address"]).Value.ToString(),
                                HospitalTel = ((AliasedValue)entity["epdt_t_device_mdata.epdt_hospital_phone"]).Value.ToString(),

                                CardType = entity.GetAttributeValue<string>("onepdt_final_warranty_card_type") ?? entity.GetAttributeValue<string>("onepdt_warranty_card_type"),

                                ModelNo = entity.GetAttributeValue<string>("onepdt_main_unit1_model"),

                                SN = entity.GetAttributeValue<string>("onepdt_main_unit1_serial_number"),

                                WarrantyPeriod = entity.GetAttributeValue<string>("onepdt_main_unit1_warranty_period"),

                                Manufacturer = entity.GetAttributeValue<string>("onepdt_main_unit1_manufacturer_brand"),

                                SelectedMode = entity.GetAttributeValue<string>("onepdt_main_unit1_selected_pacing_defibrillation_mode"),

                                MainMode = entity.GetAttributeValue<string>("onepdt_main_unit1_pacing_defibrillation_mode"),

                                ScanIntensity = entity.GetAttributeValue<string>("onepdt_main_unit1_scan_intensity"),

                                Accessories = new List<CardTypeModel.Accessory>()
                            };



                            // 找到 MAT 对应卡的类型
                            var cardTypeEntity = cardtypeManagementEntites.Entities
                                .FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_name") == cardTypeModel.CardType);

                            cardTypeModel.CardType = cardTypeEntity != null
                                ? cardTypeEntity.GetAttributeValue<string>("onepdt_type")
                                : "";



                            CardTypeModel.Accessory accessory1 = new CardTypeModel.Accessory
                            {
                                ModelNo = entity.GetAttributeValue<string>("onepdt_electrode1_model"),

                                SN = entity.GetAttributeValue<string>("onepdt_electrode1_serial_number"),

                                ImpDate = entity.Contains("onepdt_electrode1_implant_date") ? entity.GetAttributeValue<DateTime>("onepdt_electrode1_implant_date").ToString("yyyy-MM-dd") : null,

                                Manufacturer = entity.GetAttributeValue<string>("onepdt_electrode1_manufacturer_brand"),

                            };
                            cardTypeModel.Accessories.Add(accessory1);

                            CardTypeModel.Accessory accessory2 = new CardTypeModel.Accessory
                            {
                                ModelNo = entity.GetAttributeValue<string>("onepdt_electrode2_model"),

                                SN = entity.GetAttributeValue<string>("onepdt_electrode2_serial_number"),

                                ImpDate = entity.Contains("onepdt_electrode2_implant_date") ? entity.GetAttributeValue<DateTime>("onepdt_electrode2_implant_date").ToString("yyyy-MM-dd") : null,

                                Manufacturer = entity.GetAttributeValue<string>("onepdt_electrode2_manufacturer_brand"),
                            };
                            cardTypeModel.Accessories.Add(accessory2);

                            CardTypeModel.Accessory accessory3 = new CardTypeModel.Accessory
                            {
                                ModelNo = entity.GetAttributeValue<string>("onepdt_electrode3_model"),

                                SN = entity.GetAttributeValue<string>("onepdt_electrode3_serial_number"),

                                ImpDate = entity.Contains("onepdt_electrode3_implant_date") ? entity.GetAttributeValue<DateTime>("onepdt_electrode3_implant_date").ToString("yyyy-MM-dd") : null,

                                Manufacturer = entity.GetAttributeValue<string>("onepdt_electrode3_manufacturer_brand"),
                            };
                            cardTypeModel.Accessories.Add(accessory3);

                            CardTypeModel.Accessory accessory4 = new CardTypeModel.Accessory
                            {
                                ModelNo = entity.GetAttributeValue<string>("onepdt_electrode4_model"),

                                SN = entity.GetAttributeValue<string>("onepdt_electrode4_serial_number"),

                                ImpDate = entity.Contains("onepdt_electrode4_implant_date") ? entity.GetAttributeValue<DateTime>("onepdt_electrode4_implant_date").ToString("yyyy-MM-dd") : null,

                                Manufacturer = entity.GetAttributeValue<string>("onepdt_electrode4_manufacturer_brand"),
                            };
                            cardTypeModel.Accessories.Add(accessory4);

                            cardTypeModels.Add(cardTypeModel);
                        }

                    }
                }
                actionResult.Code = "01";

                actionResult.Message = "success";

                actionResult.Data = cardTypeModels;

                context.OutputParameters["Result"] = Newtonsoft.Json.JsonConvert.SerializeObject(cardTypeModels);
            }
            catch (Exception ex)
            {
                tracingService.Trace(ex.Message + ex.StackTrace);
                throw;
            }
        }

       
    }
}
