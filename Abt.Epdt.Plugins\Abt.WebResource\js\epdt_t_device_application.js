"use strict";

var alertOptions = { height: 500, width: 400 };
var confirmOptions = { height: 200, width: 300 };
var confirmStrings = { title: "确认框", text: "" };

//发卡按钮调用方法
function onSendCard(selcCtrl, selcids) {
    if (selcids.length === 0) {
        return;
    }

    confirmStrings.text = "请确认是否发卡？";
    Xrm.Navigation.openConfirmDialog(confirmStrings, confirmOptions).then(function (success) {
        if (success.confirmed) {
            var req = {
                DeviceApplicationIds: selcids.toString()
            };
            req.getMetadata = function () {
                return {
                    boundParameter: null,
                    parameterTypes: {
                        "DeviceApplicationIds": {
                            "typeName": "Edm.String",
                            "structuralProperty": 1
                        },
                    },
                    operationType: 0,
                    operationName: "epdt_DeviceApplicationSendCardV2"
                };
            };

            Xrm.WebApi.online.execute(req).then(function (response) {
                if (response.ok) {
                    response.json().then((res) => {
                        if (res.Result === "") {
                            Xrm.Navigation.openAlertDialog("选中的数据不符合发卡条件，请确认后重新选择。");
                        }
                        else {
                            let alertMessage = "";
                            let result = JSON.parse(res.Result);
                            if (result.sendFailedCard !== "") {
                                alertMessage += "【" + result.sendFailedCard + "】发卡失败 \n"
                            }
                            if (result.sendSuccCard !== "") {
                                alertMessage += "【" + result.sendSuccCard + "】发卡成功 \n"
                            }

                            if (alertMessage !== "")
                                Xrm.Navigation.openAlertDialog(alertMessage, alertOptions).then(function () {
                                    selcCtrl.refresh();
                                });
                        }
                    })
                }
            })
                .catch(function (error) {
                    console.log(error);
                    Xrm.Navigation.openErrorDialog(error);
                });
        }
    });
}

//发卡按钮可用条件
function onSendCardEnableRule(selcCtrl, selcids) {
    //【匹配成功的电子保卡】视图可见
    if (selcCtrl === null || selcCtrl === undefined || selcCtrl.getViewSelector() === null || selcCtrl.getViewSelector() === undefined) return false;
    let currView = selcCtrl.getViewSelector().getCurrentView();
    if (currView === null || currView === undefined || currView.name !== "匹配成功的电子保卡") return false;

    //根据保卡状态值展示按钮，直接从列表上取得字段值，不必通过查询后台提高按钮显示性能
    if (selcCtrl.getGrid() === null || selcCtrl.getGrid() === undefined) return false;
    let allSelectedRows = selcCtrl.getGrid().getSelectedRows();
    for (const row of allSelectedRows.getAll()) {
        try {
            var data = row.getData();
            var crmEntity = data.getEntity();
            //确保作为条件的字段在视图上显示，否则判断会有问题
            var check_status = crmEntity.attributes.get("epdt_check_status").getValue();
            var card_status = crmEntity.attributes.get("epdt_e_card_status").getValue();
            var review_results = crmEntity.attributes.get("epdt_review_results").getValue();
            if (card_status === "已冲销" || card_status === "已发放" || review_results === "无此植入") return false;

            if (check_status !== "匹配成功") return false;
        }
        catch (e) {
            console.error(e);
            return false;
        }
    }

    return true;
}

//销售复核按钮调用方法
function onSalesReview(selcCtrl, selcids) {
    if (selcids.length === 0) {
        return;
    }

    confirmStrings.text = "请确认是否推给销售复核？";
    Xrm.Navigation.openConfirmDialog(confirmStrings, confirmOptions).then(function (success) {
        if (success.confirmed) {
            var req = {
                DeviceApplicationIds: selcids.toString()
            };
            req.getMetadata = function () {
                return {
                    boundParameter: null,
                    parameterTypes: {
                        "DeviceApplicationIds": {
                            "typeName": "Edm.String",
                            "structuralProperty": 1
                        },
                    },
                    operationType: 0,
                    operationName: "epdt_DeviceApplicationSalesReview"
                };
            };

            Xrm.WebApi.online.execute(req).then(function (response) {
                if (response.ok) {
                    response.json().then((res) => {
                        let alertMessage = "";
                        let result = JSON.parse(res.Result);

                        if (result.salesReviewCard !== "") {
                            alertMessage += "【" + result.salesReviewCard + "】推送成功\n"
                        }
                        if (result.noSalesCard !== "") {
                            alertMessage += "【" + result.noSalesCard + "】植入数据查询不到对应的销售代表\n"
                        }

                        if (alertMessage !== "")
                            Xrm.Navigation.openAlertDialog(alertMessage, alertOptions).then(function () {
                                selcCtrl.refresh();
                            });
                    })
                }
            })
                .catch(function (error) {
                    console.log(error);
                    Xrm.Navigation.openErrorDialog(error);
                });
        }
    });
}

//销售复核按钮可用条件
function onSalesReviewEnableRule(selcCtrl, selcids) {
    //【匹配失败的电子保卡】视图可见
    if (selcCtrl === null || selcCtrl === undefined || selcCtrl.getViewSelector() === null || selcCtrl.getViewSelector() === undefined) return false;
    let currView = selcCtrl.getViewSelector().getCurrentView();
    if (currView === null || currView === undefined || currView.name !== "匹配失败的电子保卡") return false;

    //根据保卡状态值展示按钮，直接从列表上取得字段值，不必通过查询后台提高按钮显示性能
    if (selcCtrl.getGrid() === null || selcCtrl.getGrid() === undefined) return false;
    let allSelectedRows = selcCtrl.getGrid().getSelectedRows();
    for (const row of allSelectedRows.getAll()) {
        try {
            var data = row.getData();
            var crmEntity = data.getEntity();
            //确保作为条件的字段在视图上显示，否则判断会有问题
            var check_status = crmEntity.attributes.get("epdt_check_status").getValue();
            var card_status = crmEntity.attributes.get("epdt_e_card_status").getValue();
            var review_results = crmEntity.attributes.get("epdt_review_results").getValue();
            var approve_status = crmEntity.attributes.get("epdt_sales_approve_status").getValue(); //复核状态不能为已复核或待复核
            if (card_status === "已冲销" || card_status === "已发放" || review_results === "无此植入")
                return false;

            if (approve_status !== null || check_status !== "匹配失败") return false;
        }
        catch (e) {
            console.error(e);
            return false;
        }
    }

    return true;
}

//取消匹配按钮调用方法
function onUnmatch(selcCtrl, selcids) {
    if (selcids.length === 0) {
        return;
    }

    confirmStrings.text = "请确认是否取消匹配？";
    Xrm.Navigation.openConfirmDialog(confirmStrings, confirmOptions).then(async function (success) {
        if (success.confirmed) {
            let alertMessage = "";
            let allSelectedRows = selcCtrl.getGrid().getSelectedRows();
            for (const row of allSelectedRows.getAll()) {
                var data = row.getData();
                var crmEntity = data.getEntity();
                var name = crmEntity.attributes.get("epdt_name").getValue();

                var data =
                {
                    "epdt_if_verify_immediately": "否",
                    "epdt_check_status": "待匹配",
                    "epdt_cancel_match_date": new Date(),
                    "epdt_operation": "取消匹配",
                    "epdt_application_find_main_data": null
                }
                // update the record
               await Xrm.WebApi.updateRecord("epdt_t_device_application", crmEntity.getId(), data).then(
                    function success(result) {
                        alertMessage += "【" + name + "】取消匹配成功\n"
                    },
                    function (error) {
                        alertMessage += "【" + name + "】取消匹配失败：" + error.message + "\n"
                    }
                );
            }

            if (alertMessage !== "")
                Xrm.Navigation.openAlertDialog(alertMessage, alertOptions).then(function () {
                    selcCtrl.refresh();
                });
        }
    });
}

function onUnmatchEnableRule(selcCtrl, selcids) {
    //【匹配成功的电子保卡】视图可见
    if (selcCtrl === null || selcCtrl === undefined || selcCtrl.getViewSelector() === null || selcCtrl.getViewSelector() === undefined) return false;
    let currView = selcCtrl.getViewSelector().getCurrentView();
    if (currView === null || currView === undefined || currView.name !== "匹配成功的电子保卡") return false;

    //根据保卡状态值展示按钮，直接从列表上取得字段值，不必通过查询后台提高按钮显示性能
    if (selcCtrl.getGrid() === null || selcCtrl.getGrid() === undefined) return false;
    let allSelectedRows = selcCtrl.getGrid().getSelectedRows();
    for (const row of allSelectedRows.getAll()) {
        try {
            var data = row.getData();
            var crmEntity = data.getEntity();
            //确保作为条件的字段在视图上显示，否则判断会有问题
            var check_status = crmEntity.attributes.get("epdt_check_status").getValue();
            var card_status = crmEntity.attributes.get("epdt_e_card_status").getValue();
            var review_results = crmEntity.attributes.get("epdt_review_results").getValue();
            var approve_status = crmEntity.attributes.get("epdt_sales_approve_status").getValue();
            if (card_status === "已发放" || review_results === "无此植入" || approve_status === "已复核") return false;

            if (check_status !== "匹配成功") return false;
        }
        catch (e) {
            console.error(e);
            return false;
        }
    }

    return true;
}

//确认匹配按钮调用方法
function manual_match_openPage(data) {
    var selectedRecordID = data;

    //打开手动匹配页面
    //Inline Page
    var pageInput = {
        pageType: "custom",
        name: "epdt__cdc15",   //粘custompage的name
        recordId: selectedRecordID
    };
    var navigationOptions = {
        target: 1
    };

    Xrm.Navigation.navigateTo(pageInput, navigationOptions)
        .then(
            function () {
                //Called when page opens
            }
        ).catch(
            function (error) {
                //Handle error
            }
        )

}