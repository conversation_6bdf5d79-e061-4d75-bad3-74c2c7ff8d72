## Ignore Visual Studio temporary files, build results, and
## files generated by popular Visual Studio add-ons.
##
## Get latest from https://github.com/github/gitignore/blob/master/VisualStudio.gitignore

# User-specific files
*.suo
*.user
*.userosscache
*.sln.docstates

# User-specific files (MonoDevelop/Xamarin Studio)
*.userprefs

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/

# Visual Studio 2015/2017 cache/options directory
.vs/
# Uncomment if you have tasks that create the project's static files in wwwroot
#wwwroot/

# Visual Studio 2017 auto generated files
Generated\ Files/

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NUNIT
*.VisualState.xml
TestResult.xml

# Build Results of an ATL Project
[Dd]ebugPS/
[Rr]eleasePS/
dlldata.c

# Benchmark Results
BenchmarkDotNet.Artifacts/

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/
**/Properties/launchSettings.json

# StyleCop
StyleCopReport.xml

# Files built by Visual Studio
*_i.c
*_p.c
*_i.h
*.ilk
*.meta
*.obj
*.iobj
*.pch
*.pdb
*.ipdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Chutzpah Test files
_Chutzpah*

# Visual C++ cache files
ipch/
*.aps
*.ncb
*.opendb
*.opensdf
*.sdf
*.cachefile
*.VC.db
*.VC.VC.opendb

# Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

# Visual Studio Trace Files
*.e2e

# TFS 2012 Local Workspace
$tf/

# Guidance Automation Toolkit
*.gpState

# ReSharper is a .NET coding add-in
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# JustCode is a .NET coding add-in
.JustCode

# TeamCity is a build add-in
_TeamCity*

# DotCover is a Code Coverage Tool
*.dotCover

# AxoCover is a Code Coverage Tool
.axoCover/*
!.axoCover/settings.json

# Visual Studio code coverage results
*.coverage
*.coveragexml

# NCrunch
_NCrunch_*
.*crunch*.local.xml
nCrunchTemp_*

# MightyMoose
*.mm.*
AutoTest.Net/

# Web workbench (sass)
.sass-cache/

# Installshield output folder
[Ee]xpress/

# DocProject is a documentation generator add-in
DocProject/buildhelp/
DocProject/Help/*.HxT
DocProject/Help/*.HxC
DocProject/Help/*.hhc
DocProject/Help/*.hhk
DocProject/Help/*.hhp
DocProject/Help/Html2
DocProject/Help/html

# Click-Once directory
publish/

# Publish Web Output
*.[Pp]ublish.xml
*.azurePubxml
# Note: Comment the next line if you want to checkin your web deploy settings,
# but database connection strings (with potential passwords) will be unencrypted
*.pubxml
*.publishproj

# Microsoft Azure Web App publish settings. Comment the next line if you want to
# checkin your Azure Web App publish settings, but sensitive information contained
# in these scripts will be unencrypted
PublishScripts/

# NuGet Packages
*.nupkg
# The packages folder can be ignored because of Package Restore
**/[Pp]ackages/*
# except build/, which is used as an MSBuild target.
!**/[Pp]ackages/build/
# Uncomment if necessary however generally it will be regenerated when needed
#!**/[Pp]ackages/repositories.config
# NuGet v3's project.json files produces more ignorable files
*.nuget.props
*.nuget.targets

# Microsoft Azure Build Output
csx/
*.build.csdef

# Microsoft Azure Emulator
ecf/
rcf/

# Windows Store app package directories and files
AppPackages/
BundleArtifacts/
Package.StoreAssociation.xml
_pkginfo.txt
*.appx

# Visual Studio cache files
# files ending in .cache can be ignored
*.[Cc]ache
# but keep track of directories ending in .cache
!*.[Cc]ache/

# Others
ClientBin/
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.jfm
*.publishsettings
orleans.codegen.cs

# Including strong name files can present a security risk 
# (https://github.com/github/gitignore/pull/2483#issue-259490424)

# Since there are multiple workflows, uncomment next line to ignore bower_components
# (https://github.com/github/gitignore/pull/1529#issuecomment-104372622)
#bower_components/

# RIA/Silverlight projects
Generated_Code/

# Backup & report files from converting an old project file
# to a newer Visual Studio version. Backup files are not needed,
# because we have git ;-)
_UpgradeReport_Files/
Backup*/
UpgradeLog*.XML
UpgradeLog*.htm
ServiceFabricBackup/
*.rptproj.bak

# SQL Server files
*.mdf
*.ldf
*.ndf

# Business Intelligence projects
*.rdl.data
*.bim.layout
*.bim_*.settings
*.rptproj.rsuser

# Microsoft Fakes
FakesAssemblies/

# GhostDoc plugin setting file
*.GhostDoc.xml

# Node.js Tools for Visual Studio
.ntvs_analysis.dat
node_modules/

# Visual Studio 6 build log
*.plg

# Visual Studio 6 workspace options file
*.opt

# Visual Studio 6 auto-generated workspace file (contains which files were open etc.)
*.vbw

# Visual Studio LightSwitch build output
**/*.HTMLClient/GeneratedArtifacts
**/*.DesktopClient/GeneratedArtifacts
**/*.DesktopClient/ModelManifest.xml
**/*.Server/GeneratedArtifacts
**/*.Server/ModelManifest.xml
_Pvt_Extensions

# Paket dependency manager
.paket/paket.exe
paket-files/

# FAKE - F# Make
.fake/

# JetBrains Rider
.idea/
*.sln.iml

# CodeRush
.cr/

# Python Tools for Visual Studio (PTVS)
__pycache__/
*.pyc

# Cake - Uncomment if you are using it
# tools/**
# !tools/packages.config

# Tabs Studio
*.tss

# Telerik's JustMock configuration file
*.jmconfig

# BizTalk build output
*.btp.cs
*.btm.cs
*.odx.cs
*.xsd.cs

# OpenCover UI analysis results
OpenCover/

# Azure Stream Analytics local run output 
ASALocalRun/

# MSBuild Binary and Structured Log
*.binlog

# NVidia Nsight GPU debugger configuration file
*.nvuser

# MFractors (Xamarin productivity tool) working folder 
.mfractor/
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/WorkBookModel.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/SummarySheetDataModel.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/RowDataModel.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/ReportAFetch.xml
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/QueryPlanItems.xml
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/QueryPlan.xml
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/QueryChangesForiChannel.xml
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/QueryChanges.xml
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_udstr.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_twovoteaccount.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_specialfirstlevelchange.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_specialcasechange.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_salesterritoryandpurchaseplanitem.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_salesterritoryandpurchaseplananditems.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_salesterritoryandpurchaseplan.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_provincelevelchange.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_nofirstlevelchange.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_nationallevelchange.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_ichannelconfirmation.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_hospitalpurchaseplanchange.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_district.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_change_base.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_buheadapproval.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_bsfirstlevelchange.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/jjmc_authproductitem.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/I_jjmc_salesterritoryandpurchaseplanitem.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/I_jjmc_salesterritoryandpurchaseplan.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/iChannelPlanResultModel.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/iChannelPlanDetailsResultModel.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/iChannelPlanDetails.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/IChannelPlanBase.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/DealerSheetDataModel.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/D365ReportDataUtil.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/D365BPReportH.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/D365BPReport.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/Changes.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/ChangeAttachmentResultModel.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/CellFormatWrapper.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/CellDataModel.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/BUHeadChangesResultModel.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/BUHeadChangesBase.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/BUHeadChanges.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/BPReportWH208ResultModel.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/BPReportHResultModel.cs
/WebAPI/SharedLibs/Models/Channel/FishflowDealerQuotaChange/account.cs
/WebAPI/WebAPIs/Properties/ServiceDependencies/jjmcsfeextapi - Web Deploy/profile.arm.json
/JJMC.Tools/Build/netcoreapp3.1
