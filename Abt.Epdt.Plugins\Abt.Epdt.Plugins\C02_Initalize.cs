﻿using Microsoft.Xrm.Sdk;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Microsoft.Xrm.Sdk.Query;

namespace Abt.Epdt.Plugins
{
    public class C02_Initalize : IPlugin
    {
        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService service = serviceFactory.CreateOrganizationService(null); // 权限

            ITracingService tracingService = (ITracingService)serviceProvider.GetService(typeof(ITracingService));


            try
            {
                var openid = (string)context.InputParameters["openid"];
                //var wechat_head_avatar = (string)context.InputParameters["wechat_head_avatar"];
                var wechat_name = (string)context.InputParameters["wechat_name"];

                var registertxt = (string)context.InputParameters["register"];
                var patientlisttxt = (string)context.InputParameters["patientlist"];
                var devicelisttxt = (string)context.InputParameters["devicelist"];
                var relationlisttxt = (string)context.InputParameters["relationlist"];

                var registerlist = JsonConvert.DeserializeObject<List<C02_Register>>(registertxt);
                var patientlist = JsonConvert.DeserializeObject<List<C02_PatientInfo>>(patientlisttxt);
                var registerrelationlist = JsonConvert.DeserializeObject<List<C02_RelationRelation>>(relationlisttxt);

                var devicelist = JsonConvert.DeserializeObject<List<C02_DeviceInfo>>(devicelisttxt);

                //查询
                string fetchxml = $@"<fetch xmlns:generator='MarkMpn.SQL4CDS' >
  <entity name='epdt_privacy_policy_terms' >
    <attribute name='epdt_privacy_policy_termsid' />
    <attribute name='epdt_name' />
<attribute name='epdt_term_type' />
    <filter>
      <condition attribute='statuscode' operator='eq' value='6' />
  <condition attribute='epdt_appid' operator='eq' value='EPDT' />
  <condition attribute='epdt_usertype' operator='eq' value='2' />
      <condition attribute='epdt_term_type' operator='in'>
        <value>1</value>
        <value>0</value>
      </condition>
    </filter>
  </entity>
</fetch>";

                FetchExpression sql = new FetchExpression(fetchxml);
                var list = service.RetrieveMultiple(sql);
                Guid policy1 = Guid.Empty;
                Guid policy2 = Guid.Empty;

                foreach (var item in list.Entities)
                {
                    if (item.GetAttributeValue<OptionSetValue>("epdt_term_type").Value == 1)
                    {
                        policy1 = item.Id;//隐私政策
                    }
                    else
                    {
                        policy2 = item.Id;//知情同意书
                    }
                }

                //判断申请人是否存在
                #region 申请人信息处理

                fetchxml = $@"<fetch xmlns:generator='MarkMpn.SQL4CDS'>
  <entity name='epdt_t_wechat_registrant'>
    <attribute name='epdt_t_wechat_registrantid' />
    <filter>
      <condition attribute='epdt_openid' operator='eq' value='{openid}' />
      <condition attribute='statecode' operator='eq' value='0' />
    </filter>
  </entity>
</fetch>";

                var list2 = service.RetrieveMultiple(new FetchExpression(fetchxml));


                var register = registerlist.FirstOrDefault();
                Guid registerid = Guid.Empty;
                var registrant = new Entity("epdt_t_wechat_registrant");
                if (list2.Entities.Count > 0)
                {
                    tracingService.Trace("更新申请人开始");

                    //更新
                    var first = list2.Entities.FirstOrDefault();
                    registerid = first.Id;
                    registrant.Id = registerid;
                    registrant["epdt_name"] = register.RegisterName;
                    registrant["epdt_openid"] = openid;
                    registrant["epdt_informed_consent_auth_date"] = DateTime.Now;
                    registrant["epdt_privacy_auth_date"] = DateTime.Now;
                    registrant["epdt_privacy_auth_status"] = "已授权";
                    //registrant["epdt_wechat_head_avatar"] = wechat_head_avatar;
                    registrant["epdt_wechat_name"] = wechat_name;
                    registrant["epdt_register_phonenumber1"] = register.epdt_register_phonenumber1;
                    registrant["epdt_register_phonenumber"] = register.epdt_register_phonenumber1_;
                    registrant["epdt_register_id_no1"] = register.epdt_register_id_no1;
                    registrant["epdt_register_id_no"] = register.epdt_register_id_no1_;
                    // registrant["statecode"] = 0;
                    registrant["epdt_informed_consent_id"] = new EntityReference("epdt_privacy_policy_terms", policy2);
                    registrant["epdt_policy_term_id"] = new EntityReference("epdt_privacy_policy_terms", policy1);
                    service.Update(registrant);


                    tracingService.Trace("更新申请人结束");

                }
                else
                {
                    //新增
                    tracingService.Trace("创建申请人开始");
                    registrant["epdt_name"] = register.RegisterName;
                    registrant["epdt_openid"] = openid;
                    registrant["epdt_informed_consent_auth_date"] = DateTime.Now;
                    registrant["epdt_privacy_auth_date"] = DateTime.Now;
                    registrant["epdt_privacy_auth_status"] = "已授权";
                    //registrant["epdt_wechat_head_avatar"] = wechat_head_avatar;
                    registrant["epdt_wechat_name"] = wechat_name;

                    registrant["epdt_register_phonenumber1"] = register.epdt_register_phonenumber1;
                    registrant["epdt_register_phonenumber"] = register.epdt_register_phonenumber1_;
                    registrant["epdt_register_id_no1"] = register.epdt_register_id_no1;
                    registrant["epdt_register_id_no"] = register.epdt_register_id_no1_;
                    //  registrant["statecode"] = 0;
                    registrant["epdt_informed_consent_id"] = new EntityReference("epdt_privacy_policy_terms", policy2);
                    registrant["epdt_policy_term_id"] = new EntityReference("epdt_privacy_policy_terms", policy1);
                    registerid = service.Create(registrant);

                    tracingService.Trace("创建申请人结束");
                }




                #endregion



                #region 患者信息处理

                foreach (var item in patientlist)
                {
                    tracingService.Trace("创建患者开始");

                    //判断这个患者是否存在，如果已经存在则不重新创建

                    var fetchxml2 = $@"<fetch xmlns:generator='MarkMpn.SQL4CDS' >
  <entity name='epdt_t_registrant_patient' >
    <link-entity name='epdt_t_patient' to='epdt_patient_abbottid' from='epdt_t_patientid' alias='epdt_t_patient' link-type='outer' >
      <attribute name='epdt_t_patientid' />
    </link-entity>
    <link-entity name='epdt_t_wechat_registrant' to='epdt_registereduser_abbottid' from='epdt_t_wechat_registrantid' alias='epdt_t_wechat_registrant' link-type='outer' />
    <filter>
      <condition attribute='epdt_patient_id' entityname='epdt_t_patient' operator='eq' value='{item.epdt_patient_id}' />
      <condition attribute='epdt_t_wechat_registrantid' entityname='epdt_t_wechat_registrant' operator='eq' value='{registerid}' />
      <condition attribute='statecode' operator='eq' value='0' />
      <condition attribute='statecode' entityname='epdt_t_patient' operator='eq' value='0' />
      <condition attribute='epdt_if_delete_patient' entityname='epdt_t_patient' operator='eq' value='否' />
      <condition attribute='statecode' entityname='epdt_t_wechat_registrant' operator='eq' value='0' />
    </filter>
  </entity>
</fetch>";

                    var existpatientlist = service.RetrieveMultiple(new FetchExpression(fetchxml2));

                    if (existpatientlist.Entities.Count > 0) continue;

                    var patientid = Guid.Empty;
                    var patient = new Entity("epdt_t_patient");
                    patient["epdt_name"] = item.epdt_patient_name;
                    patient["epdt_mailing_address"] = item.epdt_mailing_address;
                    patient["epdt_card_acquisition_method"] = item.epdt_card_acquisition_method;

                    patient["epdt_patient_gender"] = item.epdt_patient_gender;
                    patient["epdt_pitient_phone_no1"] = item.epdt_pitient_phone_no1;
                    patient["epdt_patient_phonenumber"] = item.epdt_pitient_phone_no1_;
                    if (item.epdt_idtype > 0)
                        patient["epdt_idtype"] = new OptionSetValue(item.epdt_idtype);

                    patient["epdt_patient_id"] = item.epdt_patient_id;
                    patient["epdt_patient_id_no"] = item.epdt_patient_id_;

                    patient["epdt_recipient_name"] = item.epdt_recipient_name;
                    patient["epdt_recipient_phone"] = item.epdt_recipient_phone;

                    patient["epdt_if_need_paper_card"] = item.epdt_if_need_paper_card;
                    patient["epdt_if_need_follow_up_reminder_service"] = item.epdt_if_need_follow_up_reminder_service;

                    var qe1 = new QueryExpression("epdt_t_patient");
                    qe1.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    qe1.Criteria.AddCondition("epdt_patient_id", ConditionOperator.Equal, item.epdt_patient_id);
                    qe1.Criteria.AddCondition("epdt_if_delete_patient", ConditionOperator.Equal, "否");
                    var ec1 = service.RetrieveMultiple(qe1);
                    if (ec1 != null && ec1.Entities.Count > 0)
                    {
                        patientid = ec1.Entities[0].Id;
                        patient.Id = ec1.Entities[0].Id;
                        service.Update(patient);
                    }
                    else
                    {
                        patientid = service.Create(patient);
                    }


                    tracingService.Trace("创建患者结束");

                    #region 申请人患者关系

                    foreach (var item2 in registerrelationlist)
                    {
                        tracingService.Trace("创建患者关系开始");

                        var relation = new Entity("epdt_t_registrant_patient");
                        relation["epdt_relationship_with_patient"] = item2.epdt_relationship_with_patient;
                        relation["epdt_other_relationship_with_patient"] = item2.epdt_other_relationship_with_patient;
                        relation["epdt_if_default_display_patient"] = item2.epdt_if_default_display_patient;
                        relation["epdt_patient_abbottid"] = new EntityReference("epdt_t_patient", patientid);
                        relation["epdt_registereduser_abbottid"] = new EntityReference("epdt_t_wechat_registrant", registerid);
                        service.Create(relation);

                        tracingService.Trace("创建患者关系结束");
                    }

                    #endregion

                    #region 植入信息

                    foreach (var item3 in devicelist)
                    {
                        QueryExpression query = new QueryExpression("epdt_t_hospital_basic_mdata")
                        {
                            ColumnSet = new ColumnSet("epdt_hospital_code", "epdt_name", "epdt_t_hospital_basic_mdataid"),
                            NoLock = true,
                            Criteria = { Conditions = { new ConditionExpression("epdt_hospital_code", ConditionOperator.Equal, item3.Hospitalcode) } }
                        };
                        EntityCollection hosCollection = service.RetrieveMultiple(query);
                        if (hosCollection.Entities.Count > 0)
                        {
                            tracingService.Trace("创建植入信息开始");


                            Entity hospital = hosCollection.Entities[0];
                            var device = new Entity("epdt_t_device_application");
                            device["epdt_patient_abbottid"] = new EntityReference("epdt_t_patient", patientid);
                            device["epdt_hospital_code"] = new EntityReference("epdt_t_hospital_basic_mdata", hospital.Id);
                            device["epdt_implant_date"] = Convert.ToDateTime(item3.implant_date);
                            device["epdt_wechat_registrant"] = new EntityReference("epdt_t_wechat_registrant", registerid);

                            //判断是新增还是更新
                            if (!string.IsNullOrWhiteSpace(item3.epdt_t_device_applicationid))
                            {
                                var appdev = service.Retrieve("epdt_t_device_application", new Guid(item3.epdt_t_device_applicationid), new ColumnSet("epdt_check_status"));
                                if (appdev.GetAttributeValue<string>("epdt_check_status") != "匹配成功")
                                {
                                    device["epdt_check_status"] = "待匹配";
                                }
                                device.Id = new Guid(item3.epdt_t_device_applicationid);
                                service.Update(device);
                            }
                            else
                            {
                                device["epdt_check_status"] = "待匹配";
                                service.Create(device);
                            }
                            tracingService.Trace("创建植入信息结束");
                        }

                    }

                    #endregion

                    #region 记录日志

                    tracingService.Trace("创建日志开始");

                    var log1 = new Entity("epdt_privacy_policy_auth_log");
                    log1["epdt_privacy_auth_status1"] = "已授权";
                    log1["epdt_privacy_auth_page1"] = new OptionSetValue(0);

                    log1["epdt_operation_date"] = DateTime.Now;

                    log1["epdt_policy_term_id"] = new EntityReference("epdt_privacy_policy_terms", policy1);
                    log1["epdt_registereduser_abbottid"] = new EntityReference("epdt_t_wechat_registrant", registerid);

                    service.Create(log1);

                    var log2 = new Entity("epdt_privacy_policy_auth_log");
                    log2["epdt_privacy_auth_status1"] = "已授权";
                    log2["epdt_privacy_auth_page1"] = new OptionSetValue(0);

                    log2["epdt_operation_date"] = DateTime.Now;

                    log2["epdt_policy_term_id"] = new EntityReference("epdt_privacy_policy_terms", policy2);
                    log2["epdt_registereduser_abbottid"] = new EntityReference("epdt_t_wechat_registrant", registerid);


                    service.Create(log2);

                    tracingService.Trace("创建日志结束");

                    #endregion
                }
                #endregion

                context.OutputParameters["result"] = registerid.ToString();
            }
            catch (Exception ex)
            {
                tracingService.Trace(ex.Message + ex.StackTrace + ex.StackTrace);
                throw;
            }
        }
    }

}
