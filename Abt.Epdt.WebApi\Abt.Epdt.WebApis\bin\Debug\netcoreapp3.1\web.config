<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" arguments=".\Abt.Epdt.WebApis.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" />
      <staticContent>
        <mimeMap fileExtension=".dat" mimeType="application/octet-stream" />
        <mimeMap fileExtension=".symbols" mimeType="application/octet-stream" />

      </staticContent>
    </system.webServer>
  </location>
</configuration>
