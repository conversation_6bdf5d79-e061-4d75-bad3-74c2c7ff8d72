﻿using System;
using System.Linq;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System.Activities;
using System.Collections.Generic;
using Abbott.Onepdt.Plugins.Models;
using Abbott.Onepdt.Plugins.Service;
using javax.xml.transform;

namespace Abbott.Onepdt.Plugins.Actions
{
    public class PrintIdentifyCardAction : IPlugin
    {
        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService service = serviceFactory.CreateOrganizationService(null);
            ITracingService tracingService = (ITracingService)serviceProvider.GetService(typeof(ITracingService));

            try
            {
                Guid userId = context.UserId;
                string operationId = (string)context.InputParameters["PrintIds"];
                string userName = (string)context.InputParameters["UserId"];
                string[] printIds = operationId.Split(',');

                Dictionary<string, string> warrantyDetails = new Dictionary<string, string>();

                string fetchXml = $@"
                <fetch>
                  <entity name='onepdt_t_patient_id_card'>
                    <all-attributes />
                    <filter>
                      <condition attribute='onepdt_t_patient_id_cardid' operator='in'>
                        {string.Join("", printIds.Select(id => $"<value>{id}</value>"))}
                      </condition>
                    </filter>
                    <link-entity name='onepdt_t_operation' from='onepdt_t_operationid' to='onepdt_t_operation' link-type='inner' alias='onepdt_t_operation'>
                      <attribute name='onepdt_date' />
                      <attribute name='onepdt_gender' />
                      <attribute name='onepdt_hospitalid' />
                      <attribute name='onepdt_hospitalcode' />
                      <attribute name='onepdt_hospitalname' />
                      <attribute name='onepdt_name' />
                      <attribute name='onepdt_patient' />
                      <attribute name='onepdt_hcp_text' />
                      <attribute name='onepdt_primary_hcp' />
                      <attribute name='onepdt_secondary_hcp' />
                      <attribute name='onepdt_recipient_address' />
                      <attribute name='onepdt_recipient_name' />
                      <attribute name='onepdt_recipient_phone' />
                      <attribute name='onepdt_card_acquisition_type' />
                      <link-entity name='epdt_t_hospital_basic_mdata' from='epdt_t_hospital_basic_mdataid' to='onepdt_hospitalid' link-type='outer' alias='onepdt_hospital_basic'>
                        <attribute name='onepdt_address' />
                        <attribute name='onepdt_tel' />
                        <attribute name='epdt_hospital_code' />
                        <attribute name='epdt_name' />
                        <attribute name='epdt_hospital_province' />
                        <attribute name='epdt_hospital_city' />
                        <attribute name='epdt_hospital_districtandcounty' />
                        <attribute name='epdt_t_hospital_basic_mdataid' />
                      </link-entity>
                    <link-entity name='epdt_t_device_application' from='epdt_t_device_applicationid' to='onepdt_device_application' link-type='outer' alias='epdt_t_device_application'>
                      <link-entity name='epdt_t_patient' from='epdt_t_patientid' to='epdt_patient_abbottid' link-type='outer' alias='epdt_t_patient'>
                        <attribute name='epdt_mailing_address' />
                        <attribute name='epdt_recipient_name' />
                        <attribute name='epdt_recipient_phone' />
                      </link-entity>
                    </link-entity>
                    </link-entity>
                    <link-entity name='epdt_t_device_mdata' from='epdt_name' to='onepdt_name' link-type='inner' alias='epdt_t_device_mdata'>
                     <attribute name='epdt_t_device_mdataid' />
                    </link-entity>
                  </entity>
                </fetch>";


                EntityCollection printCards = service.RetrieveMultiple(new FetchExpression(fetchXml));

                EntityCollection sortedPrintCards = new EntityCollection(printCards.Entities.OrderBy(e => Array.IndexOf(printIds, e.GetAttributeValue<Guid>("onepdt_t_patient_id_cardid").ToString())).ToList());

                EntityCollection HospitalBranchMappings = IdentifyCardTypeService.GetHospitalBranchMappings(service);


                //延保政策
                var filteredPrintCards = new EntityCollection(
                    printCards.Entities.Where(e => !e.GetAttributeValue<bool>("onepdt_is_extended_warranty")).ToList()
                );

                if (filteredPrintCards.Entities.Count > 0)
                {
                    warrantyDetails = IdentifyCardTypeService.ProcessExtendedWarranty(service, filteredPrintCards);
                }

                //患者信息公众号注册信息
                var fetchXml1 = $@"
                        <fetch>
                          <entity name='epdt_t_patient'>
                            <attribute name='epdt_mailing_address' />
                            <attribute name='epdt_recipient_name' />
                            <attribute name='epdt_recipient_phone' />
                            <filter>
                              <condition attribute='epdt_card_acquisition_method' operator='eq' value='1' />
                              <condition attribute='epdt_if_need_paper_card' operator='eq' value='1' />
                              <condition attribute='statecode' operator='eq' value='0' />
                              <condition entityname='epdt_t_device_application' attribute='statecode' operator='eq' value='0' />
                              <condition entityname='epdt_t_device_mdata' attribute='epdt_name' operator='in'>
                              {string.Join("", printCards.Entities.Select(e => $"<value>{e.GetAttributeValue<string>("onepdt_name")}</value>"))}
                              </condition>
                            </filter>
                            <link-entity name='epdt_t_device_application' from='epdt_patient_abbottid' to='epdt_t_patientid' link-type='outer' alias='epdt_t_device_application'>
                                 <link-entity name='epdt_t_device_mdata' from='epdt_t_device_mdataid' to='epdt_application_find_main_data' link-type='inner' alias='epdt_t_device_mdata'>
                                     <attribute name='epdt_name' />
                                 </link-entity>
                            </link-entity>
                          </entity>
                        </fetch>";
                EntityCollection patients = service.RetrieveMultiple(new FetchExpression(fetchXml1));

                tracingService.Trace("fetchXml1" + fetchXml1);
                tracingService.Trace("查询患者数：" + patients.Entities.Count);


                List<object> batchParameters = new List<object>();
                int batchSize = 50;
                int count = 0;

                foreach (Entity entity in sortedPrintCards.Entities)
                {

                    var IdentificationCardNumber = entity.GetAttributeValue<string>("onepdt_name");

                    var warrantyDate = entity.GetAttributeValue<string>("onepdt_main_unit1_warranty_period");

                    if (warrantyDetails.ContainsKey(IdentificationCardNumber))
                    {
                        warrantyDate = warrantyDetails[IdentificationCardNumber];
                        // Use warrantyDate as needed
                    }

                    var implantingHospitalId = entity.Contains("onepdt_hospital_basic.epdt_t_hospital_basic_mdataid") ? ((AliasedValue)entity["onepdt_hospital_basic.epdt_t_hospital_basic_mdataid"])?.Value.ToString() : null;
                    var matchingHospital = implantingHospitalId != null ? HospitalBranchMappings.Entities.FirstOrDefault(h => h.GetAttributeValue<EntityReference>("onepdt_implant_hospitalid").Id.ToString() == implantingHospitalId) : null;

                    var implantingHospitalName1 = entity.Contains("onepdt_hospital_basic.epdt_name") ? ((AliasedValue)entity["onepdt_hospital_basic.epdt_name"]).Value?.ToString() : null;
                    var implantingHospitalCode1 = entity.Contains("onepdt_hospital_basic.epdt_hospital_code") ? ((AliasedValue)entity["onepdt_hospital_basic.epdt_hospital_code"]).Value?.ToString() : null;
                    var hospitalAddress1 = entity.Contains("onepdt_hospital_basic.onepdt_address")
                        ? string.Join(" ", new[]
                        {
                                entity.Contains("onepdt_hospital_basic.epdt_hospital_province") ? ((AliasedValue)entity["onepdt_hospital_basic.epdt_hospital_province"])?.Value : null,
                                entity.Contains("onepdt_hospital_basic.epdt_hospital_city") && ((AliasedValue)entity["onepdt_hospital_basic.epdt_hospital_city"])?.Value != ((AliasedValue)entity["onepdt_hospital_basic.epdt_hospital_province"])?.Value ? ((AliasedValue)entity["onepdt_hospital_basic.epdt_hospital_city"])?.Value : null,
                                entity.Contains("onepdt_hospital_basic.epdt_hospital_districtandcounty") ? ((AliasedValue)entity["onepdt_hospital_basic.epdt_hospital_districtandcounty"])?.Value : null,
                                entity.Contains("onepdt_hospital_basic.onepdt_address") ? ((AliasedValue)entity["onepdt_hospital_basic.onepdt_address"])?.Value : null
                        }.Where(v => v != null))
                        : null;
                    var hospitalPhone1 = entity.Contains("onepdt_hospital_basic.onepdt_tel") ? ((AliasedValue)entity["onepdt_hospital_basic.onepdt_tel"]).Value?.ToString() : null;
                    if (matchingHospital != null)
                    {
                        implantingHospitalName1 = matchingHospital.Contains("onepdt_print_hospital.epdt_name") ? ((AliasedValue)matchingHospital["onepdt_print_hospital.epdt_name"]).Value?.ToString() : null;
                        implantingHospitalCode1 = matchingHospital.Contains("onepdt_print_hospital.epdt_hospital_code") ? ((AliasedValue)matchingHospital["onepdt_print_hospital.epdt_hospital_code"]).Value?.ToString() : null;
                        hospitalAddress1 = matchingHospital.Contains("onepdt_print_hospital.onepdt_address")
                        ? string.Join(" ", new[]
                        {
                                  matchingHospital.Contains("onepdt_print_hospital.epdt_hospital_province") ? ((AliasedValue)matchingHospital["onepdt_print_hospital.epdt_hospital_province"])?.Value : null,
                                  matchingHospital.Contains("onepdt_print_hospital.epdt_hospital_city") && ((AliasedValue)matchingHospital["onepdt_print_hospital.epdt_hospital_city"])?.Value != ((AliasedValue)matchingHospital["onepdt_print_hospital.epdt_hospital_province"])?.Value ? ((AliasedValue)matchingHospital["onepdt_print_hospital.epdt_hospital_city"])?.Value : null,
                                  matchingHospital.Contains("onepdt_print_hospital.epdt_hospital_districtandcounty") ? ((AliasedValue)matchingHospital["onepdt_print_hospital.epdt_hospital_districtandcounty"])?.Value : null,
                                  matchingHospital.Contains("onepdt_print_hospital.onepdt_address") ? ((AliasedValue)matchingHospital["onepdt_print_hospital.onepdt_address"])?.Value : null
                        }.Where(v => v != null))
                        : null;
                        hospitalPhone1 = matchingHospital.Contains("onepdt_print_hospital.onepdt_tel") ? ((AliasedValue)matchingHospital["onepdt_print_hospital.onepdt_tel"]).Value?.ToString() : null;
                    }
                    Entity deviceMdata = new Entity("epdt_t_device_mdata", new Guid(entity.Contains("epdt_t_device_mdata.epdt_t_device_mdataid") ? ((AliasedValue)entity["epdt_t_device_mdata.epdt_t_device_mdataid"]).Value?.ToString() : Guid.Empty.ToString()));
                    deviceMdata["epdt_implant_hospital_code"] = implantingHospitalCode1;
                    deviceMdata["epdt_implant_hospital_name"] = implantingHospitalName1;
                    deviceMdata["epdt_hospital_phone"] = hospitalPhone1;
                    deviceMdata["epdt_hospital_address"] = hospitalAddress1;
                    service.Update(deviceMdata);

                    var recipientName0 = "";
                    var recipientName1 = entity.Contains("epdt_t_patient.epdt_recipient_name") ? ((AliasedValue)entity["epdt_t_patient.epdt_recipient_name"]).Value?.ToString() : null;
                    var recipientName2 = entity.Contains("onepdt_t_operation.onepdt_recipient_name") ? ((AliasedValue)entity["onepdt_t_operation.onepdt_recipient_name"]).Value?.ToString() : null;
                    var recipientName3 = entity.GetAttributeValue<string>("onepdt_print_recipient") ?? null;
                    var recipientPhone0 = "";
                    var recipientPhone1 = entity.Contains("epdt_t_patient.epdt_recipient_phone") ? ((AliasedValue)entity["epdt_t_patient.epdt_recipient_phone"]).Value?.ToString() : null;
                    var recipientPhone2 = entity.Contains("onepdt_t_operation.onepdt_recipient_phone") ? ((AliasedValue)entity["onepdt_t_operation.onepdt_recipient_phone"]).Value?.ToString() : null;
                    var recipientPhone3 = entity.GetAttributeValue<string>("onepdt_print_recipient_phone") ?? null;
                    var recipientAddress0 = "";
                    var recipientAddress1 = entity.Contains("epdt_t_patient.epdt_recipient_address") ? ((AliasedValue)entity["epdt_t_patient.epdt_recipient_address"]).Value?.ToString() : null;
                    var recipientAddress2 = entity.Contains("onepdt_t_operation.onepdt_recipient_address") ? ((AliasedValue)entity["onepdt_t_operation.onepdt_recipient_address"]).Value?.ToString() : null;
                    var recipientAddress3 = entity.GetAttributeValue<string>("onepdt_print_recipient_address") ?? null;

                    tracingService.Trace("IdentificationCardNumber:" + IdentificationCardNumber);

                    var patientEntity = patients.Entities.Where(e => e.Contains("epdt_t_device_mdata.epdt_name") && e.GetAttributeValue<AliasedValue>("epdt_t_device_mdata.epdt_name").Value.ToString() == IdentificationCardNumber).FirstOrDefault();

                    if (patientEntity != null)
                    {
                        recipientName0 = patientEntity.GetAttributeValue<string>("epdt_recipient_name") ?? null;
                        recipientPhone0 = patientEntity.GetAttributeValue<string>("epdt_recipient_phone") ?? null;
                        recipientAddress0 = patientEntity.GetAttributeValue<string>("epdt_mailing_address") ?? null;
                    }
                    tracingService.Trace("recipientName0：" + recipientName0 + "------" + recipientPhone0 + "------" + recipientAddress0);
                    tracingService.Trace("recipientName1：" + recipientName1 + "------" + recipientPhone1 + "------" + recipientAddress1);
                    tracingService.Trace("recipientName2：" + recipientName2 + "------" + recipientPhone2 + "------" + recipientAddress2);
                    tracingService.Trace("recipientName3：" + recipientName3 + "------" + recipientPhone3 + "------" + recipientAddress3);

                    var parameters = new
                    {
                        GUID = entity.GetAttributeValue<Guid>("onepdt_t_patient_id_cardid"),
                        surgeryNumber = entity.Contains("onepdt_t_operation.onepdt_name") ? ((AliasedValue)entity["onepdt_t_operation.onepdt_name"])?.Value : null,
                        deviceIdentificationCardNumber = entity.GetAttributeValue<string>("onepdt_name"),
                        patientName = entity.Contains("onepdt_t_operation.onepdt_patient") ? ((AliasedValue)entity["onepdt_t_operation.onepdt_patient"])?.Value : null,
                        patientGender = entity.Contains("onepdt_t_operation.onepdt_gender") ? ((AliasedValue)entity["onepdt_t_operation.onepdt_gender"])?.Value : null,
                        implantationDate = entity.Contains("onepdt_t_operation.onepdt_date") ? ((DateTime)((AliasedValue)entity["onepdt_t_operation.onepdt_date"])?.Value).ToString("yyyy-MM-dd") : null,
                        implantingDoctor = entity.Contains("onepdt_t_operation.onepdt_hcp_text") ? ((AliasedValue)entity["onepdt_t_operation.onepdt_hcp_text"])?.Value : null,
                        implantingHospitalName = implantingHospitalName1,
                        implantingHospitalCode = implantingHospitalCode1,
                        hospitalAddress = hospitalAddress1,
                        hospitalPhone = hospitalPhone1,
                        mainUnitSerialNumber = entity.GetAttributeValue<string>("onepdt_main_unit1_serial_number"),
                        mainUnitModel = entity.GetAttributeValue<string>("onepdt_main_unit1_model"),
                        mainUnitWarrantyPeriod = warrantyDate,
                        mainUnitScanIntensity = entity.GetAttributeValue<string>("onepdt_main_unit1_scan_intensity"),
                        mainUnitSelectedPacingDefibrillationMode = entity.GetAttributeValue<string>("onepdt_main_unit1_selected_pacing_defibrillation_mode"),
                        mainUnitPrimaryPacingDefibrillationMode = entity.GetAttributeValue<string>("onepdt_main_unit1_pacing_defibrillation_mode"),
                        mainUnitManufacturerBrand = entity.GetAttributeValue<string>("onepdt_main_unit1_manufacturer_brand"),
                        warrantyPeriod = entity.GetAttributeValue<string>("onepdt_main_unit1_warranty_period"),
                        electrode1SerialNumber = entity.GetAttributeValue<string>("onepdt_electrode1_serial_number"),
                        electrode1Model = entity.GetAttributeValue<string>("onepdt_electrode1_model"),
                        electrode1ImplantDate = entity.Contains("onepdt_electrode1_implant_date") ? entity.GetAttributeValue<DateTime>("onepdt_electrode1_implant_date").ToString("yyyy-MM-dd") : null,
                        electrode1ManufacturerBrand = entity.GetAttributeValue<string>("onepdt_electrode1_manufacturer_brand"),
                        electrode2SerialNumber = entity.GetAttributeValue<string>("onepdt_electrode2_serial_number"),
                        electrode2Model = entity.GetAttributeValue<string>("onepdt_electrode2_model"),
                        electrode2ImplantDate = entity.Contains("onepdt_electrode2_implant_date") ? entity.GetAttributeValue<DateTime>("onepdt_electrode2_implant_date").ToString("yyyy-MM-dd") : null,
                        electrode2ManufacturerBrand = entity.GetAttributeValue<string>("onepdt_electrode2_manufacturer_brand"),
                        electrode3SerialNumber = entity.GetAttributeValue<string>("onepdt_electrode3_serial_number"),
                        electrode3Model = entity.GetAttributeValue<string>("onepdt_electrode3_model"),
                        electrode3ImplantDate = entity.Contains("onepdt_electrode3_implant_date") ? entity.GetAttributeValue<DateTime>("onepdt_electrode3_implant_date").ToString("yyyy-MM-dd") : null,
                        electrode3ManufacturerBrand = entity.GetAttributeValue<string>("onepdt_electrode3_manufacturer_brand"),
                        electrode4SerialNumber = entity.GetAttributeValue<string>("onepdt_electrode4_serial_number"),
                        electrode4Model = entity.GetAttributeValue<string>("onepdt_electrode4_model"),
                        electrode4ImplantDate = entity.Contains("onepdt_electrode4_implant_date") ? entity.GetAttributeValue<DateTime>("onepdt_electrode4_implant_date").ToString("yyyy-MM-dd") : null,
                        electrode4ManufacturerBrand = entity.GetAttributeValue<string>("onepdt_electrode4_manufacturer_brand"),
                        finalWarrantyCardType = entity.GetAttributeValue<string>("onepdt_final_warranty_card_type"),
                        recipientType = entity.Contains("onepdt_t_operation.onepdt_card_acquisition_type") ? entity.FormattedValues["onepdt_t_operation.onepdt_card_acquisition_type"] : null,
                        recipientName = !string.IsNullOrEmpty(recipientName0) ? recipientName0 : (!string.IsNullOrEmpty(recipientName1) ? recipientName1 : (!string.IsNullOrEmpty(recipientName3) ? recipientName3 : recipientName2)),
                        recipientPhone = !string.IsNullOrEmpty(recipientPhone0) ? recipientPhone0 : (!string.IsNullOrEmpty(recipientPhone1) ? recipientPhone1 : (!string.IsNullOrEmpty(recipientPhone3) ? recipientPhone3 : recipientPhone2)),
                        recipientAddress = !string.IsNullOrEmpty(recipientAddress0) ? recipientAddress0 : (!string.IsNullOrEmpty(recipientAddress1) ? recipientAddress1 : (!string.IsNullOrEmpty(recipientAddress3) ? recipientAddress3 : recipientAddress2)),
                        Status = "未打印",
                        print_count = 0
                    };

                    batchParameters.Add(parameters);
                    count++;

                    if (count == batchSize)
                    {
                        string json = Newtonsoft.Json.JsonConvert.SerializeObject(batchParameters);
                        Entity printLog = new Entity("onepdt_t_print_log");
                        printLog["onepdt_print_type"] = "推送到中间库";
                        printLog["onepdt_content"] = json;
                        printLog["onepdt_print_datetime"] = DateTime.UtcNow;
                        printLog["onepdt_user_name"] = userName;
                        printLog["onepdt_print_content"] = $"共{batchParameters.Count}张识别卡";
                        printLog["createdby"] = new EntityReference("systemuser", userId);

                        service.Create(printLog);

                        batchParameters.Clear();
                        count = 0;
                    }
                }

                if (batchParameters.Count > 0)
                {

                    string json = Newtonsoft.Json.JsonConvert.SerializeObject(batchParameters);
                    Entity printLog = new Entity("onepdt_t_print_log");
                    printLog["onepdt_print_type"] = "推送到中间库";
                    printLog["onepdt_content"] = json;
                    printLog["onepdt_print_datetime"] = DateTime.UtcNow;
                    printLog["onepdt_user_name"] = userName;
                    printLog["onepdt_print_content"] = $"共{batchParameters.Count}张识别卡";
                    printLog["createdby"] = new EntityReference("systemuser", userId);



                    service.Create(printLog);
                }


                //foreach (string printId in printIds)
                //{
                //    Entity printLog = new Entity("onepdt_t_print_log");
                //    printLog["onepdt_print_type"] = "推送到中间库";
                //    printLog["onepdt_content"] = printId;
                //    printLog["onepdt_print_datetime"] = DateTime.UtcNow;
                //    printLog["onepdt_user_name"] = userName;
                //    service.Create(printLog);
                //}



            }
            catch (Exception ex)
            {
                tracingService.Trace(ex.Message + ex.StackTrace);
                throw;
            }
        }
    }
}
