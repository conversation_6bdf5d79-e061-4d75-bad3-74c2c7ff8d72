﻿using Abt.Epdt.WebApis.Command;
using Abt.Epdt.WebApis.Model;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Generic;
using System.Drawing.Printing;
using System.IO;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Xml.Linq;
using static Abt.Epdt.WebApis.Model.CardTypeModel;

namespace Abt.Epdt.WebApis.Controller
{
    [ApiController]
    [Route("api/epdt")]
    public class AppController : ControllerBase
    {
        private readonly IMemoryCache _memoryCache;
        private readonly AppCommand app;

        public AppController(IMemoryCache memoryCache)
        {
            _memoryCache = memoryCache;
            app = new AppCommand(_memoryCache);
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="id">用户id</param>
        /// <returns></returns>
        //[AllowAnonymous]
        [HttpPost]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("GetUserInfo")]
        public string GetUserInfo([FromForm] string id)
        {
            return app.GetUserInfo();
        }

        /// <summary>
        /// 检验用户邮箱并获取角色
        /// </summary>
        /// <param name="email">邮箱</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        //[AllowAnonymous]
        [HttpGet]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("LoginAndGetRole")]
        public IActionResult LoginAndGetRole(string email)
        {
            try
            {
                return Ok(app.LoginAndGetRole(User.FindFirst(ClaimTypes.Email).Value));
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 获取待办列表
        /// </summary>
        /// <param name="email">邮箱</param>
        /// <param name="type">待办类型</param>
        /// <param name="pagesize">分页大小</param>
        /// <param name="pageindex">分页索引</param>
        /// <param name="searchText">搜索内容</param>
        /// <param name="implantType">植入类型</param>
        /// <param name="status">状态</param>
        /// <param name="hospital">医院</param>
        /// <param name="starttime">植入开始时间</param>
        /// <param name="endtime">植入结束时间</param>
        /// <returns></returns>
        /// <exception cref="System.Exception"></exception>
        //[AllowAnonymous]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [HttpGet]
        [Route("GetTodoList")]
        public IActionResult GetTodoList(string email, int type, int pagesize, int pageindex, string searchText, string implantType, string status, string hospital, string starttime, string endtime)
        {
            try
            {

                return Ok(app.GetTodoList(User.FindFirst(ClaimTypes.Email).Value, type, pagesize, pageindex, searchText, implantType, status, hospital, starttime, endtime));
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 获取跟台配置
        /// </summary>
        /// <param name="tablename">配置表名</param>
        /// <param name="fieldname">配置字段名</param>
        /// <returns></returns>
        /// <exception cref="System.Exception"></exception>
        //[AllowAnonymous]
        [HttpGet]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("GetOnepdtConfig")]
        [ResponseCache(Duration = 600, Location = ResponseCacheLocation.Client)]
        public IActionResult GetOnepdtConfig(string tablename, string fieldname)
        {
            try
            {
                return Ok(app.GetOnepdtConfig(tablename, fieldname));
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 获取电子保卡
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        //[AllowAnonymous]
        [HttpGet]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("GetDapplications")]
        public IActionResult GetDapplications(string email, string operationid)
        {
            try
            {
                return Ok(app.GetDapplications(User.FindFirst(ClaimTypes.Email).Value, operationid));
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 获取医院信息
        /// </summary>
        /// <returns></returns>
        //[AllowAnonymous]
        [HttpGet]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("GetHospitals")]
        public IActionResult GetHospitals(string email)
        {
            try
            {
                return Ok(app.GetHospitals(User.FindFirst(ClaimTypes.Email).Value));
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 获取医生信息
        /// </summary>
        /// <param name="isout">是否外部医生</param>
        /// <param name="hospitalcode">医院编码</param>
        /// <returns></returns>
        /// <exception cref="System.Exception"></exception>
        //[AllowAnonymous]
        [HttpGet]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("GetHCPs")]
        public IActionResult GetHCPs(string email, bool isout, string hospitalcode)
        {
            try
            {
                return Ok(app.GetHCPs(User.FindFirst(ClaimTypes.Email).Value, isout, hospitalcode));
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 跟台数据编辑
        /// </summary>
        /// <param name="model">跟台数据Model</param>
        /// <returns></returns>
        /// <exception cref="System.Exception"></exception>
        //[AllowAnonymous]
        [HttpPost]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("EditOperation")]
        public IActionResult EditOperation([FromBody] OperationEditData model)
        {
            try
            {
                return Ok(app.EditOperation("", model));
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 根据产品序列号SAP产品
        /// </summary>
        /// <param name="name">序列号</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        //[AllowAnonymous]
        [HttpGet]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("GetSAPProduct")]
        public IActionResult GetSAPProduct(string name, bool isNonMainlandProduct)
        {
            try
            {
                return Ok(app.GetSAPProduct(name, isNonMainlandProduct));
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 根据产品序列号获取产品
        /// </summary>
        /// <param name="name">序列号</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        //[AllowAnonymous]
        [HttpGet]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("GetOnePDTProduct")]
        public IActionResult GetOnePDTProduct(string name)
        {
            try
            {
                return Ok(app.GetOnePDTProduct(name));
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 获取跟台数据详情
        /// </summary>
        /// <param name="operationid">跟台数据id</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        //[AllowAnonymous]
        [HttpGet]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("GetOperation")]
        public IActionResult GetOperation(string operationid, string email)
        {
            try
            {
                return Ok(app.GetOperation(operationid, User.FindFirst(ClaimTypes.Email).Value));
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 获取跟台图片
        /// </summary>
        /// <param name="id">跟台图片id</param>
        /// <returns></returns>
        [HttpGet]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("GetPhoto")]
        public IActionResult GetPhoto(string id)
        {
            try
            {
                return Ok(app.GetPhoto(id));
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 提交跟台
        /// </summary>
        /// <param name="operationid">跟台数据id</param>
        //[AllowAnonymous]
        [HttpGet]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("SubmitOperation")]
        public IActionResult SubmitOperation(string operationid)
        {
            try
            {
                app.SubmitOperation(operationid);
                return Ok();
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="entityname">实体名</param>
        /// <param name="entityid">实体id</param>
        //[AllowAnonymous]
        [HttpGet]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("Delete")]
        public IActionResult Delete(string entityname, string entityid)
        {
            try
            {
                app.Delete(entityname, entityid);
                return Ok();
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="model">删除对象</param>
        //[AllowAnonymous]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [HttpPost]
        [Route("BatchDelete")]
        public IActionResult BatchDelete([FromBody] BatchDeleteModel model)
        {
            try
            {
                app.BatchDelete(model);
                return Ok();
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 预览保卡
        /// </summary>
        /// <param name="operationid">跟台数据id</param>
        /// <param name="IsPreview">是否预览</param>
        /// <returns></returns>
        //[AllowAnonymous]
        [HttpGet]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("GetCardTypes")]
        public IActionResult GetCardTypes(string operationid, bool IsPreview)
        {
            try
            {
                return Ok(app.GetCardTypes(operationid, IsPreview));
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 跟台查询
        /// </summary>
        /// <param name="roleid">角色id</param>
        /// <param name="proxyemail">代理邮箱</param>
        /// <param name="email">邮箱</param>
        /// <param name="pagesize">分页大小</param>
        /// <param name="pageindex">分页索引</param>
        /// <param name="searchText">搜索框</param>
        /// <param name="productname">主机</param>
        /// <param name="submittername">填报人</param>
        /// <param name="province">省份</param>
        /// <param name="implantType">植入类型</param>
        /// <param name="starttime">植入时间起</param>
        /// <param name="endtime">植入时间止</param>
        /// <returns></returns>
        //[AllowAnonymous]
        [HttpGet]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("GetOperationQuery")]
        public IActionResult GetOperationQuery(string roleid, string proxyemail, string email, int pagesize, int pageindex, string searchText, string productname, string submittername, string province, string implantType, string starttime, string endtime)
        {
            try
            {
                var res = app.GetOperationQuery(roleid, proxyemail, User.FindFirst(ClaimTypes.Email).Value, pagesize, pageindex, searchText, productname, submittername, province, implantType, starttime, endtime);
                return Ok(res);
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 上传图片
        /// </summary>
        /// <param name="operationid">跟台id</param>
        /// <param name="photoid">图片id</param>
        /// <param name="classify">图片类型</param>
        /// <param name="file">文件</param>
        /// <returns></returns>
        //[AllowAnonymous]
        [HttpPost]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("upload")]
        public async Task<IActionResult> UploadFileAsync([FromForm] string operationid, [FromForm] string photoid, [FromForm] string classify, IFormFile file)
        {
            try
            {
                var fileid = await app.UploadFileAsync(operationid, photoid, classify, file);
                return Ok(fileid);
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 获取参数文件
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        //[Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("GetConfigFile")]
        public IActionResult GetConfigFile(string id)
        {
            try
            {
                var base64 = app.GetConfigFile(id);
                //return Ok(base64);
                //return Ok($"data:image/jpeg;base64,{base64}" );
                return File(Convert.FromBase64String(base64), "image/png", "使用说明.png");
                //return Ok(Convert.FromBase64String(base64));

            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        /// <summary>
        /// 获取用户使用说明
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        //[Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("GetUserManual")]
        public IActionResult GetUserManual()
        {
            try
            {
                var base64 = app.GetUserManual();
                return File(Convert.FromBase64String(base64), "application/pdf", "使用说明.pdf");
                //return Ok(Convert.FromBase64String(base64));

            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 跟台数据校验
        /// </summary>
        /// <returns></returns>
        //[AllowAnonymous]
        [HttpPost]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("CkeckOperation")]
        public IActionResult CkeckOperation([FromBody] OperationEditData model)
        {
            try
            {
                var tip = app.CkeckOperation(model);
                return Ok(tip);
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 切换角色
        /// </summary>
        /// <param name="userid">员工id</param>
        /// <param name="roleid">角色id</param>
        /// <returns></returns>
        [HttpGet]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("SwitchRole")]
        public IActionResult SwitchRole(string userid, string roleid)
        {
            try
            {
                return Ok(app.SwitchRole(userid, roleid, User.FindFirst(ClaimTypes.Email).Value));
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// 查询历史跟台
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [Route("GetHistoryOperation")]
        public IActionResult GetHistoryOperation()
        {
            try
            {
                return Ok(app.GetHistoryOperation());
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
