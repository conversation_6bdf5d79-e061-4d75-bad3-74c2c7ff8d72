using Abt.SharedLibs;
using Abt.SharedLibs.Models;
using Azure;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Polly;
using Polly.Retry;
//using RestSharp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Linq;
using TencentCloud.Tbp.*********.Models;
using static Azure.Core.HttpHeader;

namespace Abt.SharedLibs
{
    /// <summary>
    /// 在被引用的Project的launchSettings.json中, 添加配置信息: D365TenantId / D365BaseUri / D365ResourceUri / D365CloudInstance / D365ClientId / D365ClientSecret
    /// 在部署到App Service时
    /// 1. 在Application Settings中添加配置信息: D365TenantId / D365BaseUri / D365ResourceUri / D365CloudInstance 
    /// 2. 在KeyVault中添加配置信息: D365ClientId / D365ClientSecret
    /// </summary>
    public class D365Client : ClientBaseClass<D365Client>
    {
        //public static readonly List<string> AllowedDomains = new List<string>
        //{
        //    "abbott.com.cn",
        //    "api.weixin.qq.com",
        //    "localhost"
        //};

        #region Fields & Properties
        private readonly HttpClient m_Client;
        //private readonly RestClient m_Client;
        //private readonly string m_BaseUri;
        #endregion

        #region Constructor & Initilization
        public D365Client(ConfigurationClient configuration, ILogger<D365Client> log, HttpClient client)
            : base(configuration, log)
        {
            m_Client = client;
            //m_BaseUri = configuration.D365BaseUri; // 获取 Base URI
            //m_Client = new RestClient(m_BaseUri);

            InitClient();
        }
        private void InitClient()
        {
            m_Client.BaseAddress = new Uri(m_ConfigurationClient.D365BaseUri);
            m_Client.Timeout = new TimeSpan(0, 2, 0);  //2 minutes
            m_Client.DefaultRequestHeaders.Add("OData-MaxVersion", "4.0");
            m_Client.DefaultRequestHeaders.Add("OData-Version", "4.0");
            m_Client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(ConstantValues.JsonMediaType));
            m_Client.DefaultRequestHeaders.Add("Prefer", "odata.include-annotations=*");

            //// 创建一个默认请求，用于全局添加请求头
            //var defaultHeaders = new
            //{
            //    OData_MaxVersion = "4.0",
            //    OData_Version = "4.0",
            //    Accept = ConstantValues.JsonMediaType,  // 替换为常量值
            //    Prefer = "odata.include-annotations=*"
            //};

            //// 为每个请求添加请求头
            //m_Client.AddDefaultHeader("OData-MaxVersion", defaultHeaders.OData_MaxVersion);
            //m_Client.AddDefaultHeader("OData-Version", defaultHeaders.OData_Version);
            //m_Client.AddDefaultHeader("Accept", defaultHeaders.Accept);
            //m_Client.AddDefaultHeader("Prefer", defaultHeaders.Prefer);
            RefreshToken();
        }


        #endregion

        #region Access Token 
        private RetryPolicy<HttpResponseMessage> CreateTokenRefreshPolicy()
        {
            return Policy
                .HandleResult<HttpResponseMessage>(message => message.StatusCode == HttpStatusCode.Unauthorized)
                .Retry(1, (result, retryCount, context) =>
                {
                    RefreshToken();
                });
        }
        //private Policy<RestResponse> CreateTokenRefreshPolicy()
        //{
        //    return Policy
        //        .HandleResult<RestResponse>(response => response.StatusCode == HttpStatusCode.Unauthorized)
        //        .Retry(1, (result, retryCount, context) =>
        //        {
        //            // 刷新 token 的逻辑
        //            RefreshToken();
        //        });
        //}

        private void RefreshToken()
        {
            AzureCloudInstance cloudInstance = (AzureCloudInstance)Enum.Parse(typeof(AzureCloudInstance), m_ConfigurationClient.D365CloudInstance);
            var resourceUri = m_ConfigurationClient.D365ResourceUri;
            if (!resourceUri.EndsWith(".default"))
            {
                if (!resourceUri.EndsWith("/"))
                {
                    resourceUri += "/";
                }
                resourceUri += ".default";
            }
            var publicClient = ConfidentialClientApplicationBuilder
              .Create(m_ConfigurationClient.D365ClientIdWebAPI)
              .WithClientSecret(m_ConfigurationClient.D365ClientSecretWebAPI)
              .WithAuthority(cloudInstance, m_ConfigurationClient.D365TenantId)
              .Build();

            var authenticationResult = publicClient.AcquireTokenForClient(new List<string>() { resourceUri }).ExecuteAsync().Result;
            string m_AccessToken = authenticationResult.AccessToken;

            m_Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", m_AccessToken);
        }

        //private void RefreshToken()
        //{
        //    // 根据配置中的 D365 云实例获取 AzureCloudInstance 枚举
        //    AzureCloudInstance cloudInstance = (AzureCloudInstance)Enum.Parse(typeof(AzureCloudInstance), m_ConfigurationClient.D365CloudInstance);
        //    var resourceUri = m_ConfigurationClient.D365ResourceUri;

        //    // 确保 resourceUri 以 ".default" 结尾
        //    if (!resourceUri.EndsWith(".default"))
        //    {
        //        if (!resourceUri.EndsWith("/"))
        //        {
        //            resourceUri += "/";
        //        }
        //        resourceUri += ".default";
        //    }

        //    // 使用机密客户端应用程序构建认证请求
        //    var publicClient = ConfidentialClientApplicationBuilder
        //        .Create(m_ConfigurationClient.D365ClientIdWebAPI)
        //        .WithClientSecret(m_ConfigurationClient.D365ClientSecretWebAPI)
        //        .WithAuthority(cloudInstance, m_ConfigurationClient.D365TenantId)
        //        .Build();

        //    // 获取并缓存 token
        //    var authenticationResult = publicClient.AcquireTokenForClient(new List<string> { resourceUri }).ExecuteAsync().Result;
        //    string accessToken = authenticationResult.AccessToken;

        //    // 设置 RestClient 的 Authorization 头
        //    m_Client.AddDefaultHeader("Authorization", $"Bearer {accessToken}");
        //}
        #endregion

        private const int MAX_RETRIES = 10;//在出现429错误时, 最多的重试次数, 每次间隔延长为之前间隔的2倍时间

        #region 基础方法, 类内部使用
        /// <summary>
        /// 调用D365 WebApi的基础方法
        /// </summary>
        /// <param name="targetUri">不需要完整的Uri, 例如调用WhoAmI()方法, 只需要传入"WhoAmI()"</param>
        /// <param name="httpMethod"></param>
        /// <param name="content">可以不传入</param>
        /// <param name="requestHeaders">Header信息, 可以不传入, 就是NULL, Dictionary类型, key是HeaderName, value是HeaderValue</param>
        /// <returns>简化处理, 返回StringBuilder</returns>
        /// 可能发生的错误是HttpException
        private async Task<StringBuilder> InvokeAPI(string targetUri, HttpMethod httpMethod, HttpContent content = null, int retryCount = 0,
            bool bypassCustomPluginExecution = false)
        {
            var policy = CreateTokenRefreshPolicy();
            var resp = policy.Execute(() =>
                {
                    var requestMessage = new HttpRequestMessage(httpMethod, targetUri);
                    requestMessage.Headers.Add("MSCRM.BypassCustomPluginExecution", $"{bypassCustomPluginExecution}");

                    if (content != null)
                    {
                        requestMessage.Content = content;
                    }
                    return m_Client.SendAsync(requestMessage, HttpCompletionOption.ResponseHeadersRead).Result;
                }
            );

            if (resp.IsSuccessStatusCode)
            {
                return new StringBuilder(await resp.Content.ReadAsStringAsync());
            }
            else
            {
                if (resp.StatusCode == HttpStatusCode.TooManyRequests)
                {
                    if (++retryCount >= MAX_RETRIES)
                    {
                        throw new HttpRequestException($"429错误(Too Many Request), D365访问");
                    }
                    int seconds;
                    if (resp.Headers.Contains("Retry-After"))
                    {
                        seconds = int.Parse(resp.Headers.GetValues("Retry-After").FirstOrDefault());
                    }
                    else
                    {
                        seconds = (int)Math.Pow(2, retryCount);
                    }
                    Thread.Sleep(TimeSpan.FromSeconds(seconds));
                    return await InvokeAPI(targetUri, httpMethod, content, retryCount);
                }
                throw new HttpRequestException(resp.Content.ReadAsStringAsync().Result);
            }
        }
        //private async Task<StringBuilder> InvokeAPI(string targetUri, Method httpMethod, HttpContent content = null, int retryCount = 0,
        //    bool bypassCustomPluginExecution = false)
        //{
        //    var policy = CreateTokenRefreshPolicy();
        //    var resp = policy.Execute(() => { var request = new RestRequest(targetUri, httpMethod); request.AddHeader("MSCRM.BypassCustomPluginExecution", $"{bypassCustomPluginExecution}"); if (content != null) { request.AddParameter("application/json", content.ReadAsStringAsync().Result, ParameterType.RequestBody); } return m_Client.Execute(request); });

        //    if (resp.IsSuccessful)
        //    {
        //        return new StringBuilder(resp.Content);
        //    }
        //    else
        //    {
        //        if (resp.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
        //        {
        //            if (++retryCount >= MAX_RETRIES)
        //            {
        //                throw new HttpRequestException($"429错误(Too Many Request), D365访问");
        //            }

        //            int seconds;
        //            var retryAfterHeader = resp.Headers.FirstOrDefault(header => header.Name.Equals("Retry-After", StringComparison.OrdinalIgnoreCase));
        //            if (retryAfterHeader != null && int.TryParse(retryAfterHeader.Value.ToString(), out seconds))
        //            {
        //                //seconds = int.Parse(resp.Headers.GetValues("Retry-After").FirstOrDefault());
        //            }
        //            else
        //            {
        //                // 默认的退避策略
        //                seconds = (int)Math.Pow(2, retryCount);
        //            }

        //            await Task.Delay(TimeSpan.FromSeconds(seconds));  // 异步等待
        //            return await InvokeAPI(targetUri, httpMethod, content, retryCount, bypassCustomPluginExecution);
        //        }

        //        throw new HttpRequestException(resp.Content);
        //    }
        //}
        private static StringContent BuildJsonContent(Object jObject)
        {
            return new StringContent(JsonConvert.SerializeObject(jObject), Encoding.UTF8, ConstantValues.JsonMediaType);
        }

        #endregion

        #region webapi function
        public async Task<string> RetrieveMultipleByFilter(string entitySetName, string filter)
        {
            string targetUri = !String.IsNullOrEmpty(filter) ? entitySetName + filter : entitySetName;
            var httpMethod = HttpMethod.Get; //HttpMethod.Get;

            try
            {
                var responseMessage = await InvokeAPI(targetUri, httpMethod);

                return responseMessage.ToString();
            }
            catch (Exception excp)
            {
                m_Logger.LogError(excp, excp.Message);
                throw;
            }
        }
        public async Task<D365RetrieveMultipleResponseModel<T>> RetrieveMultipleByFilter<T>(string entitySetName, string filter)
        {
            string targetUri = !String.IsNullOrEmpty(filter) ? entitySetName + filter : entitySetName;

            var httpMethod = HttpMethod.Get; //HttpMethod.Get;

            try
            {
                var responseMessage = await InvokeAPI(targetUri, httpMethod);
                var retResult = JsonConvert.DeserializeObject<D365RetrieveMultipleResponseModel<T>>(responseMessage.ToString());

                return retResult;
            }
            catch (Exception excp)
            {
                m_Logger.LogError(excp, excp.Message);
                throw;
            }
        }
        public async Task UpdateRecord(string entityId, object entityObject, string entitySetName)
        {
            string targetUri = $"{entitySetName}({entityId.Replace("{", "").Replace("}", "")})";
            var httpMethod = HttpMethod.Patch; //new HttpMethod("PATCH");
            var content = BuildJsonContent(entityObject);

            try
            {
                await InvokeAPI(targetUri, httpMethod, content);
            }
            catch (Exception excp)
            {
                m_Logger.LogError(excp, excp.Message);
                throw;
            }

            return;
        }

        public async Task<StringBuilder> UpdateRecordWithStatus(string entityId, object entityObject, string entitySetName)
        {
            string targetUri = $"{entitySetName}({entityId.Replace("{", "").Replace("}", "")})";
            var httpMethod = HttpMethod.Patch; //new HttpMethod("PATCH");
            var content = BuildJsonContent(entityObject);

            StringBuilder responseMessage = new StringBuilder();
            try
            {
                responseMessage = await InvokeAPI(targetUri, httpMethod, content);
            }
            catch (Exception excp)
            {
                throw;
            }

            return responseMessage;
        }

        public async Task<Guid> CreateRecord(object entityRecord, string entitySetName, int retryCount = 0)
        {
            string targetUri = entitySetName;
            var httpMethod = HttpMethod.Post;
            var content = BuildJsonContent(entityRecord);

            var policy = CreateTokenRefreshPolicy();
            var resp = policy.Execute(() =>
            {
                var requestMessage = new HttpRequestMessage(httpMethod, targetUri);

                if (content != null)
                {
                    requestMessage.Content = content;
                }
                return m_Client.SendAsync(requestMessage, HttpCompletionOption.ResponseHeadersRead).Result;
            }
            );

            if (resp.IsSuccessStatusCode)
            {
                var returnUri = resp.Headers.GetValues("OData-EntityId").FirstOrDefault();
                if (returnUri == null)
                {
                    throw new ArgumentNullException(nameof(returnUri));
                }
                return new Guid(returnUri.Substring(returnUri.Length - 37, 36));
            }
            else
            {
                if (resp.StatusCode == HttpStatusCode.TooManyRequests)
                {
                    if (++retryCount >= MAX_RETRIES)
                    {
                        throw new HttpRequestException($"429错误(Too Many Request), D365访问");
                    }
                    int seconds;
                    if (resp.Headers.Contains("Retry-After"))
                    {
                        seconds = int.Parse(resp.Headers.GetValues("Retry-After").FirstOrDefault());
                    }
                    else
                    {
                        seconds = (int)Math.Pow(2, retryCount);
                    }
                    Thread.Sleep(TimeSpan.FromSeconds(seconds));
                }

                return await CreateRecord(entityRecord, entitySetName, retryCount);
            }
        }
        //public async Task<Guid> CreateRecord(object entityRecord, string entitySetName, int retryCount = 0)
        //{
        //    // 构建目标 URI
        //    string targetUri = entitySetName;

        //    // 构建请求体内容
        //    var content = BuildJsonContent(entityRecord);            

        //    // 创建重试策略
        //    var policy = CreateTokenRefreshPolicy();

        //    // 使用 RestSharp 发送请求
        //    var response = policy.Execute(() =>
        //    {
        //        // 创建请求方法
        //        var request = new RestRequest(targetUri, Method.Post);

        //        if (content != null)
        //        {
        //            request.AddParameter("application/json", content.ReadAsStringAsync().Result, ParameterType.RequestBody);
        //            //request.AddParameter("application/json", content, ParameterType.RequestBody);
        //            // 设置请求体为 JSON 格式
        //            //request.AddJsonBody(entityRecord);
        //        }

        //        return m_Client.Execute(request);
        //    });

        //    // 如果请求成功，处理返回值
        //    if (response.IsSuccessful)
        //    {
        //        var returnUri = response.Headers.FirstOrDefault(header => header.Name.Equals("OData-EntityId", StringComparison.OrdinalIgnoreCase))?.Value.ToString();
        //        if (returnUri == null)
        //        {
        //            throw new ArgumentNullException(nameof(returnUri));
        //        }

        //        // 提取 GUID
        //        return new Guid(returnUri.Substring(returnUri.Length - 37, 36));
        //    }
        //    else
        //    {
        //        // 如果返回的是 429 错误，进行重试
        //        if (response.StatusCode == HttpStatusCode.TooManyRequests)
        //        {
        //            if (++retryCount >= MAX_RETRIES)
        //            {
        //                throw new HttpRequestException($"429错误(Too Many Requests), D365访问");
        //            }

        //            int seconds;
        //            var retryAfterHeader = response.Headers.FirstOrDefault(header => header.Name.Equals("Retry-After", StringComparison.OrdinalIgnoreCase));
        //            if (retryAfterHeader != null && int.TryParse(retryAfterHeader.Value.ToString(), out seconds))
        //            {
        //                Thread.Sleep(TimeSpan.FromSeconds(seconds));
        //            }
        //            else
        //            {
        //                seconds = (int)Math.Pow(2, retryCount);
        //                Thread.Sleep(TimeSpan.FromSeconds(seconds));
        //            }
        //        }

        //        // 递归重试
        //        return await CreateRecord(entityRecord, entitySetName, retryCount);
        //    }
        //}

        public async Task<JArray> RetrieveMultiplePageing(string uri, string count, JArray jArray)
        {
            var result = "";
            HttpRequestMessage requestMessage = new HttpRequestMessage()
            {
                Method = HttpMethod.Get,
                RequestUri = new Uri(uri)
            };

            HttpResponseMessage response = await m_Client.SendAsync(requestMessage);

            result = await response.Content.ReadAsStringAsync();

            JObject jObject = (JObject)JsonConvert.DeserializeObject(result.ToString());

            if (((JArray)jObject["value"]).Count > 0)
            {
                var newArray = JArray.Parse(jObject.GetValue("value").ToString());

                jArray.Merge(newArray);
            }

            //最多支持查询20000条
            if (jArray.Count > 50000)
                return jArray;


            if (jObject.ContainsKey("@odata.nextLink"))
            {
                await RetrieveMultiplePageing(jObject.GetValue("@odata.nextLink").ToString(), count, jArray);
            }

            return jArray;
        }
        //public async Task<JArray> RetrieveMultiplePageing(string uri, string count, JArray jArray)
        //{
        //    // 创建 RestRequest 并设置目标 URI 和方法
        //    var request = new RestRequest(uri, Method.Get);

        //    // 发送请求并获取响应
        //    var response = await m_Client.ExecuteAsync(request);

        //    if (!response.IsSuccessful)
        //    {
        //        throw new HttpRequestException($"Failed to retrieve data. Status code: {response.StatusCode}, Message: {response.Content}");
        //    }

        //    // 解析响应的内容
        //    JObject jObject = JObject.Parse(response.Content);

        //    // 如果有数据，合并到传入的 jArray 中
        //    if (jObject["value"] is JArray newArray && newArray.Count > 0)
        //    {
        //        jArray.Merge(newArray);
        //    }

        //    // 最多支持查询 20000 条
        //    if (jArray.Count > 50000)
        //    {
        //        return jArray;
        //    }

        //    // 如果有 @odata.nextLink，递归获取下一页数据
        //    if (jObject.ContainsKey("@odata.nextLink"))
        //    {
        //        var nextLink = jObject["@odata.nextLink"].ToString();
        //        return await RetrieveMultiplePageing(nextLink, count, jArray);
        //    }

        //    return jArray;
        //}

        public async Task<string> RetrieveRecord(string entityId, string entitySetName, string xName)
        {
            try
            {
                string targetUri = $"{entitySetName}({entityId.Replace("{", "").Replace("}", "")})";
                var httpMethod = HttpMethod.Get; //HttpMethod.Get;
                var reponseMessage = await InvokeAPI(targetUri, httpMethod);

                return reponseMessage.ToString();
            }
            catch (Exception excp)
            {
                m_Logger.LogError(excp, excp.Message);
                throw;
            }
        }

        public async Task<T> RetrieveRecord<T>(string entityId, string entitySetName, string xName)
        {
            string targetUri = $"{entitySetName}({entityId.Replace("{", "").Replace("}", "")})";
            var httpMethod = HttpMethod.Get; //HttpMethod.Get;

            try
            {
                var responseMessage = await InvokeAPI(targetUri, httpMethod);

                return JsonConvert.DeserializeObject<T>(responseMessage.ToString());
            }
            catch (Exception excp)
            {
                m_Logger.LogError(excp, excp.Message);
                throw;
            }
        }

        public async Task<string> RetrieveRecordByFechXml(string entitySetName, string fetchxml, bool bypassCustomPluginExecution = false)
        {
            string targetUri = $"{entitySetName}?fetchXml={Uri.EscapeUriString(fetchxml)}";
            var httpMethod = HttpMethod.Get; // HttpMethod.Get;
            try
            {
                var reponseMessage = await InvokeAPI(targetUri, httpMethod, null, 0, bypassCustomPluginExecution);

                return reponseMessage.ToString();
            }
            catch (Exception excp)
            {
                m_Logger.LogError(excp, excp.Message);
                throw;
            }
        }

        public async Task<D365RetrieveMultipleResponseModel<T>> RetrieveRecordByFechXml<T>(string entitySetName, string fetchxml, bool bypassCustomPluginExecution = false)
        {
            string targetUri = String.IsNullOrEmpty(fetchxml) ? entitySetName : $"{entitySetName}?fetchXml={Uri.EscapeDataString(fetchxml)}";
            var httpMethod = HttpMethod.Get; //HttpMethod.Get;

            try
            {
                var responseMessage = await InvokeAPI(targetUri, httpMethod, null, 0, bypassCustomPluginExecution);
                var retResult = JsonConvert.DeserializeObject<D365RetrieveMultipleResponseModel<T>>(responseMessage.ToString());

                return retResult;
            }
            catch (Exception excp)
            {
                m_Logger.LogError(excp, excp.Message);
                throw;
            }
        }

        public async Task<List<T>> RetrieveAllByFetchXml<T>(string entitySetName, string fetchXml)
        {
            int pageNo = 1;
            int defaultPageSize = 5000;

            var result = new List<T>();
            var xmlDoc = XDocument.Parse(fetchXml);
            var fetchNode = xmlDoc.Element("fetch");
            if (fetchNode == null)
            {
                throw new ArgumentNullException($"查询字符串{fetchXml}非法, 没有定位到fetch节点");
            }

            while (true)
            {
                SetAttributeNode("count", defaultPageSize.ToString(), fetchNode);
                SetAttributeNode("page", pageNo.ToString(), fetchNode);
                pageNo++;

                fetchXml = xmlDoc.ToString();

                var fetchXmlResult = await this.RetrieveRecordByFechXml<T>(entitySetName, fetchXml).ConfigureAwait(false);
                result.AddRange(fetchXmlResult.Value);

                if (fetchXmlResult.Value.Count != defaultPageSize)
                {
                    break;
                }
            }

            return result;

            void SetAttributeNode(string attrName, string attrValue, XElement elementNode)
            {
                var targetAttr = elementNode.Attribute(attrName);
                if (targetAttr == null)
                {
                    elementNode.Add(new XAttribute(attrName, attrValue));
                }
                else
                {
                    targetAttr.Value = attrValue;
                }
            }
        }

        public async Task<string> RetrieveMultipleByLink(string link)
        {
            string result = "";
            try
            {
                HttpRequestMessage httpRequestMessage = new HttpRequestMessage()
                {

                    Method = HttpMethod.Get,
                    RequestUri = new Uri(link)
                };

                HttpResponseMessage response = await m_Client.SendAsync(httpRequestMessage);

                result = await response.Content.ReadAsStringAsync();
            }
            catch (Exception excp)
            {
                m_Logger.LogError(excp, excp.Message);
                throw;
            }

            return result;
        }
        //public async Task<string> RetrieveMultipleByLink(string link)
        //{
        //    string result = "";
        //    try
        //    {
        //        // 创建 RestRequest 并设置目标 URI
        //        var request = new RestRequest(link, Method.Get);

        //        // 发送请求并获取响应
        //        var response = await m_Client.ExecuteAsync(request);

        //        // 检查响应是否成功
        //        if (!response.IsSuccessful)
        //        {
        //            throw new HttpRequestException($"Failed to retrieve data. Status code: {response.StatusCode}, Message: {response.Content}");
        //        }

        //        // 获取响应内容
        //        result = response.Content;
        //    }
        //    catch (Exception excp)
        //    {
        //        // 记录异常
        //        m_Logger.LogError(excp, excp.Message);
        //        throw;
        //    }

        //    return result;
        //}

        public async Task<JObject> APIExecuteAction(string actionName, JObject obj)
        {
            var targetUri = actionName;
            var content = BuildJsonContent(obj);
            var httpMethod = HttpMethod.Post; // HttpMethod.Post;

            try
            {
                var responseMesage = await InvokeAPI(targetUri, httpMethod, content);
                return JsonConvert.DeserializeObject<JObject>(responseMesage.ToString());
            }
            catch (Exception excp)
            {
                m_Logger.LogError(excp, excp.Message);
                throw;
            }
        }


        public async Task Upsert(string entitySetName, Dictionary<string, object> altKeyProps,
            JObject entityRecord)
        {
            List<string> altKeyPropsString = new List<string>();
            foreach (var item in altKeyProps)
            {
                var keyFieldName = item.Key;
                var keyFieldValue = item.Value;

                switch (keyFieldValue)
                {
                    case int:
                    case decimal:
                        altKeyPropsString.Add($"{keyFieldName}={keyFieldValue}");
                        break;
                    case DateTime:
                    case string:
                        altKeyPropsString.Add($"{keyFieldName}='{keyFieldValue}'");
                        break;
                    default:
                        throw new NotImplementedException($"{keyFieldValue.GetType()}类型不支持,  {nameof(Upsert)}方法");
                }
            }

            string targetUri = $"{entitySetName}({String.Join(",", altKeyPropsString.ToArray())})";
            var httpMethod = HttpMethod.Patch; //new HttpMethod("PATCH");
            var content = BuildJsonContent(entityRecord);

            await InvokeAPI(targetUri, httpMethod, content);
        }

        public async Task Upsert<T>(string entitySetName, Dictionary<string, object> altKeyProps, T entityRecord)
        {
            var recordType = typeof(T);

            List<string> altKeyPropsString = new List<string>();
            foreach (var item in altKeyProps)
            {
                var keyFieldName = item.Key;
                var keyFieldValue = item.Value;
                var keyPropertyInfo = recordType.GetProperty(keyFieldName);
                var keyPropertyValue = keyPropertyInfo.GetValue(entityRecord);
                if (keyPropertyValue == null)
                {
                    keyPropertyInfo.SetValue(entityRecord, keyFieldValue);
                }

                switch (keyFieldValue)
                {
                    case int:
                    case decimal:
                        altKeyPropsString.Add($"{keyFieldName}={keyFieldValue}");
                        break;
                    case DateTime:
                    case string:
                        altKeyPropsString.Add($"{keyFieldName}='{keyFieldValue}'");
                        break;
                    default:
                        throw new NotImplementedException($"{keyFieldValue.GetType()}类型不支持,  {nameof(Upsert)}方法");
                }
            }

            string targetUri = $"{entitySetName}({String.Join(",", altKeyPropsString.ToArray())})";
            var httpMethod = HttpMethod.Patch; // new HttpMethod("PATCH");
            var content = BuildJsonContent(entityRecord);

            await InvokeAPI(targetUri, httpMethod, content);
        }

        public async Task DeleteRecordById(string entitySetName, string id)
        {
            string targetUri = $"{entitySetName}({id.Replace("{", "").Replace("}", "")})";
            var httpMethod = HttpMethod.Delete; //HttpMethod.Delete;

            await InvokeAPI(targetUri, httpMethod);
        }

        public async Task DeleteRecordByAltKeys(string entitySetName, Dictionary<string, object> altKeys)
        {
            if (altKeys == null)
            {
                throw new ArgumentNullException($"{nameof(altKeys)}");
            }

            List<string> altKeyList = new List<string>();
            foreach (var item in altKeys)
            {
                altKeyList.Add($"{item.Key}='{item.Value}'");
            }

            string targetUri = $"{entitySetName}({String.Join(",", altKeyList)})";
            var httpMethod = HttpMethod.Delete; //HttpMethod.Delete;

            await InvokeAPI(targetUri, httpMethod);
        }

        public async Task<HttpContent> DownloadFile(string entityId, string entitySetName, string xName)
        {
            string targetUri = $"{entitySetName}({entityId.Replace("{", "").Replace("}", "")})/{xName}/$value";
            var httpMethod = HttpMethod.Get; //HttpMethod.Get;

            try
            {
                var reponseMessage = await InvokeAPIStream(targetUri, httpMethod);

                return reponseMessage;
            }
            catch (Exception excp)
            {
                m_Logger.LogError(excp, excp.Message);
                throw;
            }
        }

        //public async Task<RestResponse> DownloadFile(string entityId, string entitySetName, string xName)
        //{
        //    // 构造目标 URI
        //    string targetUri = $"{entitySetName}({entityId.Replace("{", "").Replace("}", "")})/{xName}/$value";
        //    var httpMethod = Method.Get; // 使用 RestSharp 的 Method.Get

        //    try
        //    {
        //        // 调用 InvokeAPIStream 获取文件流
        //        var response = await InvokeAPIStream(targetUri, httpMethod);

        //        // 返回 RestResponse 对象，其中包含文件流
        //        return response;
        //    }
        //    catch (Exception excp)
        //    {
        //        m_Logger.LogError(excp, excp.Message);
        //        throw;
        //    }
        //}

        public async Task<HttpContent> DownloadFullImage(string entityId, string entitySetName, string xName)
        {
            string targetUri = $"{entitySetName}({entityId.Replace("{", "").Replace("}", "")})/{xName}/$value?size=full";
            var httpMethod = HttpMethod.Get;// HttpMethod.Get;

            try
            {
                var reponseMessage = await InvokeAPIStream(targetUri, httpMethod);

                return reponseMessage;
            }
            catch (Exception excp)
            {
                m_Logger.LogError(excp, excp.Message);
                throw;
            }
        }
        //public async Task<RestResponse> DownloadFullImage(string entityId, string entitySetName, string xName)
        //{
        //    // 构造目标 URI，添加查询参数 `size=full` 以下载完整图像
        //    string targetUri = $"{entitySetName}({entityId.Replace("{", "").Replace("}", "")})/{xName}/$value?size=full";
        //    var httpMethod = Method.Get; // 使用 RestSharp 的 Method.Get

        //    try
        //    {
        //        // 调用 InvokeAPIStream 获取文件流
        //        var response = await InvokeAPIStream(targetUri, httpMethod);

        //        // 返回 RestResponse 对象，其中包含文件流
        //        return response;
        //    }
        //    catch (Exception excp)
        //    {
        //        m_Logger.LogError(excp, excp.Message);
        //        throw;
        //    }
        //}

        public async Task<HttpContent> InvokeAPIStream(string targetUri, HttpMethod httpMethod, HttpContent content = null, int retryCount = 0)
        {
            var policy = CreateTokenRefreshPolicy();
            var resp = policy.Execute(() =>
            {
                var requestMessage = new HttpRequestMessage(httpMethod, targetUri);

                if (content != null)
                {
                    requestMessage.Content = content;
                }
                return m_Client.SendAsync(requestMessage).Result;
            }
            );

            if (resp.IsSuccessStatusCode)
            {
                return resp.Content;
            }
            else
            {
                if (resp.StatusCode == HttpStatusCode.TooManyRequests)
                {
                    if (++retryCount >= MAX_RETRIES)
                    {
                        throw new HttpRequestException($"429错误(Too Many Request), D365访问");
                    }
                    int seconds;
                    if (resp.Headers.Contains("Retry-After"))
                    {
                        seconds = int.Parse(resp.Headers.GetValues("Retry-After").FirstOrDefault());
                    }
                    else
                    {
                        seconds = (int)Math.Pow(2, retryCount);
                    }
                    Thread.Sleep(TimeSpan.FromSeconds(seconds));
                    return await InvokeAPIStream(targetUri, httpMethod, content, retryCount);
                }
                throw new HttpRequestException(resp.Content.ReadAsStringAsync().Result);
            }
        }
        //public async Task<RestResponse> InvokeAPIStream(string targetUri, Method httpMethod, object content = null, int retryCount = 0)
        //{
        //    var policy = CreateTokenRefreshPolicy();
        //    var resp = policy.Execute(() =>
        //    {
        //        var request = new RestRequest(targetUri, httpMethod);

        //        // 如果有内容，设置请求体
        //        if (content != null)
        //        {
        //            request.AddJsonBody(content);
        //        }

        //        // 发送请求并返回响应
        //        var response = m_Client.Execute(request);
        //        return response;
        //    });

        //    // 如果请求成功，返回响应内容
        //    if (resp.IsSuccessful)
        //    {
        //        return resp;
        //    }
        //    else
        //    {
        //        // 如果遇到 429 Too Many Requests，进行重试
        //        if (resp.StatusCode == HttpStatusCode.TooManyRequests)
        //        {
        //            if (++retryCount >= MAX_RETRIES)
        //            {
        //                throw new HttpRequestException($"429 错误 (Too Many Requests), D365 访问超限");
        //            }
        //            int seconds;
        //            var retryAfterHeader = resp.Headers.FirstOrDefault(header => header.Name.Equals("Retry-After", StringComparison.OrdinalIgnoreCase));
        //            if (retryAfterHeader != null && int.TryParse(retryAfterHeader.Value.ToString(), out seconds))
        //            {
        //                //seconds = int.Parse(resp.Headers.GetValues("Retry-After").FirstOrDefault());
        //            }
        //            else
        //            {
        //                // 默认的退避策略
        //                seconds = (int)Math.Pow(2, retryCount);
        //            }

        //            // 等待一段时间后重试
        //            await Task.Delay(TimeSpan.FromSeconds(seconds));
        //            return await InvokeAPIStream(targetUri, httpMethod, content, retryCount);
        //        }

        //        // 如果请求失败，抛出异常并返回错误信息
        //        throw new HttpRequestException(resp.Content);
        //    }
        //}

        public async Task<int> GetTotalCount(string entityName)
        {
            var targetUri = $"RetrieveTotalRecordCount(EntityNames=@EntityNames)?@EntityNames=%5B%27{entityName}%27%5D";
            var sbResponse = await InvokeAPI(targetUri, HttpMethod.Get);
            var recordCountModel = JObject.Parse(sbResponse.ToString()).GetValue("EntityRecordCountCollection").ToObject<EntityRecordCountModel>();

            return recordCountModel.Values[0];
        }

        #endregion

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="entityId">实体ID</param>
        /// <param name="entityFileAttributeLogicalName">文件字段名</param>
        /// <param name="entitySetName">实体复数</param>
        /// <param name="uploadFileName">上传文件名，带后文件后缀</param>
        /// <param name="files">文件</param>
        /// <param name="retryCount">重复请求数</param>
        /// <returns></returns>
        public async Task<bool> FileUploadAsync(string entityId, string entityFileAttributeLogicalName, string entitySetName, string uploadFileName, byte[] files, int retryCount = 3)
        {

            string targetUri = $"{entitySetName}({entityId})/{entityFileAttributeLogicalName}" + "?x-ms-file-name=" + uploadFileName;
            using (var request = new HttpRequestMessage(new HttpMethod("PATCH"), targetUri))
            {
                var policy = CreateTokenRefreshPolicy();
                var resp = policy.Execute(() =>
                {
                    using var fileStream = new System.IO.MemoryStream(files);
                    request.Content = new StreamContent(fileStream);
                    request.Content.Headers.Add("Content-Type", "application/octet-stream");
                    return m_Client.SendAsync(request).Result;
                });
                if (resp.IsSuccessStatusCode)
                {
                    return true;
                }
                else
                {
                    if (resp.StatusCode == HttpStatusCode.TooManyRequests)
                    {
                        if (++retryCount >= MAX_RETRIES)
                        {
                            throw new HttpRequestException($"429错误(Too Many Request), D365访问");
                        }
                        int seconds;
                        if (resp.Headers.Contains("Retry-After"))
                        {
                            seconds = int.Parse(resp.Headers.GetValues("Retry-After").FirstOrDefault());
                        }
                        else
                        {
                            seconds = (int)Math.Pow(2, retryCount);
                        }
                        Thread.Sleep(TimeSpan.FromSeconds(seconds));
                        return await FileUploadAsync(entityId, entityFileAttributeLogicalName, entitySetName, uploadFileName, files, retryCount);
                    }
                    throw new HttpRequestException(resp.Content.ReadAsStringAsync().Result);
                }
            }

        }
        //public async Task<bool> FileUploadAsync(string entityId, string entityFileAttributeLogicalName, string entitySetName, string uploadFileName, byte[] files, int retryCount = 3)
        //{
        //    // 构造目标 URI，包含实体ID和文件属性名称
        //    string targetUri = $"{entitySetName}({entityId})/{entityFileAttributeLogicalName}?x-ms-file-name={uploadFileName}";

        //    // 创建 RestRequest 对象，指定 PATCH 方法
        //    var request = new RestRequest(targetUri, Method.Patch);

        //    var policy = CreateTokenRefreshPolicy();
        //    var resp = policy.Execute(() =>
        //    {
        //        // 使用 MemoryStream 包装文件内容
        //        using (var fileStream = new MemoryStream(files))
        //        {
        //            // 将文件内容作为参数添加到请求体中
        //            request.AddParameter("application/octet-stream", fileStream, ParameterType.RequestBody);
        //        }

        //        // 发送请求
        //        return m_Client.Execute(request);
        //    });

        //    // 处理响应
        //    if (resp.IsSuccessful)
        //    {
        //        return true;
        //    }
        //    else
        //    {
        //        if (resp.StatusCode == HttpStatusCode.TooManyRequests)
        //        {
        //            if (++retryCount >= MAX_RETRIES)
        //            {
        //                throw new HttpRequestException($"429 错误 (Too Many Requests), D365访问");
        //            }

        //            int seconds;
        //            var retryAfterHeader = resp.Headers.FirstOrDefault(header => header.Name.Equals("Retry-After", StringComparison.OrdinalIgnoreCase));
        //            if (retryAfterHeader != null && int.TryParse(retryAfterHeader.Value.ToString(), out seconds))
        //            {
        //                //seconds = int.Parse(resp.Headers.GetValues("Retry-After").FirstOrDefault());
        //            }
        //            else
        //            {
        //                // 默认的退避策略
        //                seconds = (int)Math.Pow(2, retryCount);
        //            }

        //            // 暂停一段时间后重试
        //            await Task.Delay(TimeSpan.FromSeconds(seconds));
        //            return await FileUploadAsync(entityId, entityFileAttributeLogicalName, entitySetName, uploadFileName, files, retryCount);
        //        }

        //        // 处理其他错误，抛出异常
        //        throw new HttpRequestException(resp.Content);
        //    }
        //}
    }
}
