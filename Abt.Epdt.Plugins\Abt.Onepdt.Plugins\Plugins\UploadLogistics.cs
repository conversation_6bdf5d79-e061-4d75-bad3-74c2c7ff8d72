﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.Onepdt.Plugins
{
    public class UploadLogistics : IPlugin
    {
        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService service = serviceFactory.CreateOrganizationService(null); // 权限
            ITracingService tracingService = (ITracingService)serviceProvider.GetService(typeof(ITracingService));
            try
            {
                if (context.InputParameters.Contains("Target") && context.InputParameters["Target"] is Entity)
                {
                    // 获取实体信息
                    Entity targetEntity = (Entity)context.InputParameters["Target"];

                    targetEntity["onepdt_mail_time"] =  DateTime.UtcNow;

                }

            }
            catch (Exception ex)
            {
                tracingService.Trace(ex.Message + ex.StackTrace + ex.StackTrace);
                throw;
            }
        }
    }
}
