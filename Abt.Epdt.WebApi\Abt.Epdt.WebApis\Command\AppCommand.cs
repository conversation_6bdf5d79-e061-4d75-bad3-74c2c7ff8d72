﻿using Abt.Epdt.WebApis.Model;
using Abt.Epdt.WebApis.Pages;
using Abt.Epdt.WebApis.Util;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.IdentityModel.Tokens;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Png;
using SixLabors.ImageSharp.Processing;
using Sustainsys.Saml2.Metadata;
using System;
using System.Collections.Generic;
//using System.Drawing.Imaging;
//using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.Intrinsics.X86;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using static Abt.Epdt.WebApis.Model.CardTypeModel;
using static System.Net.WebRequestMethods;

namespace Abt.Epdt.WebApis.Command
{
    public class AppCommand : OrgService
    {
        private readonly IMemoryCache _cache;
        public AppCommand(IMemoryCache memoryCache) : base(memoryCache)
        {
            _cache = memoryCache;
        }

        public string GetUserInfo()
        {
            var qe = new QueryExpression("systemuser");
            qe.ColumnSet.AddColumn("fullname");
            var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
            return ec.Entities[0].GetAttributeValue<string>("fullname");
        }

        /// <summary>
        /// 检验用户邮箱并获取角色
        /// </summary>
        /// <param name="email">邮箱</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public UserInfo LoginAndGetRole(string email)
        {
            try
            {
                var info = new UserInfo();
                var qe = new QueryExpression("onepdt_t_employee_basic_mdata");
                qe.ColumnSet.AddColumns("onepdt_name", "onepdt_mail", "onepdt_code", "onepdt_recentrole_id");
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

                // var qe = new QueryExpression("systemuser");
                // qe.ColumnSet.AddColumns("internalemailaddress", "fullname");
                // qe.Criteria.AddCondition("isdisabled", ConditionOperator.Equal, false);
                qe.Criteria.AddCondition("onepdt_mail", ConditionOperator.Equal, email);
                qe.Criteria.AddCondition("onepdt_buidname", ConditionOperator.Equal, "CRM");
                var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                if (ec == null || ec.Entities.Count == 0)
                {
                    CommandHelper.CreateApiLog("员工登录", $"email={email}", $"不存在{email}系统用户", "员工登录", "失败", OrganizationServiceAdmin);
                    throw new Exception($"不存在{email}系统用户");
                }
                else
                {
                    info.userid = ec.Entities[0].Id.ToString();
                    info.username = ec.Entities[0].GetAttributeValue<string>("onepdt_name");
                    info.code = ec.Entities[0].GetAttributeValue<string>("onepdt_code");
                    info.useremail = email;
                }
                var roles = CommandHelper.GetRoles(email, OrganizationServiceAdmin);

                if (ec.Entities[0].Contains("onepdt_recentrole_id"))
                {
                    //最近登录角色id
                    var recentroleid = ec.Entities[0].GetAttributeValue<EntityReference>("onepdt_recentrole_id").Id.ToString();
                    if (roles.Any(a => a.roleid == recentroleid))
                    {
                        roles = roles.OrderBy(r => r.roleid == recentroleid ? 0 : 1).ToList();
                    }
                }

                info.role = roles;

                info.isexitstodolist = Isexitstodolist(email);

                CommandHelper.CreateApiLog("员工登录", $"email={email}", JsonConvert.SerializeObject(info), "员工登录", "成功", OrganizationServiceAdmin);

                return info;
            }
            catch (Exception ex)
            {
                CommandHelper.CreateApiLog("员工登录", $"email={email}", JsonConvert.SerializeObject(ex.Message), "员工登录", "失败", OrganizationServiceAdmin);
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 切换角色
        /// </summary>
        /// <param name="userid">员工id</param>
        /// <param name="roleid">角色id</param>
        /// <param name="email">邮箱</param>
        /// <returns></returns>
        public bool SwitchRole(string userid, string roleid, string email)
        {
            if (!string.IsNullOrWhiteSpace(userid) && !string.IsNullOrWhiteSpace(roleid))
            {
                var update = new Entity("onepdt_t_employee_basic_mdata", new Guid(userid));
                update["onepdt_recentrole_id"] = new EntityReference("onepdt_t_role", new Guid(roleid));
                OrganizationServiceAdmin.Update(update);
            }

            return Isexitstodolist(email);
        }

        /// <summary>
        /// 判断是否有待办清单
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        public bool Isexitstodolist(string email)
        {
            #region 判断是否有待办清单
            var qe1 = new QueryExpression("onepdt_t_operation");
            qe1.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            var f1 = new FilterExpression
            {
                FilterOperator = LogicalOperator.And,
                Conditions =
                {
                    new ConditionExpression("onepdt_submit_status", ConditionOperator.Equal, false),
                    new ConditionExpression("onepdt_is_returned", ConditionOperator.Equal, false),
                    new ConditionExpression("onepdt_approval_status", ConditionOperator.Null),
                    new ConditionExpression("onepdt_address_approval_status", ConditionOperator.Null)
                }
            };
            var f2 = new FilterExpression
            {
                FilterOperator = LogicalOperator.And,
                Conditions =
                {
                    new ConditionExpression("onepdt_approval_status", ConditionOperator.Equal, 2)
                }
            };
            var f3 = new FilterExpression
            {
                FilterOperator = LogicalOperator.And,
                Conditions =
                {
                    new ConditionExpression("onepdt_address_approval_status", ConditionOperator.Equal, 2)
                }
            };
            var f4 = new FilterExpression
            {
                FilterOperator = LogicalOperator.And,
                Conditions =
                {
                    new ConditionExpression("onepdt_approval_status", ConditionOperator.Null),
                    new ConditionExpression("onepdt_address_approval_status", ConditionOperator.Equal, 0),
                    new ConditionExpression("onepdt_is_returned", ConditionOperator.Equal, true)
                }
            };
            var f5 = new FilterExpression
            {
                FilterOperator = LogicalOperator.And,
                Conditions =
                {
                    new ConditionExpression("onepdt_submit_status", ConditionOperator.Equal, false),
                    new ConditionExpression("onepdt_is_returned", ConditionOperator.Equal, true),
                    new ConditionExpression("onepdt_approval_status", ConditionOperator.Null),
                    new ConditionExpression("onepdt_address_approval_status", ConditionOperator.Null)
                }
            };
            var filter = new FilterExpression
            {
                FilterOperator = LogicalOperator.Or,
                Filters = { f1, f2, f3, f4, f5 }
            };

            qe1.Criteria.AddFilter(filter);
            var link = new LinkEntity("onepdt_t_operation", "onepdt_t_employee_basic_mdata", "onepdt_submitter", "onepdt_t_employee_basic_mdataid", JoinOperator.Inner);
            link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            link.LinkCriteria.AddCondition("onepdt_mail", ConditionOperator.Equal, email);
            link.EntityAlias = "emp";
            qe1.LinkEntities.Add(link);
            qe1.PageInfo = new PagingInfo()
            {
                ReturnTotalRecordCount = true
            };
            var ec1 = OrganizationServiceAdmin.RetrieveMultiple(qe1);
            if (ec1 == null || ec1.Entities.Count == 0) return false;
            return true;
            #endregion
        }

        /// <summary>
        /// 获取待办列表
        /// </summary>
        /// <returns></returns>
        public OperationData GetTodoList(string email, int type, int pagesize, int pageindex, string searchText, string implantType, string status, string hospital, string starttime, string endtime)
        {
            try
            {
                var data = new OperationData();
                var types = new List<TypeInfo>() { new TypeInfo() { typename = "周报" }, new TypeInfo() { typename = "审批驳回" }, new TypeInfo() { typename = "邮寄复核" }, new TypeInfo() { typename = "信息更正" }, new TypeInfo() { typename = "退回重建" } }; ;

                //var psrid = CommandHelper.GetPsrid(email, OrganizationServiceAdmin);
                //var SaleHospital = CommandHelper.GetSaleHospital(psrid, OrganizationServiceAdmin);
                //if (SaleHospital == null || SaleHospital.Count == 0)
                //{
                //    return data;
                //}

                var qe = new QueryExpression("onepdt_t_operation");
                qe.ColumnSet.AllColumns = true;
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                if (!string.IsNullOrWhiteSpace(searchText))
                {
                    FilterExpression filter = new FilterExpression
                    {
                        FilterOperator = LogicalOperator.Or,
                        Conditions =
                        {
                            new ConditionExpression("onepdt_name", ConditionOperator.Like, "%"+ searchText +"%"),
                            new ConditionExpression("onepdt_hospitalname", ConditionOperator.Like, "%"+ searchText +"%"),
                            new ConditionExpression("onepdt_patient", ConditionOperator.Like, "%"+ searchText +"%"),
                        }
                    };
                    qe.Criteria.AddFilter(filter);
                }
                if (!string.IsNullOrWhiteSpace(implantType))
                {
                    qe.Criteria.AddCondition("onepdt_typename", ConditionOperator.In, implantType.Split('、'));
                }
                if (!string.IsNullOrWhiteSpace(status))
                {
                    qe.Criteria.AddCondition("onepdt_submit_status", ConditionOperator.Equal, status == "跟台");
                }
                if (!string.IsNullOrWhiteSpace(hospital))
                {
                    qe.Criteria.AddCondition("onepdt_hospitalname", ConditionOperator.Like, "%" + hospital + "%");
                }
                if (!string.IsNullOrWhiteSpace(starttime) && !string.IsNullOrWhiteSpace(endtime))
                {
                    qe.Criteria.AddCondition("onepdt_date", ConditionOperator.OnOrAfter, Convert.ToDateTime(starttime));
                    qe.Criteria.AddCondition("onepdt_date", ConditionOperator.OnOrBefore, Convert.ToDateTime(endtime));
                }
                var link = new LinkEntity("onepdt_t_operation", "onepdt_t_employee_basic_mdata", "onepdt_submitter", "onepdt_t_employee_basic_mdataid", JoinOperator.Inner);
                link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                link.LinkCriteria.AddCondition("onepdt_mail", ConditionOperator.Equal, email);
                link.EntityAlias = "emp";
                qe.LinkEntities.Add(link);
                qe.AddOrder("modifiedon", OrderType.Descending);
                //查询待办Tab的dot
                var dotec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                if (dotec != null && dotec.Entities.Count > 0)
                {
                    //周报
                    var type1 = dotec.Entities.Where(a => a.GetAttributeValue<bool>("onepdt_submit_status") == false && a.GetAttributeValue<bool>("onepdt_is_returned") == false && !a.Contains("onepdt_approval_status") && !a.Contains("onepdt_address_approval_status")).ToList();
                    //业务驳回
                    var type2 = dotec.Entities.Where(a => a.Contains("onepdt_approval_status") && a.GetAttributeValue<OptionSetValue>("onepdt_approval_status").Value == 2).ToList();
                    //邮寄退回
                    var type3 = dotec.Entities.Where(a => a.Contains("onepdt_address_approval_status") && a.GetAttributeValue<OptionSetValue>("onepdt_address_approval_status").Value == 2).ToList();
                    //部分退回
                    var type4 = dotec.Entities.Where(a => a.GetAttributeValue<bool>("onepdt_is_returned") == true && !a.Contains("onepdt_approval_status") && a.Contains("onepdt_address_approval_status") && a.GetAttributeValue<OptionSetValue>("onepdt_address_approval_status").Value == 0).ToList();
                    //全部退回
                    var type5 = dotec.Entities.Where(a => a.GetAttributeValue<bool>("onepdt_submit_status") == false && a.GetAttributeValue<bool>("onepdt_is_returned") == true && !a.Contains("onepdt_approval_status") && !a.Contains("onepdt_address_approval_status")).ToList();

                    types[0].ishowdot = type1.Count() > 0;
                    types[1].ishowdot = type2.Count() > 0;
                    types[2].ishowdot = type3.Count() > 0;
                    types[3].ishowdot = type4.Count() > 0;
                    types[4].ishowdot = type5.Count() > 0;
                }
                data.types = types;

                if (type == 0)//周报
                {
                    //周报+退回状态为否+业务审批状态为空+邮寄审批状态为空
                    qe.Criteria.AddCondition("onepdt_submit_status", ConditionOperator.Equal, false);
                    qe.Criteria.AddCondition("onepdt_is_returned", ConditionOperator.Equal, false);
                    qe.Criteria.AddCondition("onepdt_approval_status", ConditionOperator.Null);
                    qe.Criteria.AddCondition("onepdt_address_approval_status", ConditionOperator.Null);
                }
                else if (type == 1)//审批退回
                {
                    qe.Criteria.AddCondition("onepdt_approval_status", ConditionOperator.Equal, 2);
                }
                else if (type == 2)//邮寄退回
                {
                    qe.Criteria.AddCondition("onepdt_address_approval_status", ConditionOperator.Equal, 2);
                }
                else if (type == 3)//部分退回
                {
                    //部分退回：业务审批状态为空&邮寄审批状态=待审批&是否退回=是
                    qe.Criteria.AddCondition("onepdt_approval_status", ConditionOperator.Null);
                    qe.Criteria.AddCondition("onepdt_address_approval_status", ConditionOperator.Equal, 0);
                    qe.Criteria.AddCondition("onepdt_is_returned", ConditionOperator.Equal, true);
                }
                else if (type == 4)//全部退回
                {
                    //周报+已退回+业务审批状态为空+邮寄审批状态为空
                    qe.Criteria.AddCondition("onepdt_submit_status", ConditionOperator.Equal, false);
                    qe.Criteria.AddCondition("onepdt_is_returned", ConditionOperator.Equal, true);
                    qe.Criteria.AddCondition("onepdt_approval_status", ConditionOperator.Null);
                    qe.Criteria.AddCondition("onepdt_address_approval_status", ConditionOperator.Null);
                }
                qe.PageInfo = new PagingInfo()
                {
                    PageNumber = pageindex,
                    Count = pagesize,
                    ReturnTotalRecordCount = true
                };
                var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                if (ec != null && ec.Entities.Count > 0)
                {
                    data.total = ec.TotalRecordCount;
                    var ids = ec.Entities.Select(a => a.Id.ToString()).ToList();
                    //产品型号名称
                    var productnamelist = GetProductname(ids);

                    var list = new List<OperationModel>();
                    foreach (var item in ec.Entities)
                    {
                        var model = new OperationModel();

                        model.id = item.Id.ToString();
                        model.entityname = "onepdt_t_operation";
                        model.onepdt_name = item.GetAttributeValue<string>("onepdt_name");
                        model.onepdt_hospitalname = item.GetAttributeValue<string>("onepdt_hospitalname");
                        model.onepdt_patient = item.GetAttributeValue<string>("onepdt_patient");
                        if (item.Contains("onepdt_submit_status"))
                        {
                            model.onepdt_submit_status_label = item.GetAttributeValue<bool>("onepdt_submit_status") ? "跟台" : "周报";
                        }
                        if (item.Contains("onepdt_approval_status"))
                        {
                            model.onepdt_approval_status_label = item.FormattedValues["onepdt_approval_status"];
                        }
                        if (item.Contains("onepdt_date"))
                        {
                            model.onepdt_date = item.GetAttributeValue<DateTime>("onepdt_date").AddHours(8).ToString("yyyy/MM/dd");
                        }
                        if (item.Contains("onepdt_submitter"))
                        {
                            model.onepdt_submittername = item.GetAttributeValue<EntityReference>("onepdt_submitter").Name;
                        }
                        model.onepdt_product_name = productnamelist.Where(a => a.id == item.Id.ToString())?.FirstOrDefault()?.text;//GetProductname(item.Id.ToString());
                        if (item.Contains("onepdt_approver_text"))
                            model.onepdt_approver_text = item.GetAttributeValue<string>("onepdt_approver_text");
                        var onepdt_approval_comment = item.GetAttributeValue<string>("onepdt_approval_comment");
                        var onepdt_address_approval_comment = item.GetAttributeValue<string>("onepdt_address_approval_comment");
                        if (type == 1)
                        {
                            model.onepdt_comment = onepdt_approval_comment;
                        }
                        else if (type == 2)
                        {
                            model.onepdt_comment = onepdt_address_approval_comment;
                        }
                        else
                        {
                            if (!string.IsNullOrWhiteSpace(onepdt_address_approval_comment))
                            {
                                model.onepdt_comment = onepdt_address_approval_comment;
                            }
                            if (!string.IsNullOrWhiteSpace(onepdt_approval_comment))
                            {
                                model.onepdt_comment = onepdt_approval_comment;
                            }
                        }
                        list.Add(model);
                    }
                    data.datas = list;
                }
                return data;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 获取植入类型
        /// </summary>
        /// <returns></returns>
        /// <summary>
        /// 获取跟台配置
        /// </summary>
        /// <param name="tablename"></param>
        /// <param name="fieldname"></param>
        /// <returns></returns>
        public List<OnepdtConfig> GetOnepdtConfig(string tablename, string fieldname)
        {
            var list = new List<OnepdtConfig>();

            var data = string.Empty;
            // 尝试从缓存中获取
            if (!_cache.TryGetValue($"{tablename}{fieldname}", out data))
            {
                // 如果缓存中没有，则从配置中读取
                var qe = new QueryExpression("onepdt_t_configuration");
                qe.ColumnSet.AddColumn("onepdt_name");
                qe.ColumnSet.AddColumn("onepdt_value");
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition("onepdt_table_name", ConditionOperator.Equal, tablename);
                qe.Criteria.AddCondition("onepdt_field_name", ConditionOperator.Equal, fieldname);
                if (tablename == "产品信息" && fieldname == "产品大类")
                {
                    qe.Criteria.AddCondition("onepdt_parentname", ConditionOperator.Equal, "主机");
                }
                qe.AddOrder("onepdt_order", OrderType.Ascending);
                var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                if (ec != null && ec.Entities.Count > 0)
                {
                    var datalist = ec.Entities.Select(b => new OnepdtConfig { text = b.GetAttributeValue<string>("onepdt_name"), value = fieldname == "植入工具" ? b.GetAttributeValue<string>("onepdt_name") : b.Id.ToString(), configValue = b.GetAttributeValue<string>("onepdt_value") }).ToList();
                    data = JsonConvert.SerializeObject(datalist);
                }

                // 将连接字符串存储到缓存
                _cache.Set($"{tablename}{fieldname}", data, TimeSpan.FromHours(2)); // 缓存1小时
            }
            if (!string.IsNullOrWhiteSpace(data))
            {
                list = JsonConvert.DeserializeObject<List<OnepdtConfig>>(data);
            }
            return list;
        }

        /// <summary>
        /// 获取主机型号
        /// </summary>
        /// <param name="operationid"></param>
        /// <returns></returns>
        public string GetProductname(string operationid)
        {
            var productname = string.Empty;

            var qe = new QueryExpression("onepdt_t_operation_implant");
            qe.ColumnSet.AllColumns = true;
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("onepdt_operation", ConditionOperator.Equal, operationid);
            var link = new LinkEntity("onepdt_t_operation_implant", "onepdt_t_product_mdata", "onepdt_product_mdata", "onepdt_t_product_mdataid", JoinOperator.Inner);
            link.Columns.AddColumn("onepdt_name");
            link.LinkCriteria.AddCondition("onepdt_type", ConditionOperator.Equal, "主机");
            link.EntityAlias = "pro";
            qe.LinkEntities.Add(link);
            var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
            if (ec != null && ec.Entities.Count > 0)
            {
                var list = ec.Entities.Where(a => a.Contains("pro.onepdt_name")).Select(b => b.GetAttributeValue<AliasedValue>("pro.onepdt_name").Value.ToString()).ToList();
                productname = string.Join(",", list);
            }
            return productname;
        }

        /// <summary>
        /// 获取主机型号
        /// </summary>
        /// <param name="operationid"></param>
        /// <returns></returns>
        public List<QueryModel> GetProductname(List<string> operationids)
        {
            var list = new List<QueryModel>();

            var qe = new QueryExpression("onepdt_t_operation_implant");
            qe.ColumnSet.AllColumns = true;
            //qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("onepdt_operation", ConditionOperator.In, operationids.ToArray());
            var link = new LinkEntity("onepdt_t_operation_implant", "onepdt_t_product_mdata", "onepdt_product_mdata", "onepdt_t_product_mdataid", JoinOperator.Inner);
            link.Columns.AddColumn("onepdt_name");
            link.LinkCriteria.AddCondition("onepdt_type", ConditionOperator.Equal, "主机");
            link.EntityAlias = "pro";
            qe.LinkEntities.Add(link);
            var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
            if (ec != null && ec.Entities.Count > 0)
            {
                var gb = ec.Entities.GroupBy(a => a.GetAttributeValue<EntityReference>("onepdt_operation").Id.ToString()).Select(b => new { operationid = b.Key, list = b.ToList() }).ToList();
                if (gb != null && gb.Count > 0)
                {
                    foreach (var item in gb)
                    {
                        var mo = new QueryModel();
                        mo.id = item.operationid;
                        var de = item.list.Where(a => a.Contains("pro.onepdt_name")).Select(b => b.GetAttributeValue<AliasedValue>("pro.onepdt_name").Value.ToString()).ToList();
                        mo.text = string.Join(",", de);
                        var de1 = item.list.Where(a => a.Contains("onepdt_name")).Select(b => b.GetAttributeValue<string>("onepdt_name")).ToList();
                        mo.text1 = string.Join(",", de1);
                        list.Add(mo);
                    }
                }
            }
            return list;
        }

        /// <summary>
        /// 获取打印状态
        /// </summary>
        /// <param name="operationid"></param>
        /// <returns></returns>
        public string GetPintStatus(string operationid)
        {
            var print_status = string.Empty;

            var qe = new QueryExpression("onepdt_t_patient_id_card");
            qe.ColumnSet.AddColumn("onepdt_print_status");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("onepdt_t_operation", ConditionOperator.Equal, operationid);
            var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
            if (ec != null && ec.Entities.Count > 0)
            {
                if (ec.Entities[0].Contains("onepdt_print_status"))
                    print_status = ec.Entities[0].FormattedValues["onepdt_print_status"];
            }
            return print_status;
        }

        /// <summary>
        /// 获取打印状态
        /// </summary>
        /// <param name="operationid"></param>
        /// <returns></returns>
        public List<QueryModel> GetPintStatus(List<string> operationids)
        {
            var list = new List<QueryModel>();

            var qe = new QueryExpression("onepdt_t_patient_id_card");
            qe.ColumnSet.AddColumns("onepdt_print_status", "onepdt_t_operation", "onepdt_logistics_no");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("onepdt_t_operation", ConditionOperator.In, operationids.ToArray());
            var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
            if (ec != null && ec.Entities.Count > 0)
            {
                var gb = ec.Entities.GroupBy(a => a.GetAttributeValue<EntityReference>("onepdt_t_operation").Id.ToString()).Select(b => new { operationid = b.Key, list = b.ToList() }).ToList();
                if (gb != null && gb.Count > 0)
                {
                    foreach (var item in gb)
                    {
                        var mo = new QueryModel();
                        mo.id = item.operationid;
                        if (item.list != null && item.list.Count > 0)
                        {
                            var en = item.list[0];
                            if (en.Contains("onepdt_print_status"))
                            {
                                mo.text = en.FormattedValues["onepdt_print_status"];
                            }
                            if (en.Contains("onepdt_logistics_no"))
                            {
                                mo.text2 = en.GetAttributeValue<string>("onepdt_logistics_no");
                            }
                        }
                        list.Add(mo);
                    }
                }
            }
            return list;
        }

        /// <summary>
        /// 获取电子保卡
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public List<Dapplication> GetDapplications(string email, string operationid)
        {
            try
            {
                var psrid = CommandHelper.GetPsrid(email, OrganizationServiceAdmin);
                var SaleHospital = CommandHelper.GetSaleHospital(psrid, OrganizationServiceAdmin);

                var list = new List<Dapplication>();

                if (SaleHospital != null && SaleHospital.Count > 0)
                {
                    var qe = new QueryExpression("epdt_t_device_application");
                    qe.ColumnSet.AddColumns("epdt_name", "epdt_patient_abbottid", "epdt_hospital_code", "epdt_implant_date");
                    qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    qe.Criteria.AddCondition("epdt_check_status", ConditionOperator.NotEqual, "匹配成功");
                    FilterExpression filter = new FilterExpression
                    {
                        FilterOperator = LogicalOperator.Or,
                        Conditions =
                        {
                            new ConditionExpression("epdt_check_status",ConditionOperator.NotEqual,"匹配失败"),
                            new ConditionExpression("epdt_review_results",ConditionOperator.NotEqual,"无此植入")
                        }
                    };
                    qe.Criteria.AddFilter(filter);
                    var link = new LinkEntity("epdt_t_device_application", "epdt_t_patient", "epdt_patient_abbottid", "epdt_t_patientid", JoinOperator.Inner);
                    link.Columns.AddColumns("epdt_name", "epdt_patient_gender", "epdt_recipient_name", "epdt_recipient_phone", "epdt_mailing_address", "epdt_if_need_paper_card", "epdt_card_acquisition_method");
                    link.LinkCriteria.AddCondition("epdt_if_delete_patient", ConditionOperator.Equal, "否");
                    link.EntityAlias = "p";
                    qe.LinkEntities.Add(link);
                    var link1 = new LinkEntity("epdt_t_device_application", "epdt_t_hospital_basic_mdata", "epdt_hospital_code", "epdt_t_hospital_basic_mdataid", JoinOperator.Inner);
                    link1.Columns.AddColumns("epdt_name", "epdt_hospital_code");
                    link1.LinkCriteria.AddCondition("epdt_hospital_code", ConditionOperator.In, SaleHospital.ToArray());
                    link1.EntityAlias = "hosp";
                    qe.LinkEntities.Add(link1);
                    var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                    if (ec != null && ec.Entities.Count > 0)
                    {
                        //跟台和周报已经关联过的需过滤
                        var appids = GetOperationApplication(operationid);

                        foreach (var item in ec.Entities)
                        {
                            if (!appids.Contains(item.Id.ToString()))
                            {
                                var model = new Dapplication();
                                model.id = item.Id.ToString();
                                model.patientName = item.Contains("p.epdt_name") ? item.GetAttributeValue<AliasedValue>("p.epdt_name").Value.ToString() : "";
                                model.patientGender = item.Contains("p.epdt_patient_gender") ? item.GetAttributeValue<AliasedValue>("p.epdt_patient_gender").Value.ToString() : "";
                                model.recipientname = item.Contains("p.epdt_recipient_name") ? item.GetAttributeValue<AliasedValue>("p.epdt_recipient_name").Value.ToString() : "";
                                model.recipientphone = item.Contains("p.epdt_recipient_phone") ? item.GetAttributeValue<AliasedValue>("p.epdt_recipient_phone").Value.ToString() : "";
                                model.mailingaddress = item.Contains("p.epdt_mailing_address") ? item.GetAttributeValue<AliasedValue>("p.epdt_mailing_address").Value.ToString() : "";
                                model.implantDate = item.Contains("epdt_implant_date") ? item.GetAttributeValue<DateTime>("epdt_implant_date").AddHours(8).ToString("yyyy/MM/dd") : "";
                                model.hospital = item.Contains("epdt_hospital_code") ? item.GetAttributeValue<EntityReference>("epdt_hospital_code").Name : "";
                                model.hospitalid = item.Contains("epdt_hospital_code") ? item.GetAttributeValue<EntityReference>("epdt_hospital_code").Id.ToString() : "";
                                model.hospitalcode = item.Contains("hosp.epdt_hospital_code") ? item.GetAttributeValue<AliasedValue>("hosp.epdt_hospital_code").Value.ToString() : "";
                                model.epdt_if_need_paper_card = item.Contains("p.epdt_if_need_paper_card") ? (bool)item.GetAttributeValue<AliasedValue>("p.epdt_if_need_paper_card").Value : false;
                                model.epdt_card_acquisition_method = item.Contains("p.epdt_card_acquisition_method") ? (((bool)item.GetAttributeValue<AliasedValue>("p.epdt_card_acquisition_method").Value) ? "自定义" : "医院获取") : "";
                                list.Add(model);
                            }
                        }
                    }
                }

                return list;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 获取跟台数据的保卡信息
        /// </summary>
        /// <returns></returns>
        public List<string> GetOperationApplication(string operationid)
        {
            var list = new List<string>();

            var qe = new QueryExpression("onepdt_t_operation");
            qe.ColumnSet.AddColumn("onepdt_device_application");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("onepdt_device_application", ConditionOperator.NotNull);
            qe.Criteria.AddCondition("onepdt_approval_status", ConditionOperator.NotEqual, 3);
            if (!string.IsNullOrWhiteSpace(operationid))
            {
                qe.Criteria.AddCondition("onepdt_t_operationid", ConditionOperator.NotEqual, operationid);
            }
            var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
            if (ec != null && ec.Entities.Count > 0)
            {
                list = ec.Entities.Select(a => a.GetAttributeValue<EntityReference>("onepdt_device_application").Id.ToString()).Distinct().ToList();
            }
            return list;
        }

        /// <summary>
        /// 获取医院信息
        /// </summary>
        /// <returns></returns>
        public List<HospitalInfo> GetHospitals(string email)
        {
            var list = new List<HospitalInfo>();
            var data = string.Empty;

            try
            {
                // 尝试从缓存中获取
                //if (!_cache.TryGetValue($"{email}HospitalInfo", out data))
                //{
                // 如果缓存中没有，则从数据库中读取
                var psrid = CommandHelper.GetPsrid(email, OrganizationServiceAdmin);
                var SaleHospital = CommandHelper.GetSaleHospital(psrid, OrganizationServiceAdmin);

                if (SaleHospital != null && SaleHospital.Count > 0)
                {
                    var qe = new QueryExpression("epdt_t_hospital_basic_mdata");
                    qe.ColumnSet.AddColumns("epdt_name", "epdt_hospital_code");
                    qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    qe.Criteria.AddCondition("epdt_hospital_code", ConditionOperator.In, SaleHospital.ToArray());
                    var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                    if (ec != null && ec.Entities.Count > 0)
                    {
                        var datalist = ec.Entities.Select(a => new HospitalInfo() { id = a.Id.ToString(), name = a.GetAttributeValue<string>("epdt_name"), code = a.GetAttributeValue<string>("epdt_hospital_code") }).ToList();
                        data = JsonConvert.SerializeObject(datalist);
                    }
                }

                // 将连接字符串存储到缓存
                //    _cache.Set($"{email}HospitalInfo", data, TimeSpan.FromHours(1)); // 缓存1小时
                //}
                if (!string.IsNullOrWhiteSpace(data))
                {
                    list = JsonConvert.DeserializeObject<List<HospitalInfo>>(data);
                }

                return list;
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 获取医生信息
        /// </summary>
        /// <returns></returns>
        public List<HCPInfo> GetHCPs(string email, bool isout, string hospitalcode)
        {
            try
            {
                var pageindex = 0;
                var hasmore = true;
                //var psrid = CommandHelper.GetPsrid(email, OrganizationServiceAdmin);
                //var SaleHospital = CommandHelper.GetSaleHospital(psrid, OrganizationServiceAdmin);

                var list = new List<HCPInfo>();

                while (hasmore)
                {
                    pageindex++;
                    var qe = new QueryExpression("onepdt_t_hcp_basic_mdata");
                    qe.ColumnSet.AddColumns("onepdt_name", "onepdt_sfe_hospital_name");
                    qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    qe.Criteria.AddCondition("onepdt_customer_typename", ConditionOperator.Equal, "临床");
                    //不勾外院：本院术者：选择的植入医院下所有医生
                    if (!isout)
                    {
                        if (!string.IsNullOrWhiteSpace(hospitalcode))
                        {
                            qe.Criteria.AddCondition("onepdt_sfe_hospital_id", ConditionOperator.Equal, hospitalcode);
                        }
                    }
                    //外院：外院术者：植入医院外其他所有医院下的所有医生
                    else
                    {
                        if (!string.IsNullOrWhiteSpace(hospitalcode))
                        {
                            qe.Criteria.AddCondition("onepdt_sfe_hospital_id", ConditionOperator.NotEqual, hospitalcode);
                        }
                    }
                    var link = new LinkEntity("onepdt_t_hcp_basic_mdata", "onepdt_t_configuration", "onepdt_department", "onepdt_t_configurationid", JoinOperator.LeftOuter);
                    link.Columns.AddColumns("onepdt_name");
                    link.EntityAlias = "con";
                    qe.LinkEntities.Add(link);
                    qe.PageInfo = new PagingInfo()
                    {
                        PageNumber = pageindex,
                        Count = 5000,
                        ReturnTotalRecordCount = true
                    };
                    var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                    if (ec != null && ec.Entities.Count > 0)
                    {
                        hasmore = ec.MoreRecords;

                        var data = ec.Entities.Select(a => new HCPInfo() { id = a.Id.ToString(), name = a.GetAttributeValue<string>("onepdt_name"), department = a.Contains("con.onepdt_name") ? a.GetAttributeValue<AliasedValue>("con.onepdt_name").Value.ToString() : "", hospital = a.Contains("onepdt_sfe_hospital_name") ? a.GetAttributeValue<string>("onepdt_sfe_hospital_name") : "" }).ToList();

                        list.AddRange(data);
                    }
                    else
                    {
                        hasmore = false;
                    }
                }
                return list;
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 跟台数据编辑
        /// </summary>
        /// <param name="email"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public string EditOperation(string email, OperationEditData model)
        {
            try
            {
                //if (model.details == null || model.details.Count == 0)
                //{
                //    throw new Exception("请选择产品明细");
                //}
                //if (model.details.Any(a => string.IsNullOrWhiteSpace(a.productid)))
                //{
                //    throw new Exception("请检查产品序列号是否存在对应产品型号");
                //}
                //植入类型为“仅替换导线”时产品不能有主机，其他类型时均必须有主机
                //if (!string.IsNullOrWhiteSpace(model.implantType) && model.implantType == "仅替换导线" && model.details.Any(a => a.bigType == "主机"))
                //{
                //    throw new Exception("【仅替换导线】不能有主机");
                //}
                //if (!string.IsNullOrWhiteSpace(model.implantType) && model.implantType != "仅替换导线" && !model.details.Any(a => a.bigType == "主机"))
                //{
                //    throw new Exception($"【{model.implantType}】必须有主机");
                //}                

                var trans = new ExecuteTransactionRequest()
                {
                    Requests = new OrganizationRequestCollection(),
                    ReturnResponses = true
                };

                var entityid = string.Empty;
                var entity = new Entity("onepdt_t_operation");
                if (!string.IsNullOrWhiteSpace(model.email))
                {
                    var submiter = CommandHelper.GetEmployee(model.email, OrganizationServiceAdmin);
                    if (submiter != null)
                    {
                        entity["onepdt_submitter"] = submiter.ToEntityReference();
                        entity["onepdt_submiter_text"] = submiter.GetAttributeValue<string>("onepdt_code");
                    }
                }
                if (!string.IsNullOrWhiteSpace(model.deviceapplicationid))
                {
                    entity["onepdt_device_application"] = new EntityReference("epdt_t_device_application", new Guid(model.deviceapplicationid));
                }
                if (!string.IsNullOrWhiteSpace(model.patientName))
                {
                    entity["onepdt_patient"] = model.patientName;
                }
                if (!string.IsNullOrWhiteSpace(model.patientGender))
                {
                    entity["onepdt_gender"] = model.patientGender == "0" ? "男" : "女";
                }
                if (!string.IsNullOrWhiteSpace(model.implantHospital))
                {
                    entity["onepdt_hospitalname"] = model.implantHospital;
                }
                if (!string.IsNullOrWhiteSpace(model.implantHospitalCode))
                {
                    entity["onepdt_hospitalcode"] = model.implantHospitalCode;
                }
                if (!string.IsNullOrWhiteSpace(model.implantHospitalId))
                {
                    entity["onepdt_hospitalid"] = new EntityReference("epdt_t_hospital_basic_mdata", new Guid(model.implantHospitalId));
                }
                if (!string.IsNullOrWhiteSpace(model.implantDate))
                {
                    entity["onepdt_date"] = Convert.ToDateTime(model.implantDate);
                }
                if (!string.IsNullOrWhiteSpace(model.firstSurgeonid))
                {
                    entity["onepdt_primary_hcp"] = new EntityReference("onepdt_t_hcp_basic_mdata", new Guid(model.firstSurgeonid));
                }
                if (!string.IsNullOrWhiteSpace(model.secondSurgeonid))
                {
                    entity["onepdt_secondary_hcp"] = new EntityReference("onepdt_t_hcp_basic_mdata", new Guid(model.secondSurgeonid));
                }
                if (!string.IsNullOrWhiteSpace(model.thirdSurgeonid))
                {
                    entity["onepdt_thirdary_hcp"] = new EntityReference("onepdt_t_hcp_basic_mdata", new Guid(model.thirdSurgeonid));
                }
                if (!string.IsNullOrWhiteSpace(model.firstSurgeon) || !string.IsNullOrWhiteSpace(model.secondSurgeon) || !string.IsNullOrWhiteSpace(model.thirdSurgeon))
                {
                    var onepdt_hcp_text = model.firstSurgeon + "," + model.secondSurgeon + "," + model.thirdSurgeon;
                    entity["onepdt_hcp_text"] = onepdt_hcp_text.TrimEnd(',');
                }
                if (!string.IsNullOrWhiteSpace(model.surgicalAssistantTypeid))
                {
                    entity["onepdt_support_typeid"] = new EntityReference("onepdt_t_configuration", new Guid(model.surgicalAssistantTypeid));
                }
                if (!string.IsNullOrWhiteSpace(model.surgeonDurationid))
                {
                    entity["onepdt_durationid"] = new EntityReference("onepdt_t_configuration", new Guid(model.surgeonDurationid));
                }
                if (!string.IsNullOrWhiteSpace(model.hisPurkinjeSystemPacingid))
                {
                    entity["onepdt_xpsystemid"] = new EntityReference("onepdt_t_configuration", new Guid(model.hisPurkinjeSystemPacingid));
                }
                if (!string.IsNullOrWhiteSpace(model.xfsystemId))
                {
                    entity["onepdt_xfsystemid"] = new EntityReference("onepdt_t_configuration", new Guid(model.xfsystemId));
                }
                entity["onepdt_success_tool_list"] = model.success_tool_list;
                entity["onepdt_failed_toollist"] = model.failed_tool_list;
                if (!string.IsNullOrWhiteSpace(model.icdSurgeryProphylacticLevelid))
                {
                    entity["onepdt_icd_classid"] = new EntityReference("onepdt_t_configuration", new Guid(model.icdSurgeryProphylacticLevelid));
                }
                if (!string.IsNullOrWhiteSpace(model.implantTypeid))
                {
                    entity["onepdt_type"] = new EntityReference("onepdt_t_configuration", new Guid(model.implantTypeid));
                }
                if (!string.IsNullOrWhiteSpace(model.originaldeviceid))
                {
                    entity["onepdt_original_device"] = new EntityReference("onepdt_t_configuration", new Guid(model.originaldeviceid));
                }
                if (!string.IsNullOrWhiteSpace(model.replaceddeviceid))
                {
                    entity["onepdt_replaced_device"] = new EntityReference("onepdt_t_configuration", new Guid(model.replaceddeviceid));
                }
                if (!string.IsNullOrWhiteSpace(model.surgicalAssistant))
                {
                    entity["onepdt_salesname"] = model.surgicalAssistant;
                }
                if (!string.IsNullOrWhiteSpace(model.relatedoperationid))
                {
                    entity["onepdt_related_operation"] = new EntityReference("onepdt_t_operation", new Guid(model.relatedoperationid));
                }
                //entity["onepdt_sp_history"] = model.isIcdPatientWithCadHistory;
                //entity["onepdt_sendcard"] = model.isWarrantyCardMailed;
                entity["onepdt_icd_status"] = model.isIcdPatientWithCadHistory ? new OptionSetValue(1) : new OptionSetValue(0);
                entity["onepdt_actual_card_status"] = model.isWarrantyCardMailed ? new OptionSetValue(1) : new OptionSetValue(0);
                if (!string.IsNullOrWhiteSpace(model.receType))
                {
                    entity["onepdt_card_acquisition_type"] = new OptionSetValue(Convert.ToInt32(model.receType));
                }
                else
                {
                    entity["onepdt_card_acquisition_type"] = null;
                }
                if (!string.IsNullOrWhiteSpace(model.ksheathvalue))
                {
                    entity["onepdt_ksheath_status"] = new OptionSetValue(Convert.ToInt32(model.ksheathvalue));
                }
                if (!string.IsNullOrWhiteSpace(model.address))
                {
                    entity["onepdt_recipient_address"] = model.address;
                }
                else
                {
                    entity["onepdt_recipient_address"] = null;
                }
                if (!string.IsNullOrWhiteSpace(model.rece))
                {
                    entity["onepdt_recipient_name"] = model.rece;
                }
                else
                {
                    entity["onepdt_recipient_name"] = null;
                }
                if (!string.IsNullOrWhiteSpace(model.tel))
                {
                    entity["onepdt_recipient_phone"] = model.tel;
                }
                else
                {
                    entity["onepdt_recipient_phone"] = null;
                }
                if (!string.IsNullOrWhiteSpace(model.messageNote))
                {
                    entity["onepdt_comment"] = model.messageNote;
                }
                else
                {
                    entity["onepdt_comment"] = null;
                }
                if (!string.IsNullOrWhiteSpace(model.id))
                {
                    entityid = model.id;
                    entity.Id = new Guid(model.id);
                    //OrganizationServiceAdmin.Update(entity);
                    trans.Requests.Add(new UpdateRequest { Target = entity });
                }
                else
                {
                    var id = Guid.NewGuid();
                    entity.Id = id;
                    trans.Requests.Add(new CreateRequest { Target = entity });
                    entityid = id.ToString();
                    //entityid = OrganizationServiceAdmin.Create(entity).ToString();
                }

                if (model.deletePreviousProduct)
                {
                    var qe = new QueryExpression("onepdt_t_operation_implant");
                    qe.Criteria.AddCondition("onepdt_operation", ConditionOperator.Equal, new Guid(entityid));

                    var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                    if (ec != null && ec.Entities.Count > 0)
                    {
                        foreach (var oldProduct in ec.Entities)
                        {
                            var deleteRequest = new DeleteRequest
                            {
                                Target = new EntityReference("onepdt_t_operation_implant", oldProduct.Id)
                            };
                            trans.Requests.Add(deleteRequest);
                            // trans.Requests.Add(deleteRequest);
                        }
                    }
                }

                //跟台产品明细
                if (model.details != null && model.details.Count > 0)
                {
                    foreach (var detail in model.details)
                    {
                        if (detail.deflag && !string.IsNullOrWhiteSpace(detail.id))
                        {
                            OrganizationServiceAdmin.Delete("onepdt_t_operation_implant", new Guid(detail.id));
                        }
                        else
                        {
                            var de = new Entity("onepdt_t_operation_implant");
                            de["onepdt_name"] = string.IsNullOrWhiteSpace(detail.serialNumber) ? detail.serialNumber : detail.serialNumber.ToUpper();
                            de["onepdt_is_scrapped"] = detail.isWearAndTear;
                            de["onepdt_is_experimental"] = detail.isNonClinicalTrial;
                            de["onepdt_is_nonmainland"] = detail.isNonMainlandProduct;
                            de["onepdt_operation"] = new EntityReference("onepdt_t_operation", new Guid(entityid));
                            if (!string.IsNullOrWhiteSpace(detail.productid))
                            {
                                de["onepdt_product_mdata"] = new EntityReference("onepdt_t_product_mdata", new Guid(detail.productid));
                            }
                            if (!string.IsNullOrWhiteSpace(detail.id))
                            {
                                de.Id = new Guid(detail.id);
                                trans.Requests.Add(new UpdateRequest { Target = de });
                                //OrganizationServiceAdmin.Update(de);
                            }
                            else
                            {
                                de.Id = Guid.NewGuid();
                                trans.Requests.Add(new CreateRequest { Target = de });
                                //OrganizationServiceAdmin.Create(de).ToString();
                            }
                        }
                    }
                }
                OrganizationServiceAdmin.Execute(trans);

                CommandHelper.CreateApiLog("保存周报/跟台", JsonConvert.SerializeObject(model), entityid, "保存周报/跟台", "成功", OrganizationServiceAdmin);

                #region 注释
                ////授权书
                //if (model.authorizationLetter != null && model.authorizationLetter.Count > 0)
                //{
                //    foreach (var item in model.authorizationLetter)
                //    {
                //        var fileid = string.Empty;
                //        var photo = new Entity("onepdt_t_opreation_photo");
                //        photo["onepdt_name"] = item.file.name;
                //        photo["onepdt_classify"] = "授权书";
                //        photo["onepdt_operation"] = new EntityReference("onepdt_t_operation", new Guid(entityid));
                //        if (!string.IsNullOrWhiteSpace(item.id))
                //        {
                //            fileid = item.id;
                //            photo.Id = new Guid(item.id);
                //            OrganizationServiceAdmin.Update(photo);
                //        }
                //        else
                //        {
                //            var newid = Guid.NewGuid();
                //            photo.Id = newid;
                //            fileid = newid.ToString();
                //            OrganizationServiceAdmin.Create(photo);

                //            var base64str = string.Empty;
                //            if (item.content.IndexOf("base64,") > -1)
                //            {
                //                var strindex = item.content.IndexOf("base64,");
                //                base64str = item.content.Substring(item.content.IndexOf("base64,") + 7);
                //            }
                //            else
                //            {
                //                base64str = item.content;
                //            }
                //            if (!string.IsNullOrWhiteSpace(base64str))
                //                CommandHelper.UploadFile(OrganizationServiceAdmin, new EntityReference("onepdt_t_opreation_photo", new Guid(fileid)), "onepdt_photo", item.file.name, Convert.FromBase64String(base64str));
                //        }
                //    }
                //}

                ////回执单
                //if (model.receiptForm != null && model.receiptForm.Count > 0)
                //{
                //    foreach (var item in model.receiptForm)
                //    {
                //        var fileid = string.Empty;
                //        var photo = new Entity("onepdt_t_opreation_photo");
                //        photo["onepdt_name"] = item.file.name;
                //        photo["onepdt_classify"] = "回执单";
                //        photo["onepdt_operation"] = new EntityReference("onepdt_t_operation", new Guid(entityid));
                //        if (!string.IsNullOrWhiteSpace(item.id))
                //        {
                //            fileid = item.id;
                //            photo.Id = new Guid(item.id);
                //            OrganizationServiceAdmin.Update(photo);
                //        }
                //        else
                //        {
                //            var newid = Guid.NewGuid();
                //            photo.Id = newid;
                //            fileid = newid.ToString();
                //            OrganizationServiceAdmin.Create(photo);

                //            var base64str = string.Empty;
                //            if (item.content.IndexOf("base64,") > -1)
                //            {
                //                var strindex = item.content.IndexOf("base64,");
                //                base64str = item.content.Substring(item.content.IndexOf("base64,") + 7);
                //            }
                //            else
                //            {
                //                base64str = item.content;
                //            }
                //            if (!string.IsNullOrWhiteSpace(base64str))
                //                CommandHelper.UploadFile(OrganizationServiceAdmin, new EntityReference("onepdt_t_opreation_photo", new Guid(fileid)), "onepdt_photo", item.file.name, Convert.FromBase64String(base64str));
                //        }
                //    }
                //} 
                #endregion

                return entityid;
            }
            catch (Exception ex)
            {
                CommandHelper.CreateApiLog("保存周报/跟台", JsonConvert.SerializeObject(model), ex.Message, "保存周报/跟台", "失败", OrganizationServiceAdmin);
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 根据产品序列号获取SAP产品
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public List<SAPProduct> GetSAPProduct(string name, bool isNonMainlandProduct)
        {
            try
            {
                var list = new List<SAPProduct>();
                if (string.IsNullOrWhiteSpace(name))
                {
                    throw new Exception("请传入序列号");
                }
                if (isNonMainlandProduct)
                {
                    var qe = new QueryExpression("onepdt_t_nonmainland_management");
                    qe.ColumnSet.AllColumns = true;
                    qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    qe.Criteria.AddCondition("onepdt_sn", ConditionOperator.Equal, name);
                    var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                    if (ec != null && ec.Entities.Count > 0)
                    {
                        foreach (var item in ec.Entities)
                        {
                            var product = new SAPProduct();
                            product.id = item.Id.ToString();
                            product.zmod = item.GetAttributeValue<string>("onepdt_name");
                            list.Add(product);
                        }

                    }
                }
                else
                {
                    var qe = new QueryExpression("onepdt_t_sap_product_mdata");
                    qe.ColumnSet.AllColumns = true;
                    qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    qe.Criteria.AddCondition("onepdt_sn", ConditionOperator.Equal, name);
                    var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                    if (ec != null && ec.Entities.Count > 0)
                    {
                        foreach (var item in ec.Entities)
                        {
                            var product = new SAPProduct();
                            product.id = item.Id.ToString();
                            product.zmod = item.GetAttributeValue<string>("onepdt_zmod");
                            list.Add(product);
                        }
                    }
                    else
                    {
                        var qe2 = new QueryExpression("onepdt_t_product_mdata");
                        qe2.ColumnSet.AllColumns = true;
                        qe2.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                        qe2.Criteria.AddCondition("onepdt_name", ConditionOperator.Equal, name);
                        qe2.Criteria.AddCondition("onepdt_type_idname", ConditionOperator.Equal, "配件");
                        var ec2 = OrganizationServiceAdmin.RetrieveMultiple(qe2);
                        if (ec2 != null && ec2.Entities.Count > 0)
                        {
                            foreach (var item in ec2.Entities)
                            {
                                var product = new SAPProduct();
                                product.id = item.Id.ToString();
                                product.zmod = item.GetAttributeValue<string>("onepdt_name");
                                list.Add(product);
                            }
                        }
                    }
                }

                return list;
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 根据产品序列号获取产品
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public OnePDTProduct GetOnePDTProduct(string name)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(name))
                {
                    throw new Exception("请传入序列号");
                }
                var product = new OnePDTProduct();
                var qe = new QueryExpression("onepdt_t_product_mdata");
                qe.ColumnSet.AllColumns = true;
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition("onepdt_name", ConditionOperator.Equal, name);
                var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                if (ec != null && ec.Entities.Count > 0)
                {
                    var item = ec.Entities[0];
                    product.id = item.Id.ToString();
                    product.onepdt_name = item.GetAttributeValue<string>("onepdt_name");
                    if (item.Contains("onepdt_type_id"))
                        product.onepdt_type = item.GetAttributeValue<EntityReference>("onepdt_type_id").Name;
                    if (item.Contains("onepdt_classification_id"))
                        product.onepdt_classification = item.GetAttributeValue<EntityReference>("onepdt_classification_id").Name;
                    if (item.Contains("onepdt_category_id"))
                        product.onepdt_category = item.GetAttributeValue<EntityReference>("onepdt_category_id").Name;
                }
                return product;
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 获取跟台数据详情
        /// </summary>
        /// <param name="operationid">跟台数据id</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public OperationEditData GetOperation(string operationid, string email)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(operationid))
                {
                    throw new Exception("跟台数据id丢失");
                }
                var model = new OperationEditData();

                #region 主档
                var operation = OrganizationServiceAdmin.Retrieve("onepdt_t_operation", new Guid(operationid), new ColumnSet(true));
                model.id = operationid;
                if (operation.Contains("onepdt_device_application"))
                {
                    model.deviceapplicationid = operation.GetAttributeValue<EntityReference>("onepdt_device_application").Id.ToString();
                    var qe2 = new QueryExpression("epdt_t_device_application");
                    qe2.ColumnSet.AddColumns("epdt_name", "epdt_patient_abbottid", "epdt_hospital_code", "epdt_implant_date");
                    qe2.Criteria.AddCondition("epdt_t_device_applicationid", ConditionOperator.Equal, model.deviceapplicationid);
                    var link1 = new LinkEntity("epdt_t_device_application", "epdt_t_patient", "epdt_patient_abbottid", "epdt_t_patientid", JoinOperator.Inner);
                    link1.Columns.AddColumns("epdt_name", "epdt_patient_gender", "epdt_recipient_name", "epdt_recipient_phone", "epdt_mailing_address", "epdt_if_need_paper_card", "epdt_card_acquisition_method");
                    link1.EntityAlias = "p";
                    qe2.LinkEntities.Add(link1);
                    var link2 = new LinkEntity("epdt_t_device_application", "epdt_t_hospital_basic_mdata", "epdt_hospital_code", "epdt_t_hospital_basic_mdataid", JoinOperator.Inner);
                    link2.Columns.AddColumns("epdt_name", "epdt_hospital_code");
                    link2.EntityAlias = "hosp";
                    qe2.LinkEntities.Add(link2);
                    var ec2 = OrganizationServiceAdmin.RetrieveMultiple(qe2);
                    if (ec2 != null && ec2.Entities.Count > 0)
                    {
                        var item = ec2.Entities[0];
                        model.devpatientName = item.Contains("p.epdt_name") ? item.GetAttributeValue<AliasedValue>("p.epdt_name").Value.ToString() : "";
                        model.devpatientGender = item.Contains("p.epdt_patient_gender") ? (item.GetAttributeValue<AliasedValue>("p.epdt_patient_gender").Value.ToString() == "男" ? "0" : "1") : "";
                        model.devimplantDate = item.Contains("epdt_implant_date") ? item.GetAttributeValue<DateTime>("epdt_implant_date").AddHours(8).ToString("yyyy/MM/dd") : "";
                        model.devimplantHospital = item.Contains("epdt_hospital_code") ? item.GetAttributeValue<EntityReference>("epdt_hospital_code").Name : "";
                        model.devimplantHospitalId = item.Contains("epdt_hospital_code") ? item.GetAttributeValue<EntityReference>("epdt_hospital_code").Id.ToString() : "";
                        model.devimplantHospitalCode = item.Contains("hosp.epdt_hospital_code") ? item.GetAttributeValue<AliasedValue>("hosp.epdt_hospital_code").Value.ToString() : "";
                    }
                }
                if (operation.Contains("onepdt_patient"))
                {
                    model.patientName = operation.GetAttributeValue<string>("onepdt_patient");
                }
                if (operation.Contains("onepdt_gender"))
                {
                    model.patientGender = operation.GetAttributeValue<string>("onepdt_gender") == "男" ? "0" : "1";
                }
                if (operation.Contains("onepdt_hospitalname"))
                {
                    model.implantHospital = operation.GetAttributeValue<string>("onepdt_hospitalname");
                }
                if (operation.Contains("onepdt_hospitalcode"))
                {
                    model.implantHospitalCode = operation.GetAttributeValue<string>("onepdt_hospitalcode");
                }
                if (operation.Contains("onepdt_hospitalid"))
                {
                    model.implantHospitalId = operation.GetAttributeValue<EntityReference>("onepdt_hospitalid").Id.ToString();
                }
                if (operation.Contains("onepdt_date"))
                {
                    model.implantDate = operation.GetAttributeValue<DateTime>("onepdt_date").AddHours(8).ToString("yyyy/MM/dd");
                }
                if (operation.Contains("onepdt_primary_hcp"))
                {
                    model.firstSurgeonid = operation.GetAttributeValue<EntityReference>("onepdt_primary_hcp").Id.ToString();
                    model.firstSurgeon = operation.GetAttributeValue<EntityReference>("onepdt_primary_hcp").Name;
                }
                if (operation.Contains("onepdt_secondary_hcp"))
                {
                    model.secondSurgeonid = operation.GetAttributeValue<EntityReference>("onepdt_secondary_hcp").Id.ToString();
                    model.secondSurgeon = operation.GetAttributeValue<EntityReference>("onepdt_secondary_hcp").Name;
                }
                if (operation.Contains("onepdt_thirdary_hcp"))
                {
                    model.thirdSurgeonid = operation.GetAttributeValue<EntityReference>("onepdt_thirdary_hcp").Id.ToString();
                    model.thirdSurgeon = operation.GetAttributeValue<EntityReference>("onepdt_thirdary_hcp").Name;
                }
                if (operation.Contains("onepdt_support_typeid"))
                {
                    model.surgicalAssistantTypeid = operation.GetAttributeValue<EntityReference>("onepdt_support_typeid").Id.ToString();
                    model.surgicalAssistantType = operation.GetAttributeValue<EntityReference>("onepdt_support_typeid").Name.ToString();
                }
                if (operation.Contains("onepdt_durationid"))
                {
                    model.surgeonDurationid = operation.GetAttributeValue<EntityReference>("onepdt_durationid").Id.ToString();
                    model.surgeonDuration = operation.GetAttributeValue<EntityReference>("onepdt_durationid").Name.ToString();
                }
                if (operation.Contains("onepdt_xpsystemid"))
                {
                    model.hisPurkinjeSystemPacingid = operation.GetAttributeValue<EntityReference>("onepdt_xpsystemid").Id.ToString();
                    model.hisPurkinjeSystemPacing = operation.GetAttributeValue<EntityReference>("onepdt_xpsystemid").Name.ToString();
                }
                if (operation.Contains("onepdt_xfsystemid"))
                {
                    model.xfsystemId = operation.GetAttributeValue<EntityReference>("onepdt_xfsystemid").Id.ToString();
                    model.xfsystem = operation.GetAttributeValue<EntityReference>("onepdt_xfsystemid").Name.ToString();
                }
                if (operation.Contains("onepdt_success_tool_list"))
                {
                    model.success_tool_list = operation.GetAttributeValue<string>("onepdt_success_tool_list");
                }
                if (operation.Contains("onepdt_failed_toollist"))
                {
                    model.failed_tool_list = operation.GetAttributeValue<string>("onepdt_failed_toollist");
                    model.failtoolslist = operation.GetAttributeValue<string>("onepdt_failed_toollist").Split('、').Select(a => new OnepdtConfig() { text = a.ToString(), value = a.ToString() }).ToList();
                }
                if (operation.Contains("onepdt_icd_classid"))
                {
                    model.icdSurgeryProphylacticLevelid = operation.GetAttributeValue<EntityReference>("onepdt_icd_classid").Id.ToString();
                    model.icdSurgeryProphylacticLevel = operation.GetAttributeValue<EntityReference>("onepdt_icd_classid").Name.ToString();
                }
                if (operation.Contains("onepdt_type"))
                {
                    model.implantTypeid = operation.GetAttributeValue<EntityReference>("onepdt_type").Id.ToString();
                    model.implantType = operation.GetAttributeValue<EntityReference>("onepdt_type").Name.ToString();
                }
                if (operation.Contains("onepdt_original_device"))
                {
                    model.originaldeviceid = operation.GetAttributeValue<EntityReference>("onepdt_original_device").Id.ToString();
                    model.originaldevice = operation.GetAttributeValue<EntityReference>("onepdt_original_device").Name.ToString();
                }
                if (operation.Contains("onepdt_replaced_device"))
                {
                    model.replaceddeviceid = operation.GetAttributeValue<EntityReference>("onepdt_replaced_device").Id.ToString();
                    model.replaceddevice = operation.GetAttributeValue<EntityReference>("onepdt_replaced_device").Name.ToString();
                }
                if (operation.Contains("onepdt_salesname"))
                {
                    model.surgicalAssistant = operation.GetAttributeValue<string>("onepdt_salesname");
                }
                if (operation.Contains("onepdt_card_acquisition_type"))
                {
                    model.receType = operation.GetAttributeValue<OptionSetValue>("onepdt_card_acquisition_type").Value.ToString();
                }
                //model.isIcdPatientWithCadHistory = operation.GetAttributeValue<bool>("onepdt_sp_history");
                //model.isWarrantyCardMailed = operation.GetAttributeValue<bool>("onepdt_sendcard");
                if (operation.Contains("onepdt_icd_status"))
                {
                    model.isIcdPatientWithCadHistory = operation.GetAttributeValue<OptionSetValue>("onepdt_icd_status").Value == 0 ? false : true;
                }
                if (operation.Contains("onepdt_actual_card_status"))
                {
                    model.isWarrantyCardMailed = operation.GetAttributeValue<OptionSetValue>("onepdt_actual_card_status").Value == 0 ? false : true;
                }
                if (operation.Contains("onepdt_ksheath_status"))
                {
                    model.ksheathvalue = operation.GetAttributeValue<OptionSetValue>("onepdt_ksheath_status").Value.ToString();
                    model.ksheathlabel = operation.FormattedValues["onepdt_ksheath_status"];
                }
                if (operation.Contains("onepdt_related_operation"))
                {
                    var relatedoperationid = operation.GetAttributeValue<EntityReference>("onepdt_related_operation").Id.ToString();
                    model.relatedoperationid = relatedoperationid;

                    var relatedoperation = OrganizationServiceAdmin.Retrieve("onepdt_t_operation", new Guid(relatedoperationid), new ColumnSet("onepdt_patient", "onepdt_gender", "onepdt_hospitalname", "onepdt_date"));
                    model.relatedoperationpatient = relatedoperation.GetAttributeValue<string>("onepdt_patient");
                    model.relatedoperationgender = relatedoperation.GetAttributeValue<string>("onepdt_gender");
                    model.relatedoperationhospitalname = relatedoperation.GetAttributeValue<string>("onepdt_hospitalname");
                    if (relatedoperation.Contains("onepdt_date"))
                        model.relatedoperationdate = relatedoperation.GetAttributeValue<DateTime>("onepdt_date").AddHours(8).ToString("yyyy/MM/dd");
                    //产品型号名称
                    var productnamelist = GetProductname(new List<string>() { relatedoperationid });
                    if (productnamelist != null && productnamelist.Count > 0 && !string.IsNullOrWhiteSpace(productnamelist[0].text)) { model.relatedoperationproduct_name = productnamelist[0].text; }
                    if (productnamelist != null && productnamelist.Count > 0 && !string.IsNullOrWhiteSpace(productnamelist[0].text1)) { model.relatedoperationproduct_sn = productnamelist[0].text1; }
                }
                model.onepdt_is_returned = operation.GetAttributeValue<bool>("onepdt_is_returned");
                model.address = operation.GetAttributeValue<string>("onepdt_recipient_address");
                model.rece = operation.GetAttributeValue<string>("onepdt_recipient_name");
                model.tel = operation.GetAttributeValue<string>("onepdt_recipient_phone");
                model.messageNote = operation.GetAttributeValue<string>("onepdt_comment");
                if (operation.Contains("onepdt_submit_status"))
                {
                    model.onepdt_submit_status_label = operation.GetAttributeValue<bool>("onepdt_submit_status") ? "跟台" : "周报";
                }
                if (operation.Contains("onepdt_approval_status"))
                {
                    model.onepdt_approval_status = operation.GetAttributeValue<OptionSetValue>("onepdt_approval_status").Value;
                    model.onepdt_approval_statuslabel = operation.FormattedValues["onepdt_approval_status"];
                }
                if (operation.Contains("onepdt_address_approval_status"))
                {
                    model.onepdt_address_approval_status = operation.GetAttributeValue<OptionSetValue>("onepdt_address_approval_status").Value;
                    model.onepdt_address_approval_statuslabel = operation.FormattedValues["onepdt_address_approval_status"];
                }
                if (operation.Contains("onepdt_address_approval_comment"))
                {
                    model.onepdt_address_approval_comment = operation.GetAttributeValue<string>("onepdt_address_approval_comment");
                }
                if (operation.Contains("onepdt_address_approval_tag"))
                {
                    model.onepdt_address_approval_tag = operation.GetAttributeValue<string>("onepdt_address_approval_tag");
                }
                if (operation.Contains("onepdt_approval_comment"))
                {
                    model.onepdt_approval_comment = operation.GetAttributeValue<string>("onepdt_approval_comment");
                }
                if (operation.Contains("onepdt_approval_tag"))
                {
                    model.onepdt_approval_tag = operation.GetAttributeValue<string>("onepdt_approval_tag");
                }
                if (operation.Contains("onepdt_submitter"))
                {
                    var emp = OrganizationServiceAdmin.Retrieve("onepdt_t_employee_basic_mdata", operation.GetAttributeValue<EntityReference>("onepdt_submitter").Id, new ColumnSet("onepdt_mail"));
                    model.submitteremail = emp.GetAttributeValue<string>("onepdt_mail");
                    if (!String.IsNullOrWhiteSpace(model.submitteremail) && model.submitteremail.ToLower() != email.ToLower())
                    {
                        model.isedit = false;
                    }
                }
                #endregion

                #region 明细
                var qe = new QueryExpression("onepdt_t_operation_implant");
                qe.ColumnSet.AllColumns = true;
                //qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition("onepdt_operation", ConditionOperator.Equal, operationid);
                var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                if (ec != null && ec.Entities.Count > 0)
                {
                    var details = new List<OperationProductDetail>();
                    foreach (var entity in ec.Entities)
                    {
                        var de = new OperationProductDetail();
                        de.id = entity.Id.ToString();
                        de.serialNumber = entity.GetAttributeValue<string>("onepdt_name");
                        de.isWearAndTear = entity.GetAttributeValue<bool>("onepdt_is_scrapped");
                        de.isNonClinicalTrial = entity.GetAttributeValue<bool>("onepdt_is_experimental");
                        de.isNonMainlandProduct = entity.GetAttributeValue<bool>("onepdt_is_nonmainland");
                        if (entity.Contains("onepdt_product_mdata"))
                        {
                            de.model = entity.GetAttributeValue<EntityReference>("onepdt_product_mdata").Name;
                            de.productid = entity.GetAttributeValue<EntityReference>("onepdt_product_mdata").Id.ToString();

                            var product = OrganizationServiceAdmin.Retrieve("onepdt_t_product_mdata", entity.GetAttributeValue<EntityReference>("onepdt_product_mdata").Id, new ColumnSet("onepdt_type", "onepdt_type_id", "onepdt_category_id"));
                            if (product.Contains("onepdt_type_id"))
                                de.bigType = product.GetAttributeValue<EntityReference>("onepdt_type_id").Name;
                            //de.bigType = product.GetAttributeValue<string>("onepdt_type");
                            if (product.Contains("onepdt_category_id"))
                                de.category = product.GetAttributeValue<EntityReference>("onepdt_category_id").Name;
                        }
                        de.SAPProduct = GetSAPProduct(de.serialNumber, de.isNonMainlandProduct);
                        details.Add(de);
                    }
                    model.details = details;
                }
                #endregion

                #region 跟台图片
                var qe1 = new QueryExpression("onepdt_t_opreation_photo");
                qe1.ColumnSet.AllColumns = true;
                qe1.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe1.Criteria.AddCondition("onepdt_operation", ConditionOperator.Equal, operationid);
                var ec1 = OrganizationServiceAdmin.RetrieveMultiple(qe1);
                if (ec1 != null && ec1.Entities.Count > 0)
                {
                    #region 授权书
                    var letterlist = new List<OpreationPhoto>();
                    var letters = ec1.Entities.Where(a => a.GetAttributeValue<string>("onepdt_classify") == "授权书").ToList();
                    foreach (var item in letters)
                    {
                        var photo = new OpreationPhoto();
                        photo.id = item.Id.ToString();
                        //var filename = item.GetAttributeValue<string>("onepdt_name");
                        //var content = CommandHelper.DownloadFile(OrganizationServiceAdmin, item.ToEntityReference(), "onepdt_photo");
                        //var minetype = CommandHelper.GetMimeType(filename);
                        //var base64 = $"data:{minetype};base64,{content}";
                        //photo.content = base64;
                        //photo.file = new FileModel() { name = filename, type = minetype };
                        letterlist.Add(photo);
                    }
                    model.authorizationLetter = letterlist;
                    #endregion

                    #region 回执单
                    var receiptlist = new List<OpreationPhoto>();
                    var receipts = ec1.Entities.Where(a => a.GetAttributeValue<string>("onepdt_classify") == "回执单").ToList();
                    foreach (var item in receipts)
                    {
                        var photo = new OpreationPhoto();
                        photo.id = item.Id.ToString();
                        //var filename = item.GetAttributeValue<string>("onepdt_name");
                        //var content = CommandHelper.DownloadFile(OrganizationServiceAdmin, item.ToEntityReference(), "onepdt_photo");
                        //var minetype = CommandHelper.GetMimeType(filename);
                        //var base64 = $"data:{minetype};base64,{content}";
                        //photo.content = base64;
                        //photo.file = new FileModel() { name = filename, type = minetype };
                        receiptlist.Add(photo);
                    }
                    model.receiptForm = receiptlist;
                    #endregion
                }
                #endregion

                return model;
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 获取跟台图片
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public OpreationPhoto GetPhoto(string id)
        {
            var photo = new OpreationPhoto();
            if (!string.IsNullOrWhiteSpace(id))
            {
                var item = OrganizationServiceAdmin.Retrieve("onepdt_t_opreation_photo", new Guid(id), new ColumnSet(true));
                //if (item.Contains("onepdt_photo"))
                //{
                photo.id = item.Id.ToString();
                var filename = item.GetAttributeValue<string>("onepdt_name");
                var content = CommandHelper.DownloadFile(OrganizationServiceAdmin, item.ToEntityReference(), "onepdt_photo");
                var minetype = CommandHelper.GetMimeType(filename);
                var base64 = $"data:{minetype};base64,{content}";
                photo.content = base64;
                photo.file = new FileModel() { name = filename, type = minetype };
                //}

            }
            return photo;
        }

        /// <summary>
        /// 提交跟台
        /// </summary>
        /// <param name="operationid">跟台数据id</param>
        public void SubmitOperation(string operationid)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(operationid))
                {
                    throw new Exception("跟台数据id丢失");
                }

                var operation = OrganizationServiceAdmin.Retrieve("onepdt_t_operation", new Guid(operationid), new ColumnSet("onepdt_approval_status", "onepdt_card_acquisition_type", "onepdt_address_approval_status", "onepdt_submit_status"));

                //更新跟台审批状态=待审批 提交状态=跟台
                var entity = new Entity("onepdt_t_operation", new Guid(operationid));
                if (!operation.Contains("onepdt_approval_status") || (operation.Contains("onepdt_approval_status") && operation.GetAttributeValue<OptionSetValue>("onepdt_approval_status").Value != 1))
                    entity["onepdt_approval_status"] = new OptionSetValue(0);
                if (!operation.Contains("onepdt_address_approval_status") || (operation.Contains("onepdt_address_approval_status") && operation.GetAttributeValue<OptionSetValue>("onepdt_address_approval_status").Value != 1))
                    entity["onepdt_address_approval_status"] = new OptionSetValue(0);
                entity["onepdt_submit_status"] = true;
                //周报提交时：是否退回=否
                if (!operation.GetAttributeValue<bool>("onepdt_submit_status"))
                {
                    entity["onepdt_is_returned"] = false;
                }

                //邮寄拒绝的数据：提交后需根据最新的【收件人】判断：若为自定义则邮寄审批”待审批“，若为销售则邮寄审批”通过“
                //if (operation.Contains("onepdt_address_approval_status") && operation.GetAttributeValue<OptionSetValue>("onepdt_address_approval_status").Value == 2)
                //{
                //    if (operation.Contains("onepdt_card_acquisition_type"))
                //    {
                //        if (operation.GetAttributeValue<OptionSetValue>("onepdt_card_acquisition_type").Value == 1)//自定义
                //        {
                //            entity["onepdt_address_approval_status"] = new OptionSetValue(0);
                //        }
                //        else//销售本人
                //        {
                //            entity["onepdt_address_approval_status"] = new OptionSetValue(1);
                //        }
                //    }
                //}

                OrganizationServiceAdmin.Update(entity);


            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="entityname">实体名</param>
        /// <param name="entityid">实体id</param>
        public void Delete(string entityname, string entityid)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(entityname))
                {
                    throw new Exception("实体名丢失");
                }
                if (string.IsNullOrWhiteSpace(entityid))
                {
                    throw new Exception("实体id丢失");
                }

                if (entityname == "onepdt_t_operation")
                {
                    var entity = new Entity(entityname, new Guid(entityid));
                    entity["statecode"] = new OptionSetValue(1);
                    OrganizationServiceAdmin.Update(entity);
                }
                else
                {
                    //删除
                    OrganizationServiceAdmin.Delete(entityname, new Guid(entityid));
                }
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="model">删除对象</param>
        public void BatchDelete(BatchDeleteModel model)
        {
            try
            {
                if (model == null)
                {
                    throw new Exception("传入信息丢失");
                }
                if (string.IsNullOrWhiteSpace(model.entityname))
                {
                    throw new Exception("实体名丢失");
                }
                if (model.entityids == null || model.entityids.Count == 0)
                {
                    throw new Exception("实体id丢失");
                }

                //删除
                var trans = new ExecuteTransactionRequest()
                {
                    Requests = new OrganizationRequestCollection(),
                    ReturnResponses = true
                };
                foreach (var item in model.entityids)
                {
                    trans.Requests.Add(new DeleteRequest { Target = new EntityReference(model.entityname, new Guid(item)) });
                }
                OrganizationServiceAdmin.Execute(trans);
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 预览保卡
        /// </summary>
        /// <param name="operationid">跟台数据id</param>
        /// <returns></returns>
        public List<CardTypeModel> GetCardTypes(string operationid, bool IsPreview)
        {
            try
            {
                var res = new List<CardTypeModel>();
                var operation = OrganizationServiceAdmin.Retrieve("onepdt_t_operation", new Guid(operationid), new ColumnSet("onepdt_patient_id_card"));

                var response = CommandHelper.InvokeAction(OrganizationServiceAdmin, "onepdt_identify_cardtype", new Dictionary<string, object> { { "OpreationId", operationid }, { "IsPreview", !operation.Contains("onepdt_patient_id_card") } });
                if (response != null && response.Results != null && response.Results.ContainsKey("Result"))
                {
                    var Results = (string)response.Results["Result"];
                    res = JsonConvert.DeserializeObject<List<CardTypeModel>>(Results);
                }
                return res;
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 跟台查询
        /// </summary>
        /// <param name="roleid">角色id</param>
        /// <param name="proxyemail">代理邮箱</param>
        /// <param name="email">邮箱</param>
        /// <param name="searchText">搜索框</param>
        /// <param name="implantType">植入类型</param>
        /// <param name="starttime">植入时间起</param>
        /// <param name="endtime">植入时间止</param>
        /// <returns></returns>
        public OperationData GetOperationQuery(string roleid, string proxyemail, string email, int pagesize, int pageindex, string searchText, string productname, string submittername, string province, string implantType, string starttime, string endtime)
        {
            try
            {
                var data = new OperationData() { datas = new List<OperationModel>() };
                var list = new List<OperationModel>();

                if (!string.IsNullOrWhiteSpace(proxyemail) && !string.IsNullOrWhiteSpace(email))
                {
                    var fetchxml1 = $@"<fetch version='1.0' mapping='logical' no-lock='false' distinct='true'>
                          <entity name='onepdt_t_employeerole'>
                            <attribute name='onepdt_name'/>
                            <attribute name='statecode'/>
                            <attribute name='createdon'/>
                            <order attribute='createdon' descending='false'/>
                            <attribute name='onepdt_role_id'/>
                            <attribute name='onepdt_employee_id'/>
                            <attribute name='onepdt_t_employeeroleid'/>
                            <filter type='and'>
                              <condition attribute='statecode' operator='eq' value='0'/>
                            </filter>
                            <link-entity name='onepdt_t_employee_basic_mdata' alias='aa' link-type='inner' from='onepdt_t_employee_basic_mdataid' to='onepdt_employee_id'>
                              <filter type='and'>
                                <condition attribute='onepdt_mail' operator='eq' value='{email}'/>
                              </filter>
                            </link-entity>
                            <link-entity name='onepdt_t_employee_basic_mdata' alias='ab' link-type='inner' from='onepdt_t_employee_basic_mdataid' to='onepdt_sales_employee_id'>
                              <filter type='and'>
                                <condition attribute='onepdt_mail' operator='eq' value='{proxyemail}'/>
                              </filter>
                            </link-entity>
                          </entity>
                        </fetch>";
                    var proxyec = OrganizationServiceAdmin.RetrieveMultiple(new FetchExpression(fetchxml1));
                    if (proxyec == null || proxyec.Entities.Count == 0)
                    {
                        throw new Exception($"【{email}】没有代理【{proxyemail}】的权限");
                    }
                }

                //查询角色
                var hosp = new List<string>();
                var role = OrganizationServiceAdmin.Retrieve("onepdt_t_role", new Guid(roleid), new ColumnSet("onepdt_name"));
                if (role.Contains("onepdt_name") && role.GetAttributeValue<string>("onepdt_name").ToLower().Contains("管理员") || role.GetAttributeValue<string>("onepdt_name").ToLower().Contains("admin"))
                {

                }
                else
                {
                    //查询销售架构对应销售代表
                    var prsid = CommandHelper.GetSalesOrgCode(roleid, !string.IsNullOrWhiteSpace(proxyemail) ? proxyemail : email, OrganizationServiceAdmin);
                    if (prsid == null || prsid.Count == 0)
                    {
                        throw new Exception($"不存在{(!string.IsNullOrWhiteSpace(proxyemail) ? proxyemail : email)}销售架构信息");
                    }
                    //查询有权限的医院code
                    hosp = CommandHelper.GetHospitalCode(prsid, role.GetAttributeValue<string>("onepdt_name").ToLower() == "销售员", OrganizationServiceAdmin);
                    if (hosp == null || hosp.Count == 0)
                    {
                        return data;
                    }
                }

                var condotion = "";
                if (hosp.Count > 0)
                {
                    condotion += $"<condition attribute='onepdt_hospitalcode' operator='in'><value>{string.Join("</value><value>", hosp)}</value></condition>";
                }
                if (!string.IsNullOrWhiteSpace(implantType))
                {
                    condotion += $"<condition attribute='onepdt_typename' operator='in'><value>{string.Join("</value><value>", implantType.Split('、').ToList())}</value></condition>";
                }
                if (!string.IsNullOrWhiteSpace(starttime) && !string.IsNullOrWhiteSpace(endtime))
                {
                    condotion += $@"<condition attribute='onepdt_date' operator='on-or-after' value='{starttime}'/>
                                    <condition attribute='onepdt_date' operator='on-or-before' value='{endtime}'/>";
                }
                if (!string.IsNullOrWhiteSpace(productname))
                {
                    var proarr = productname.Split('、').ToList();
                    if (proarr != null && proarr.Count > 0)
                    {
                        condotion += "<filter type='or'>";
                        proarr.ForEach(a =>
                        {
                            condotion += $@"<condition entityname='product' attribute='onepdt_product_mdataname' operator='like' value='%{a}%'/>";
                        });
                        condotion += "</filter>";
                    }
                }
                if (!string.IsNullOrWhiteSpace(submittername))
                {
                    var subarr = submittername.Split('、').ToList();
                    if (subarr != null && subarr.Count > 0)
                    {
                        condotion += "<filter type='or'>";
                        subarr.ForEach(a =>
                        {
                            condotion += $@"<condition attribute='onepdt_submittername' operator='like' value='%{a}%'/>";
                        });
                        condotion += "</filter>";
                    }
                }
                //if (!string.IsNullOrWhiteSpace(province))
                //{
                //    var provarr = province.Split('、').ToList();
                //    if (provarr != null && provarr.Count > 0)
                //    {
                //        condotion += "<filter type='or'>";
                //        provarr.ForEach(a =>
                //        {
                //            condotion += $@"<condition entityname='hos' attribute='epdt_hospital_province' operator='like' value='%{a}%'/>";
                //        });
                //        condotion += "</filter>";
                //    }
                //}
                if (!string.IsNullOrWhiteSpace(searchText))
                {
                    condotion += $@"<filter type='or'>
                                        <condition attribute='onepdt_name' operator='like' value='%{searchText}%'/>
                                        <condition attribute='onepdt_hospitalname' operator='like' value='%{searchText}%'/>
                                        <condition attribute='onepdt_patient' operator='like' value='%{searchText}%'/>
                                        <condition entityname='hos' attribute='epdt_hospital_province' operator='like' value='%{searchText}%'/>
                                     </filter>";
                }


                var fetchxml = @"<fetch version='1.0' mapping='logical' no-lock='false' distinct='true' {0}>
                        <entity name='onepdt_t_operation'>
                        <all-attributes/>
                        <order attribute='onepdt_date' descending='true'/>
                        <link-entity name='epdt_t_device_application' alias='device' link-type='outer' from='epdt_t_device_applicationid' to='onepdt_device_application'>
                            <attribute name='epdt_e_card_status'/>
                        </link-entity>
                        <link-entity name='epdt_t_hospital_basic_mdata' alias='hos' link-type='outer' from='epdt_t_hospital_basic_mdataid' to='onepdt_hospitalid'></link-entity>
                        <link-entity name='onepdt_t_operation_implant' alias='product' link-type='outer' from='onepdt_operation' to='onepdt_t_operationid'/>
                            <filter type='and'>
                                <condition attribute='statecode' operator='eq' value='0'/> {1}
                            </filter>
                        </entity>
                    </fetch>";
                var fe = string.Format(fetchxml, $"page='{pageindex}' count='{pagesize}' returntotalrecordcount='true'", condotion);
                var ec = OrganizationServiceAdmin.RetrieveMultiple(new FetchExpression(fe));

                if (ec != null && ec.Entities.Count > 0)
                {
                    data.total = ec.TotalRecordCount;
                    var ids = ec.Entities.Select(a => a.Id.ToString()).ToList();
                    //产品型号名称
                    var productnamelist = GetProductname(ids);
                    //打印状态
                    var printstatuslist = GetPintStatus(ids);

                    foreach (var item in ec.Entities)
                    {
                        var model = new OperationModel();
                        model.id = item.Id.ToString();
                        model.onepdt_name = item.GetAttributeValue<string>("onepdt_name");
                        model.onepdt_hospitalname = item.GetAttributeValue<string>("onepdt_hospitalname");
                        model.onepdt_hospitalcode = item.GetAttributeValue<string>("onepdt_hospitalcode");
                        model.onepdt_patient = item.GetAttributeValue<string>("onepdt_patient");
                        model.onepdt_hcp_text = item.GetAttributeValue<string>("onepdt_hcp_text");
                        if (item.Contains("onepdt_submit_status"))
                        {
                            model.onepdt_submit_status_label = item.GetAttributeValue<bool>("onepdt_submit_status") ? "跟台" : "周报";
                        }
                        if (item.Contains("onepdt_approval_status"))
                        {
                            model.onepdt_approval_status_label = item.FormattedValues["onepdt_approval_status"];
                        }
                        if (item.Contains("onepdt_address_approval_status"))
                        {
                            model.onepdt_address_approval_status_label = item.FormattedValues["onepdt_address_approval_status"];
                        }
                        if (item.Contains("onepdt_date"))
                        {
                            model.onepdt_date = item.GetAttributeValue<DateTime>("onepdt_date").AddHours(8).ToString("yyyy-MM-dd");
                        }
                        if (item.Contains("onepdt_submitter"))
                        {
                            model.onepdt_submittername = item.GetAttributeValue<EntityReference>("onepdt_submitter").Name;
                        }
                        if (item.Contains("onepdt_archival_status"))
                        {
                            model.onepdt_archival_statuslabel = item.FormattedValues["onepdt_archival_status"];
                        }
                        if (item.Contains("device.epdt_e_card_status"))
                        {
                            model.onepdt_device_applicationname = item.GetAttributeValue<AliasedValue>("device.epdt_e_card_status").Value.ToString();
                        }
                        model.onepdt_print_statuslabel = printstatuslist.Where(a => a.id == item.Id.ToString())?.FirstOrDefault()?.text;
                        model.onepdt_logistics_no = printstatuslist.Where(a => a.id == item.Id.ToString())?.FirstOrDefault()?.text2;
                        model.onepdt_product_name = productnamelist.Where(a => a.id == item.Id.ToString())?.FirstOrDefault()?.text;
                        list.Add(model);
                    }
                }
                data.datas = list;

                var ec1 = OrganizationServiceAdmin.RetrieveMultiple(new FetchExpression(string.Format(fetchxml, "", condotion)));
                if (ec1 != null && ec1.Entities.Count > 0)
                {
                    var gbname = ec1.Entities.Where(a => a.Contains("onepdt_name")
                    && ((a.Contains("onepdt_approval_status") && a.GetAttributeValue<OptionSetValue>("onepdt_approval_status").Value != 3) || !a.Contains("onepdt_approval_status"))
                    && !(a.GetAttributeValue<bool>("onepdt_submit_status") == false && a.GetAttributeValue<bool>("onepdt_is_returned") == true && !a.Contains("onepdt_approval_status") && !a.Contains("onepdt_address_approval_status") && a.GetAttributeValue<DateTime>("createdon").ToString("yyyy-MM-dd HH:mm:ss") == a.GetAttributeValue<DateTime>("modifiedon").ToString("yyyy-MM-dd HH:mm:ss"))
                    ).GroupBy(b => b.GetAttributeValue<string>("onepdt_name")).Select(c => new { onepdt_name = c.Key, list = c.ToList() }).ToList();
                    data.operationtotal = gbname.Count();
                }
                return data;
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 上传图片
        /// </summary>
        /// <param name="operationid">跟台id</param>
        /// <param name="photoid">图片id</param>
        /// <param name="classify">图片类型</param>
        /// <param name="file">文件</param>
        /// <returns></returns>
        public async Task<string> UploadFileAsync(string operationid, string photoid, string classify, IFormFile file)
        {
            var failbase64 = string.Empty;
            var fileid = string.Empty;
            try
            {
                if (file == null || file.Length == 0)
                {
                    throw new Exception("No file uploaded.");
                }
                if (string.IsNullOrWhiteSpace(operationid))
                {
                    throw new Exception("跟台数据id不存在");
                }

                if (string.IsNullOrWhiteSpace(photoid))
                {
                    byte[] fileBytes;
                    string fileName = file.FileName;
                    string extension = Path.GetExtension(file.FileName).ToLower();

                    // 转换文件为Base64字符串
                    using (var memoryStream = new MemoryStream())
                    {
                        await file.CopyToAsync(memoryStream);
                        fileBytes = memoryStream.ToArray();
                    }

                    // 如果不是 PNG，则转换为 PNG
                    //if (extension != ".png")
                    //{
                    //    using (var inputStream = new MemoryStream(fileBytes))
                    //    using (var image = new Bitmap(inputStream)) // 读取原始图片
                    //    using (var outputStream = new MemoryStream())
                    //    {
                    //        image.Save(outputStream, ImageFormat.Png); // 转换为 PNG
                    //        fileBytes = outputStream.ToArray();
                    //        fileName = Path.GetFileNameWithoutExtension(file.FileName) + ".png"; // 更改文件名后缀
                    //    }
                    //}
                    //if (extension != ".png")
                    //{
                    //    using (var inputStream = new MemoryStream(fileBytes))
                    //    using (var image = Image.Load(inputStream)) // ImageSharp 加载图片
                    //    using (var outputStream = new MemoryStream())
                    //    {
                    //        image.Mutate(x => x.AutoOrient()); // 确保图片方向正确
                    //        image.Save(outputStream, new PngEncoder()); // 转换为 PNG
                    //        fileBytes = outputStream.ToArray();
                    //        fileName = Path.GetFileNameWithoutExtension(file.FileName) + ".png"; // 更改文件名后缀
                    //    }
                    //}
                    // 转换为 Base64 字符串
                    string base64String = Convert.ToBase64String(fileBytes);
                    failbase64 = base64String;
                    
                    var photo = new Entity("onepdt_t_opreation_photo");
                    photo["onepdt_name"] = fileName;
                    photo["onepdt_classify"] = classify;
                    photo["onepdt_operation"] = new EntityReference("onepdt_t_operation", new Guid(operationid));
                    if (!string.IsNullOrWhiteSpace(photoid))
                    {
                        fileid = photoid;
                        photo.Id = new Guid(photoid);
                        OrganizationServiceAdmin.Update(photo);
                    }
                    else
                    {
                        var newid = Guid.NewGuid();
                        photo.Id = newid;
                        fileid = newid.ToString();
                        OrganizationServiceAdmin.Create(photo);

                        var base64str = string.Empty;
                        if (base64String.IndexOf("base64,") > -1)
                        {
                            var strindex = base64String.IndexOf("base64,");
                            base64str = base64String.Substring(base64String.IndexOf("base64,") + 7);
                        }
                        else
                        {
                            base64str = base64String;
                        }
                        if (!string.IsNullOrWhiteSpace(base64str))
                        {
                            try
                            {
                                CommandHelper.UploadFile(OrganizationServiceAdmin, new EntityReference("onepdt_t_opreation_photo", new Guid(fileid)), "onepdt_photo", file.FileName, Convert.FromBase64String(base64str));
                            }
                            catch (Exception uploadEx)
                            {
                                // **如果文件上传失败，删除已创建的 onepdt_t_opreation_photo**
                                if (RecordExists("onepdt_t_opreation_photo", fileid))
                                {
                                    OrganizationServiceAdmin.Delete("onepdt_t_opreation_photo", new Guid(fileid));
                                }
                                throw new Exception($"文件上传失败: {uploadEx.Message}");
                            }
                        }
                    }
                    return fileid;
                }
                return photoid;
            }
            catch (Exception ex)
            {
                // **确保失败时回滚 `onepdt_t_opreation_photo`**
                // **如果文件上传失败，删除已创建的 onepdt_t_opreation_photo**
                if (RecordExists("onepdt_t_opreation_photo", fileid))
                {
                    OrganizationServiceAdmin.Delete("onepdt_t_opreation_photo", new Guid(fileid));
                }
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 查询是否存在数据
        /// </summary>
        /// <param name="entityName"></param>
        /// <param name="recordId"></param>
        /// <returns></returns>
        public bool RecordExists(string entityName, string recordId)
        {
            QueryExpression query = new QueryExpression(entityName)
            {
                ColumnSet = new ColumnSet(false), // 只查询 ID
                Criteria =
                {
                    Conditions = { new ConditionExpression($"{entityName}id", ConditionOperator.Equal, recordId) }
                }
            };

            EntityCollection result = OrganizationServiceAdmin.RetrieveMultiple(query);
            return result.Entities.Count > 0;
        }


        /// <summary>
        /// 获取用户使用说明
        /// </summary>
        /// <returns></returns>
        public string GetUserManual()
        {
            var base64 = string.Empty;

            var qe = new QueryExpression("onepdt_t_configuration");
            qe.ColumnSet.AllColumns = true;
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("onepdt_value", ConditionOperator.Equal, "WechatUserManual");
            var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
            if (ec != null && ec.Entities.Count > 0)
            {
                var entity = ec.Entities[0];
                var content = CommandHelper.DownloadFile(OrganizationServiceAdmin, entity.ToEntityReference(), "onepdt_file");
                base64 = $"{content}";
            }
            return base64;
        }

        /// <summary>
        /// 获取参数配置文件
        /// </summary>
        /// <returns></returns>
        public string GetConfigFile(string id)
        {
            var base64 = string.Empty;

            var qe = new QueryExpression("onepdt_t_configuration");
            qe.ColumnSet.AllColumns = true;
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("onepdt_t_configurationid", ConditionOperator.Equal, id);
            var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
            if (ec != null && ec.Entities.Count > 0)
            {
                var entity = ec.Entities[0];
                var content = CommandHelper.DownloadFile(OrganizationServiceAdmin, entity.ToEntityReference(), "onepdt_file");
                base64 = $"{content}";
            }
            return base64;
        }

        /// <summary>
        /// 跟台数据校验
        /// </summary>
        /// <param name="model"></param>
        public string CkeckOperation(OperationEditData model)
        {
            try
            {
                var tip = new StringBuilder();
                var errors = new StringBuilder();

                #region 必填校验
                if (string.IsNullOrWhiteSpace(model.patientName))
                {
                    errors.AppendLine("请输入患者姓名");
                }
                if (string.IsNullOrWhiteSpace(model.patientGender))
                {
                    errors.AppendLine("请选择患者性别");
                }
                if (string.IsNullOrWhiteSpace(model.implantHospitalId))
                {
                    errors.AppendLine("请选择植入医院");
                }
                if (string.IsNullOrWhiteSpace(model.implantDate))
                {
                    errors.AppendLine("请选择植入日期");
                }
                if (string.IsNullOrWhiteSpace(model.firstSurgeonid))
                {
                    errors.AppendLine("请选择第一术者");
                }
                if (string.IsNullOrWhiteSpace(model.implantTypeid))
                {
                    errors.AppendLine("请选择植入类型");
                }
                if (string.IsNullOrWhiteSpace(model.surgicalAssistantType))
                {
                    errors.AppendLine("跟台者不能为空");
                }
                if (string.IsNullOrWhiteSpace(model.hisPurkinjeSystemPacingid))
                {
                    errors.AppendLine("请选择是否尝试心室生理性起搏");
                }
                if (string.IsNullOrWhiteSpace(model.xfsystemId))
                {
                    errors.AppendLine("请选择是否尝试心房生理性起搏");
                }
                if (string.IsNullOrWhiteSpace(model.surgicalAssistant) && !string.IsNullOrWhiteSpace(model.surgicalAssistantType) && model.surgicalAssistantType != "无需跟台")
                {
                    errors.AppendLine("请输入跟台人姓名");
                }
                if (model.details.Count == 0)
                {
                    errors.AppendLine("请选择植入产品");
                }
                if (model.implantType == "Aveir DR部分更换VR/AR" || model.implantType == "Aveir VR/AR升级为DR")
                {
                    var zj = model.details.Where(a => a.bigType == "主机").ToList();
                    if (zj.Count != 1) errors.AppendLine("该植入类型只可有一台主机产品");
                    if (zj.Count == 1 && zj[0].category != "Leadless") errors.AppendLine("主机的产品大类需是Leadless");
                }
                #endregion

                if (!string.IsNullOrWhiteSpace(errors.ToString()))
                {
                    throw new Exception(errors.ToString());
                }

                var JsonBody = model.details.Select(a => new VerificationReq() { MainId = model.id, Id = a.id, SN = a.serialNumber, Model = a.model, ImplantDate = model.implantDate, Nonmainland = a.isNonMainlandProduct ? "Y" : "N" }).ToList();
                var response = CommandHelper.InvokeAction(OrganizationServiceAdmin, "onepdt_Implant_verification", new Dictionary<string, object> { { "JsonBody", JsonConvert.SerializeObject(JsonBody) } });
                if (response != null && response.Results != null && response.Results.ContainsKey("Result"))
                {
                    var Results = (string)response.Results["Result"];
                    var res = JsonConvert.DeserializeObject<List<VerificationResult>>(Results);
                    res.ForEach(r =>
                    {
                        if (r.ResultType == "E") errors.AppendLine($"{r.model}:{r.Message}");
                        if (r.IsExpired) tip.AppendLine($"植入产品【{r.model}】不在有效期");
                    });
                }

                if (!string.IsNullOrWhiteSpace(errors.ToString()))
                {
                    throw new Exception(errors.ToString());
                }

                return tip.ToString();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 查询历史跟台
        /// </summary>
        /// <returns></returns>
        public List<OperationModel> GetHistoryOperation()
        {
            var list = new List<OperationModel>();

            var fetchxml = @"<fetch version='1.0' mapping='logical' no-lock='false' distinct='true'>
                              <entity name='onepdt_t_operation'>
                                <attribute name='onepdt_t_operationid'/>
                                <attribute name='onepdt_name'/>
                                <attribute name='onepdt_hospitalcode'/>
                                <attribute name='onepdt_hospitalname'/>
                                <attribute name='onepdt_patient'/>
                                <attribute name='onepdt_gender'/>
                                <attribute name='onepdt_date'/>
                                <order attribute='onepdt_date' descending='true'/>
                                <link-entity name='onepdt_t_operation_implant' alias='aa' link-type='inner' from='onepdt_operation' to='onepdt_t_operationid'>
                                  <link-entity name='onepdt_t_product_mdata' alias='ab' link-type='inner' from='onepdt_t_product_mdataid' to='onepdt_product_mdata'>
                                    <filter type='and'>
                                      <condition attribute='onepdt_family' operator='eq' value='Aveir'/>
                                      <condition attribute='onepdt_type_idname' operator='eq' value='主机'/>
                                    </filter>
                                  </link-entity>
                                </link-entity>
                                <filter type='and'>
                                  <condition attribute='statecode' operator='eq' value='0'/>
                                  <condition attribute='onepdt_approval_status' operator='eq' value='1'/>
                                </filter>
                              </entity>
                            </fetch>";
            var ec = OrganizationServiceAdmin.RetrieveMultiple(new FetchExpression(fetchxml));
            if (ec != null && ec.Entities.Count > 0)
            {
                var ids = ec.Entities.Select(a => a.Id.ToString()).ToList();
                //产品型号名称
                var productnamelist = GetProductname(ids);

                foreach (var item in ec.Entities)
                {
                    var model = new OperationModel();
                    model.id = item.Id.ToString();
                    model.onepdt_hospitalname = item.GetAttributeValue<string>("onepdt_hospitalname");
                    model.onepdt_hospitalcode = item.GetAttributeValue<string>("onepdt_hospitalcode");
                    model.onepdt_patient = item.GetAttributeValue<string>("onepdt_patient");
                    model.onepdt_gender = item.GetAttributeValue<string>("onepdt_gender");
                    if (item.Contains("onepdt_date"))
                    {
                        model.onepdt_date = item.GetAttributeValue<DateTime>("onepdt_date").AddHours(8).ToString("yyyy/MM/dd");
                    }
                    model.onepdt_product_name = productnamelist.Where(a => a.id == item.Id.ToString())?.FirstOrDefault()?.text;
                    model.onepdt_product_sn = productnamelist.Where(a => a.id == item.Id.ToString())?.FirstOrDefault()?.text1;
                    list.Add(model);
                }
            }

            return list;
        }
    }
}
