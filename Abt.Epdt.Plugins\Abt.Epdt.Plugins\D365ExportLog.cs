﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abt.Epdt.Plugins
{
    /// <summary>
    /// Abt.Epdt.Plugins.D365ExportLog: RetrieveMultiple of epdt_t_device_application - PreOperation
    /// </summary>
    public class D365ExportLog : IPlugin
    {
        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService service = serviceFactory.CreateOrganizationService(context.UserId); // 权限

            //IOrganizationService service = serviceFactory.CreateOrganizationService(null); // 权限

            bool isexportexcel = false;
            if (context.ParentContext != null)
            {
                isexportexcel = context.ParentContext.MessageName == "ExportToExcel";
            }

            if (isexportexcel)
            {
                var trackingquery = new Entity("epdt_t_export_data_log");
                if(context.InputParameters.Contains("Query"))
                {
                    QueryExpression querymain = new QueryExpression();
                    if(context.InputParameters["Query"] is QueryExpression)
                    {
                        //查看当前此人生效的导出
                        string fetchxml = $@"<fetch xmlns:generator='MarkMpn.SQL4CDS' >
  <entity name='epdt_t_special_business_scenario' >
    <attribute name='epdt_t_special_business_scenarioid' />
    <attribute name='epdt_name' />
    <filter>
      <condition attribute='epdt_export_start_time' operator='le' value='{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}' />
      <condition attribute='epdt_export_end_time' operator='ge' value='{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}' />
      <condition attribute='epdt_export_person' operator='eq' value='{context.UserId}' />
    </filter>
  </entity>
</fetch>";
                        FetchExpression sql = new FetchExpression(fetchxml);
                        var list = service.RetrieveMultiple(sql);

                        foreach (var first in list.Entities)
                        {
                            QueryExpression query = (QueryExpression)context.InputParameters["Query"];
                            string cols = string.Join(",", query.ColumnSet.Columns);
                            trackingquery["epdt_name"] = "特殊场景导出 - " + first.GetAttributeValue<string>("epdt_name");
                            trackingquery["epdt_linkedbusinesssenario"] = new EntityReference("epdt_t_special_business_scenario", first.Id);
                            service.Create(trackingquery);
                        }

                        //var first = list.Entities.FirstOrDefault();
                        //if (first != null)
                        //{
                            
                        //}
                    }
                }
            }
        }
    }
}
