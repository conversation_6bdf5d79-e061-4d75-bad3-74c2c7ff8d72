﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Text.Encodings.Web</name>
  </assembly>
  <members>
    <member name="T:System.Text.Encodings.Web.HtmlEncoder">
      <summary>Represents an HTML character encoding.</summary>
    </member>
    <member name="M:System.Text.Encodings.Web.HtmlEncoder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Encodings.Web.HtmlEncoder" /> class.</summary>
    </member>
    <member name="M:System.Text.Encodings.Web.HtmlEncoder.Create(System.Text.Encodings.Web.TextEncoderSettings)">
      <summary>Creates a new instance of the HtmlEncoder class with the specified settings.</summary>
      <param name="settings">Settings that control how the <see cref="T:System.Text.Encodings.Web.HtmlEncoder" /> instance encodes, primarily which characters to encode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="settings" /> is <see langword="null" />.</exception>
      <returns>A new instance of the <see cref="T:System.Text.Encodings.Web.HtmlEncoder" /> class.</returns>
    </member>
    <member name="M:System.Text.Encodings.Web.HtmlEncoder.Create(System.Text.Unicode.UnicodeRange[])">
      <summary>Creates a new instance of the HtmlEncoder class that specifies characters the encoder is allowed to not encode.</summary>
      <param name="allowedRanges">The set of characters that the encoder is allowed to not encode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="allowedRanges" /> is <see langword="null" />.</exception>
      <returns>A new instance of the <see cref="T:System.Text.Encodings.Web.HtmlEncoder" /> class.</returns>
    </member>
    <member name="P:System.Text.Encodings.Web.HtmlEncoder.Default">
      <summary>Gets a built-in instance of the <see cref="T:System.Text.Encodings.Web.HtmlEncoder" /> class.</summary>
      <returns>A built-in instance of the <see cref="T:System.Text.Encodings.Web.HtmlEncoder" /> class.</returns>
    </member>
    <member name="T:System.Text.Encodings.Web.JavaScriptEncoder">
      <summary>Represents a JavaScript character encoding.</summary>
    </member>
    <member name="M:System.Text.Encodings.Web.JavaScriptEncoder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Encodings.Web.JavaScriptEncoder" /> class.</summary>
    </member>
    <member name="M:System.Text.Encodings.Web.JavaScriptEncoder.Create(System.Text.Encodings.Web.TextEncoderSettings)">
      <summary>Creates a new instance of JavaScriptEncoder class with the specified settings.</summary>
      <param name="settings">Settings that control how the <see cref="T:System.Text.Encodings.Web.JavaScriptEncoder" /> instance encodes, primarily which characters to encode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="settings" /> is <see langword="null" />.</exception>
      <returns>A new instance of the <see cref="T:System.Text.Encodings.Web.JavaScriptEncoder" /> class.</returns>
    </member>
    <member name="M:System.Text.Encodings.Web.JavaScriptEncoder.Create(System.Text.Unicode.UnicodeRange[])">
      <summary>Creates a new instance of the JavaScriptEncoder class that specifies characters the encoder is allowed to not encode.</summary>
      <param name="allowedRanges">The set of characters that the encoder is allowed to not encode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="allowedRanges" /> is <see langword="null" />.</exception>
      <returns>A new instance of the <see cref="T:System.Text.Encodings.Web.JavaScriptEncoder" /> class.</returns>
    </member>
    <member name="P:System.Text.Encodings.Web.JavaScriptEncoder.Default">
      <summary>Gets a built-in instance of the <see cref="T:System.Text.Encodings.Web.JavaScriptEncoder" /> class.</summary>
      <returns>A built-in instance of the <see cref="T:System.Text.Encodings.Web.JavaScriptEncoder" /> class.</returns>
    </member>
    <member name="P:System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping">
      <summary>Gets a built-in JavaScript encoder instance that is less strict about what is encoded.</summary>
      <returns>A JavaScript encoder instance.</returns>
    </member>
    <member name="T:System.Text.Encodings.Web.TextEncoder">
      <summary>The base class of web encoders.</summary>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Encodings.Web.TextEncoder" /> class.</summary>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoder.Encode(System.IO.TextWriter,System.Char[],System.Int32,System.Int32)">
      <summary>Encodes characters from an array and writes them to a <see cref="T:System.IO.TextWriter" /> object.</summary>
      <param name="output">The stream to which to write the encoded text.</param>
      <param name="value">The array of characters to encode.</param>
      <param name="startIndex">The array index of the first character to encode.</param>
      <param name="characterCount">The number of characters in the array to encode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="output" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The <see cref="M:System.Text.Encodings.Web.TextEncoder.TryEncodeUnicodeScalar(System.Int32,System.Char*,System.Int32,System.Int32@)" /> method failed. The encoder does not implement <see cref="P:System.Text.Encodings.Web.TextEncoder.MaxOutputCharactersPerInputCharacter" /> correctly.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is out of range.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="characterCount" /> is out of range.</exception>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoder.Encode(System.IO.TextWriter,System.String)">
      <summary>Encodes the specified string to a <see cref="T:System.IO.TextWriter" /> object.</summary>
      <param name="output">The stream to which to write the encoded text.</param>
      <param name="value">The string to encode.</param>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoder.Encode(System.IO.TextWriter,System.String,System.Int32,System.Int32)">
      <summary>Encodes a substring and writes it to a <see cref="T:System.IO.TextWriter" /> object.</summary>
      <param name="output">The stream to which to write the encoded text.</param>
      <param name="value">The string whose substring is to be encoded.</param>
      <param name="startIndex">The index where the substring starts.</param>
      <param name="characterCount">The number of characters in the substring.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="output" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The <see cref="M:System.Text.Encodings.Web.TextEncoder.TryEncodeUnicodeScalar(System.Int32,System.Char*,System.Int32,System.Int32@)" /> method failed. The encoder does not implement <see cref="P:System.Text.Encodings.Web.TextEncoder.MaxOutputCharactersPerInputCharacter" /> correctly.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> is out of range.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="characterCount" /> is out of range.</exception>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoder.Encode(System.ReadOnlySpan{System.Char},System.Span{System.Char},System.Int32@,System.Int32@,System.Boolean)">
      <summary>Encodes the supplied characters.</summary>
      <param name="source">A source buffer containing the characters to encode.</param>
      <param name="destination">The destination buffer to which the encoded form of <paramref name="source" /> will be written.</param>
      <param name="charsConsumed">The number of characters consumed from the <paramref name="source" /> buffer.</param>
      <param name="charsWritten">The number of characters written to the <paramref name="destination" /> buffer.</param>
      <param name="isFinalBlock">
        <see langword="true" /> to indicate there is no further source data that needs to be encoded; otherwise, <see langword="false" />.</param>
      <returns>An enumeration value that describes the result of the encoding operation.</returns>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoder.Encode(System.String)">
      <summary>Encodes the supplied string and returns the encoded text as a new string.</summary>
      <param name="value">The string to encode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The <see cref="M:System.Text.Encodings.Web.TextEncoder.TryEncodeUnicodeScalar(System.Int32,System.Char*,System.Int32,System.Int32@)" /> method failed. The encoder does not implement <see cref="P:System.Text.Encodings.Web.TextEncoder.MaxOutputCharactersPerInputCharacter" /> correctly.</exception>
      <returns>The encoded string.</returns>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoder.EncodeUtf8(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@,System.Int32@,System.Boolean)">
      <summary>Encodes the supplied UTF-8 text.</summary>
      <param name="utf8Source">A source buffer containing the UTF-8 text to encode.</param>
      <param name="utf8Destination">The destination buffer to which the encoded form of <paramref name="utf8Source" /> will be written.</param>
      <param name="bytesConsumed">The number of bytes consumed from the <paramref name="utf8Source" /> buffer.</param>
      <param name="bytesWritten">The number of bytes written to the <paramref name="utf8Destination" /> buffer.</param>
      <param name="isFinalBlock">
        <see langword="true" /> to indicate there is no further source data that needs to be encoded; otherwise, <see langword="false" />.</param>
      <returns>A status code that describes the result of the encoding operation.</returns>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoder.FindFirstCharacterToEncode(System.Char*,System.Int32)">
      <summary>Finds the index of the first character to encode.</summary>
      <param name="text">The text buffer to search.</param>
      <param name="textLength">The number of characters in <paramref name="text" />.</param>
      <returns>The index of the first character to encode.</returns>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoder.FindFirstCharacterToEncodeUtf8(System.ReadOnlySpan{System.Byte})">
      <summary>Finds the first element in a UTF-8 text input buffer that would be escaped by the current encoder instance.</summary>
      <param name="utf8Text">The UTF-8 text input buffer to search.</param>
      <returns>The index of the first element in <paramref name="utf8Text" /> that would be escaped by the current encoder instance, or -1 if no data in <paramref name="utf8Text" /> requires escaping.</returns>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoder.TryEncodeUnicodeScalar(System.Int32,System.Char*,System.Int32,System.Int32@)">
      <summary>Encodes a Unicode scalar value and writes it to a buffer.</summary>
      <param name="unicodeScalar">A Unicode scalar value.</param>
      <param name="buffer">A pointer to the buffer to which to write the encoded text.</param>
      <param name="bufferLength">The length of the destination <paramref name="buffer" /> in characters.</param>
      <param name="numberOfCharactersWritten">When the method returns, indicates the number of characters written to the <paramref name="buffer" />.</param>
      <returns>
        <see langword="false" /> if <paramref name="bufferLength" /> is too small to fit the encoded text; otherwise, returns <see langword="true" />.</returns>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoder.WillEncode(System.Int32)">
      <summary>Determines if a given Unicode scalar value will be encoded.</summary>
      <param name="unicodeScalar">A Unicode scalar value.</param>
      <returns>
        <see langword="true" /> if the <paramref name="unicodeScalar" /> value will be encoded by this encoder; otherwise, returns <see langword="false" />.</returns>
    </member>
    <member name="P:System.Text.Encodings.Web.TextEncoder.MaxOutputCharactersPerInputCharacter">
      <summary>Gets the maximum number of characters that this encoder can generate for each input code point.</summary>
      <returns>The maximum number of characters.</returns>
    </member>
    <member name="T:System.Text.Encodings.Web.TextEncoderSettings">
      <summary>Represents a filter that allows only certain Unicode code points.</summary>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoderSettings.#ctor">
      <summary>Instantiates an empty filter (allows no code points through by default).</summary>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoderSettings.#ctor(System.Text.Encodings.Web.TextEncoderSettings)">
      <summary>Instantiates a filter by cloning the allowed list of another <see cref="T:System.Text.Encodings.Web.TextEncoderSettings" /> object.</summary>
      <param name="other">The other <see cref="T:System.Text.Encodings.Web.TextEncoderSettings" /> object to be cloned.</param>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoderSettings.#ctor(System.Text.Unicode.UnicodeRange[])">
      <summary>Instantiates a filter where only the character ranges specified by <paramref name="allowedRanges" /> are allowed by the filter.</summary>
      <param name="allowedRanges">The allowed character ranges.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="allowedRanges" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoderSettings.AllowCharacter(System.Char)">
      <summary>Allows the character specified by <paramref name="character" /> through the filter.</summary>
      <param name="character">The allowed character.</param>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoderSettings.AllowCharacters(System.Char[])">
      <summary>Allows all characters specified by <paramref name="characters" /> through the filter.</summary>
      <param name="characters">The allowed characters.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="characters" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoderSettings.AllowCodePoints(System.Collections.Generic.IEnumerable{System.Int32})">
      <summary>Allows all code points specified by <paramref name="codePoints" />.</summary>
      <param name="codePoints">The allowed code points.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="codePoints" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoderSettings.AllowRange(System.Text.Unicode.UnicodeRange)">
      <summary>Allows all characters specified by <paramref name="range" /> through the filter.</summary>
      <param name="range">The range of characters to be allowed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="range" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoderSettings.AllowRanges(System.Text.Unicode.UnicodeRange[])">
      <summary>Allows all characters specified by <paramref name="ranges" /> through the filter.</summary>
      <param name="ranges">The ranges of characters to be allowed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ranges" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoderSettings.Clear">
      <summary>Resets this object by disallowing all characters.</summary>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoderSettings.ForbidCharacter(System.Char)">
      <summary>Disallows the character <paramref name="character" /> through the filter.</summary>
      <param name="character">The disallowed character.</param>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoderSettings.ForbidCharacters(System.Char[])">
      <summary>Disallows all characters specified by <paramref name="characters" /> through the filter.</summary>
      <param name="characters">The disallowed characters.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="characters" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoderSettings.ForbidRange(System.Text.Unicode.UnicodeRange)">
      <summary>Disallows all characters specified by <paramref name="range" /> through the filter.</summary>
      <param name="range">The range of characters to be disallowed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="range" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoderSettings.ForbidRanges(System.Text.Unicode.UnicodeRange[])">
      <summary>Disallows all characters specified by <paramref name="ranges" /> through the filter.</summary>
      <param name="ranges">The ranges of characters to be disallowed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ranges" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Text.Encodings.Web.TextEncoderSettings.GetAllowedCodePoints">
      <summary>Gets an enumerator of all allowed code points.</summary>
      <returns>The enumerator of allowed code points.</returns>
    </member>
    <member name="T:System.Text.Encodings.Web.UrlEncoder">
      <summary>Represents a URL character encoding.</summary>
    </member>
    <member name="M:System.Text.Encodings.Web.UrlEncoder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Text.Encodings.Web.UrlEncoder" /> class.</summary>
    </member>
    <member name="M:System.Text.Encodings.Web.UrlEncoder.Create(System.Text.Encodings.Web.TextEncoderSettings)">
      <summary>Creates a new instance of UrlEncoder class with the specified settings.</summary>
      <param name="settings">Settings that control how the <see cref="T:System.Text.Encodings.Web.UrlEncoder" /> instance encodes, primarily which characters to encode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="settings" /> is <see langword="null" />.</exception>
      <returns>A new instance of the <see cref="T:System.Text.Encodings.Web.UrlEncoder" /> class.</returns>
    </member>
    <member name="M:System.Text.Encodings.Web.UrlEncoder.Create(System.Text.Unicode.UnicodeRange[])">
      <summary>Creates a new instance of the UrlEncoder class that specifies characters the encoder is allowed to not encode.</summary>
      <param name="allowedRanges">The set of characters that the encoder is allowed to not encode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="allowedRanges" /> is <see langword="null" />.</exception>
      <returns>A new instance of the <see cref="T:System.Text.Encodings.Web.UrlEncoder" /> class.</returns>
    </member>
    <member name="P:System.Text.Encodings.Web.UrlEncoder.Default">
      <summary>Gets a built-in instance of the <see cref="T:System.Text.Encodings.Web.UrlEncoder" /> class.</summary>
      <returns>A built-in instance of the <see cref="T:System.Text.Encodings.Web.UrlEncoder" /> class.</returns>
    </member>
    <member name="T:System.Text.Unicode.UnicodeRange">
      <summary>Represents a contiguous range of Unicode code points.</summary>
    </member>
    <member name="M:System.Text.Unicode.UnicodeRange.#ctor(System.Int32,System.Int32)">
      <summary>Creates a new <see cref="T:System.Text.Unicode.UnicodeRange" /> that includes a specified number of characters starting at a specified Unicode code point.</summary>
      <param name="firstCodePoint">The first code point in the range.</param>
      <param name="length">The number of code points in the range.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="firstCodePoint" /> is less than zero or greater than 0xFFFF. 

-or-           

<paramref name="length" /> is less than zero.

-or-

<paramref name="firstCodePoint" /> plus <paramref name="length" /> is greater than 0xFFFF.</exception>
    </member>
    <member name="M:System.Text.Unicode.UnicodeRange.Create(System.Char,System.Char)">
      <summary>Creates a new <see cref="T:System.Text.Unicode.UnicodeRange" /> instance from a span of characters.</summary>
      <param name="firstCharacter">The first character in the range.</param>
      <param name="lastCharacter">The last character in the range.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastCharacter" /> precedes <paramref name="firstCharacter" />.</exception>
      <returns>A range that includes all characters between <paramref name="firstCharacter" /> and <paramref name="lastCharacter" />.</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRange.FirstCodePoint">
      <summary>Gets the first code point in the range represented by this <see cref="T:System.Text.Unicode.UnicodeRange" /> instance.</summary>
      <returns>The first code point in the range.</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRange.Length">
      <summary>Gets the number of code points in the range represented by this <see cref="T:System.Text.Unicode.UnicodeRange" /> instance.</summary>
      <returns>The number of code points in the range.</returns>
    </member>
    <member name="T:System.Text.Unicode.UnicodeRanges">
      <summary>Provides static properties that return predefined <see cref="T:System.Text.Unicode.UnicodeRange" /> instances that correspond to blocks from the Unicode specification.</summary>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.All">
      <summary>Gets a range that consists of the entire Basic Multilingual Plane (BMP), from U+0000 to U+FFFF).</summary>
      <returns>A range that consists of the entire BMP.</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.AlphabeticPresentationForms">
      <summary>Gets the Alphabetic Presentation Forms Unicode block (U+FB00-U+FB4F).</summary>
      <returns>The Alphabetic Presentation Forms Unicode block (U+FB00-U+FB4F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Arabic">
      <summary>Gets the Arabic Unicode block (U+0600-U+06FF).</summary>
      <returns>The Arabic Unicode block (U+0600-U+06FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.ArabicExtendedA">
      <summary>Gets the Arabic Extended-A Unicode block (U+08A0-U+08FF).</summary>
      <returns>The Arabic Extended-A Unicode block (U+08A0-U+08FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.ArabicPresentationFormsA">
      <summary>Gets the Arabic Presentation Forms-A Unicode block (U+FB50-U+FDFF).</summary>
      <returns>The Arabic Presentation Forms-A Unicode block (U+FB50-U+FDFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.ArabicPresentationFormsB">
      <summary>Gets the Arabic Presentation Forms-B Unicode block (U+FE70-U+FEFF).</summary>
      <returns>The Arabic Presentation Forms-B Unicode block (U+FE70-U+FEFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.ArabicSupplement">
      <summary>Gets the Arabic Supplement Unicode block (U+0750-U+077F).</summary>
      <returns>The Arabic Supplement Unicode block (U+0750-U+077F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Armenian">
      <summary>Gets the Armenian Unicode block (U+0530-U+058F).</summary>
      <returns>The Armenian Unicode block (U+0530-U+058F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Arrows">
      <summary>Gets the Arrows Unicode block (U+2190-U+21FF).</summary>
      <returns>The Arrows Unicode block (U+2190-U+21FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Balinese">
      <summary>Gets the Balinese Unicode block (U+1B00-U+1B7F).</summary>
      <returns>The Balinese Unicode block (U+1B00-U+1B7F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Bamum">
      <summary>Gets the Bamum Unicode block (U+A6A0-U+A6FF).</summary>
      <returns>The Bamum Unicode block (U+A6A0-U+A6FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.BasicLatin">
      <summary>Gets the Basic Latin Unicode block (U+0021-U+007F).</summary>
      <returns>The Basic Latin Unicode block (U+0021-U+007F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Batak">
      <summary>Gets the Batak Unicode block (U+1BC0-U+1BFF).</summary>
      <returns>The Batak Unicode block (U+1BC0-U+1BFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Bengali">
      <summary>Gets the Bengali Unicode block (U+0980-U+09FF).</summary>
      <returns>The Bengali Unicode block (U+0980-U+09FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.BlockElements">
      <summary>Gets the Block Elements Unicode block (U+2580-U+259F).</summary>
      <returns>The Block Elements Unicode block (U+2580-U+259F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Bopomofo">
      <summary>Gets the Bopomofo Unicode block (U+3100-U+312F).</summary>
      <returns>The Bopomofo Unicode block (U+3105-U+312F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.BopomofoExtended">
      <summary>Gets the Bopomofo Extended Unicode block (U+31A0-U+31BF).</summary>
      <returns>The Bopomofo Extended Unicode block (U+31A0-U+31BF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.BoxDrawing">
      <summary>Gets the Box Drawing Unicode block (U+2500-U+257F).</summary>
      <returns>The Box Drawing Unicode block (U+2500-U+257F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.BraillePatterns">
      <summary>Gets the Braille Patterns Unicode block (U+2800-U+28FF).</summary>
      <returns>The Braille Patterns Unicode block (U+2800-U+28FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Buginese">
      <summary>Gets the Buginese Unicode block (U+1A00-U+1A1F).</summary>
      <returns>The Buginese Unicode block (U+1A00-U+1A1F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Buhid">
      <summary>Gets the Buhid Unicode block (U+1740-U+175F).</summary>
      <returns>The Buhid Unicode block (U+1740-U+175F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Cham">
      <summary>Gets the Cham Unicode block (U+AA00-U+AA5F).</summary>
      <returns>The Cham Unicode block (U+AA00-U+AA5F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Cherokee">
      <summary>Gets the Cherokee Unicode block (U+13A0-U+13FF).</summary>
      <returns>The Cherokee Unicode block (U+13A0-U+13FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CherokeeSupplement">
      <summary>Gets the Cherokee Supplement Unicode block (U+AB70-U+ABBF).</summary>
      <returns>The Cherokee Supplement Unicode block (U+AB70-U+ABBF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CjkCompatibility">
      <summary>Gets the CJK Compatibility Unicode block (U+3300-U+33FF).</summary>
      <returns>The CJK Compatibility Unicode block (U+3300-U+33FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CjkCompatibilityForms">
      <summary>Gets the CJK Compatibility Forms Unicode block (U+FE30-U+FE4F).</summary>
      <returns>The CJK Compatibility Forms Unicode block (U+FE30-U+FE4F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CjkCompatibilityIdeographs">
      <summary>Gets the CJK Compatibility Ideographs Unicode block (U+F900-U+FAD9).</summary>
      <returns>The CJK Compatibility Ideographs Unicode block (U+F900-U+FAD9).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CjkRadicalsSupplement">
      <summary>Gets the CJK Radicals Supplement Unicode block (U+2E80-U+2EFF).</summary>
      <returns>The CJK Radicals Supplement Unicode block (U+2E80-U+2EFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CjkStrokes">
      <summary>Gets the CJK Strokes Unicode block (U+31C0-U+31EF).</summary>
      <returns>The CJK Strokes Unicode block (U+31C0-U+31EF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CjkSymbolsandPunctuation">
      <summary>Gets the CJK Symbols and Punctuation Unicode block (U+3000-U+303F).</summary>
      <returns>The CJK Symbols and Punctuation Unicode block (U+3000-U+303F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CjkUnifiedIdeographs">
      <summary>Gets the CJK Unified Ideographs Unicode block (U+4E00-U+9FCC).</summary>
      <returns>The CJK Unified Ideographs Unicode block (U+4E00-U+9FCC).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CjkUnifiedIdeographsExtensionA">
      <summary>Gets the CJK Unitied Ideographs Extension A Unicode block (U+3400-U+4DB5).</summary>
      <returns>The CJK Unitied Ideographs Extension A Unicode block (U+3400-U+4DB5).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CombiningDiacriticalMarks">
      <summary>Gets the Combining Diacritical Marks Unicode block (U+0300-U+036F).</summary>
      <returns>The Combining Diacritical Marks Unicode block (U+0300-U+036F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CombiningDiacriticalMarksExtended">
      <summary>Gets the Combining Diacritical Marks Extended Unicode block (U+1AB0-U+1AFF).</summary>
      <returns>The Combining Diacritical Marks Extended Unicode block (U+1AB0-U+1AFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CombiningDiacriticalMarksforSymbols">
      <summary>Gets the Combining Diacritical Marks for Symbols Unicode block (U+20D0-U+20FF).</summary>
      <returns>The Combining Diacritical Marks for Symbols Unicode block (U+20D0-U+20FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CombiningDiacriticalMarksSupplement">
      <summary>Gets the Combining Diacritical Marks Supplement Unicode block (U+1DC0-U+1DFF).</summary>
      <returns>The Combining Diacritical Marks Supplement Unicode block (U+1DC0-U+1DFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CombiningHalfMarks">
      <summary>Gets the Combining Half Marks Unicode block (U+FE20-U+FE2F).</summary>
      <returns>The Combining Half Marks Unicode block (U+FE20-U+FE2F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CommonIndicNumberForms">
      <summary>Gets the Common Indic Number Forms Unicode block (U+A830-U+A83F).</summary>
      <returns>The Common Indic Number Forms Unicode block (U+A830-U+A83F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.ControlPictures">
      <summary>Gets the Control Pictures Unicode block (U+2400-U+243F).</summary>
      <returns>The Control Pictures Unicode block (U+2400-U+243F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Coptic">
      <summary>Gets the Coptic Unicode block (U+2C80-U+2CFF).</summary>
      <returns>The Coptic Unicode block (U+2C80-U+2CFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CurrencySymbols">
      <summary>Gets the Currency Symbols Unicode block (U+20A0-U+20CF).</summary>
      <returns>The Currency Symbols Unicode block (U+20A0-U+20CF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Cyrillic">
      <summary>Gets the Cyrillic Unicode block (U+0400-U+04FF).</summary>
      <returns>The Cyrillic Unicode block (U+0400-U+04FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CyrillicExtendedA">
      <summary>Gets the Cyrillic Extended-A Unicode block (U+2DE0-U+2DFF).</summary>
      <returns>The Cyrillic Extended-A Unicode block (U+2DE0-U+2DFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CyrillicExtendedB">
      <summary>Gets the Cyrillic Extended-B Unicode block (U+A640-U+A69F).</summary>
      <returns>The Cyrillic Extended-B Unicode block (U+A640-U+A69F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CyrillicExtendedC">
      <summary>A <see cref="T:System.Text.Unicode.UnicodeRange" /> corresponding to the 'Cyrillic Extended-C' Unicode block (U+1C80..U+1C8F).</summary>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.CyrillicSupplement">
      <summary>Gets the Cyrillic Supplement Unicode block (U+0500-U+052F).</summary>
      <returns>The Cyrillic Supplement Unicode block (U+0500-U+052F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Devanagari">
      <summary>Gets the Devangari Unicode block (U+0900-U+097F).</summary>
      <returns>The Devangari Unicode block (U+0900-U+097F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.DevanagariExtended">
      <summary>Gets the Devanagari Extended Unicode block (U+A8E0-U+A8FF).</summary>
      <returns>The Devanagari Extended Unicode block (U+A8E0-U+A8FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Dingbats">
      <summary>Gets the Dingbats Unicode block (U+2700-U+27BF).</summary>
      <returns>The Dingbats Unicode block (U+2700-U+27BF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.EnclosedAlphanumerics">
      <summary>Gets the Enclosed Alphanumerics Unicode block (U+2460-U+24FF).</summary>
      <returns>The Enclosed Alphanumerics Unicode block (U+2460-U+24FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.EnclosedCjkLettersandMonths">
      <summary>Gets the Enclosed CJK Letters and Months Unicode block (U+3200-U+32FF).</summary>
      <returns>The Enclosed CJK Letters and Months Unicode block (U+3200-U+32FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Ethiopic">
      <summary>Gets the Ethiopic Unicode block (U+1200-U+137C).</summary>
      <returns>The Ethiopic Unicode block (U+1200-U+137C).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.EthiopicExtended">
      <summary>Gets the Ethipic Extended Unicode block (U+2D80-U+2DDF).</summary>
      <returns>The Ethipic Extended Unicode block (U+2D80-U+2DDF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.EthiopicExtendedA">
      <summary>Gets the Ethiopic Extended-A Unicode block (U+AB00-U+AB2F).</summary>
      <returns>The Ethiopic Extended-A Unicode block (U+AB00-U+AB2F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.EthiopicSupplement">
      <summary>Gets the Ethiopic Supplement Unicode block (U+1380-U+1399).</summary>
      <returns>The Ethiopic Supplement Unicode block (U+1380-U+1399).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.GeneralPunctuation">
      <summary>Gets the General Punctuation Unicode block (U+2000-U+206F).</summary>
      <returns>The General Punctuation Unicode block (U+2000-U+206F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.GeometricShapes">
      <summary>Gets the Geometric Shapes Unicode block (U+25A0-U+25FF).</summary>
      <returns>The Geometric Shapes Unicode block (U+25A0-U+25FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Georgian">
      <summary>Gets the Georgian Unicode block (U+10A0-U+10FF).</summary>
      <returns>The Georgian Unicode block (U+10A0-U+10FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.GeorgianExtended">
      <summary>A <see cref="T:System.Text.Unicode.UnicodeRange" /> corresponding to the 'Georgian Extended' Unicode block (U+1C90..U+1CBF).</summary>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.GeorgianSupplement">
      <summary>Gets the Georgian Supplement Unicode block (U+2D00-U+2D2F).</summary>
      <returns>The Georgian Supplement Unicode block (U+2D00-U+2D2F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Glagolitic">
      <summary>Gets the Glagolitic Unicode block (U+2C00-U+2C5F).</summary>
      <returns>The Glagolitic Unicode block (U+2C00-U+2C5F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.GreekandCoptic">
      <summary>Gets the Greek and Coptic Unicode block (U+0370-U+03FF).</summary>
      <returns>The Greek and Coptic Unicode block (U+0370-U+03FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.GreekExtended">
      <summary>Gets the Greek Extended Unicode block (U+1F00-U+1FFF).</summary>
      <returns>The Greek Extended Unicode block (U+1F00-U+1FFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Gujarati">
      <summary>Gets the Gujarti Unicode block (U+0A81-U+0AFF).</summary>
      <returns>The Gujarti Unicode block (U+0A81-U+0AFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Gurmukhi">
      <summary>Gets the Gurmukhi Unicode block (U+0A01-U+0A7F).</summary>
      <returns>The Gurmukhi Unicode block (U+0A01-U+0A7F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.HalfwidthandFullwidthForms">
      <summary>Gets the Halfwidth and Fullwidth Forms Unicode block (U+FF00-U+FFEE).</summary>
      <returns>The Halfwidth and Fullwidth Forms Unicode block (U+FF00-U+FFEE).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.HangulCompatibilityJamo">
      <summary>Gets the Hangul Compatibility Jamo Unicode block (U+3131-U+318F).</summary>
      <returns>The Hangul Compatibility Jamo Unicode block (U+3131-U+318F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.HangulJamo">
      <summary>Gets the Hangul Jamo Unicode block (U+1100-U+11FF).</summary>
      <returns>The Hangul Jamo Unicode block (U+1100-U+11FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.HangulJamoExtendedA">
      <summary>Gets the Hangul Jamo Extended-A Unicode block (U+A960-U+A9F).</summary>
      <returns>The Hangul Jamo Extended-A Unicode block (U+A960-U+A97F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.HangulJamoExtendedB">
      <summary>Gets the Hangul Jamo Extended-B Unicode block (U+D7B0-U+D7FF).</summary>
      <returns>The Hangul Jamo Extended-B Unicode block (U+D7B0-U+D7FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.HangulSyllables">
      <summary>Gets the Hangul Syllables Unicode block (U+AC00-U+D7AF).</summary>
      <returns>The Hangul Syllables Unicode block (U+AC00-U+D7AF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Hanunoo">
      <summary>Gets the Hanunoo Unicode block (U+1720-U+173F).</summary>
      <returns>The Hanunoo Unicode block (U+1720-U+173F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Hebrew">
      <summary>Gets the Hebrew Unicode block (U+0590-U+05FF).</summary>
      <returns>The Hebrew Unicode block (U+0590-U+05FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Hiragana">
      <summary>Gets the Hiragana Unicode block (U+3040-U+309F).</summary>
      <returns>The Hiragana Unicode block (U+3040-U+309F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.IdeographicDescriptionCharacters">
      <summary>Gets the Ideographic Description Characters Unicode block (U+2FF0-U+2FFF).</summary>
      <returns>The Ideographic Description Characters Unicode block (U+2FF0-U+2FFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.IpaExtensions">
      <summary>Gets the IPA Extensions Unicode block (U+0250-U+02AF).</summary>
      <returns>The IPA Extensions Unicode block (U+0250-U+02AF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Javanese">
      <summary>Gets the Javanese Unicode block (U+A980-U+A9DF).</summary>
      <returns>The Javanese Unicode block (U+A980-U+A9DF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Kanbun">
      <summary>Gets the Kanbun Unicode block (U+3190-U+319F).</summary>
      <returns>The Kanbun Unicode block (U+3190-U+319F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.KangxiRadicals">
      <summary>Gets the Kangxi Radicals Supplement Unicode block (U+2F00-U+2FDF).</summary>
      <returns>The Kangxi Radicals Supplement Unicode block (U+2F00-U+2FDF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Kannada">
      <summary>Gets the Kannada Unicode block (U+0C81-U+0CFF).</summary>
      <returns>The Kannada Unicode block (U+0C81-U+0CFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Katakana">
      <summary>Gets the Katakana Unicode block (U+30A0-U+30FF).</summary>
      <returns>The Katakana Unicode block (U+30A0-U+30FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.KatakanaPhoneticExtensions">
      <summary>Gets the Katakana Phonetic Extensions Unicode block (U+31F0-U+31FF).</summary>
      <returns>The Katakana Phonetic Extensions Unicode block (U+31F0-U+31FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.KayahLi">
      <summary>Gets the Kayah Li Unicode block (U+A900-U+A92F).</summary>
      <returns>The Kayah Li Unicode block (U+A900-U+A92F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Khmer">
      <summary>Gets the Khmer Unicode block (U+1780-U+17FF).</summary>
      <returns>The Khmer Unicode block (U+1780-U+17FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.KhmerSymbols">
      <summary>Gets the Khmer Symbols Unicode block (U+19E0-U+19FF).</summary>
      <returns>The Khmer Symbols Unicode block (U+19E0-U+19FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Lao">
      <summary>Gets the Lao Unicode block (U+0E80-U+0EDF).</summary>
      <returns>The Lao Unicode block (U+0E80-U+0EDF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Latin1Supplement">
      <summary>Gets the Latin-1 Supplement Unicode block (U+00A1-U+00FF).</summary>
      <returns>The Latin-1 Supplement Unicode block (U+00A1-U+00FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.LatinExtendedA">
      <summary>Gets the Latin Extended-A Unicode block (U+0100-U+017F).</summary>
      <returns>The Latin Extended-A Unicode block (U+0100-U+017F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.LatinExtendedAdditional">
      <summary>Gets the Latin Extended Additional Unicode block (U+1E00-U+1EFF).</summary>
      <returns>The Latin Extended Additional Unicode block (U+1E00-U+1EFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.LatinExtendedB">
      <summary>Gets the Latin Extended-B Unicode block (U+0180-U+024F).</summary>
      <returns>The Latin Extended-B Unicode block (U+0180-U+024F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.LatinExtendedC">
      <summary>Gets the Latin Extended-C Unicode block (U+2C60-U+2C7F).</summary>
      <returns>The Latin Extended-C Unicode block (U+2C60-U+2C7F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.LatinExtendedD">
      <summary>Gets the Latin Extended-D Unicode block (U+A720-U+A7FF).</summary>
      <returns>The Latin Extended-D Unicode block (U+A720-U+A7FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.LatinExtendedE">
      <summary>Gets the Latin Extended-E Unicode block (U+AB30-U+AB6F).</summary>
      <returns>The Latin Extended-E Unicode block (U+AB30-U+AB6F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Lepcha">
      <summary>Gets the Lepcha Unicode block (U+1C00-U+1C4F).</summary>
      <returns>The Lepcha Unicode block (U+1C00-U+1C4F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.LetterlikeSymbols">
      <summary>Gets the Letterlike Symbols Unicode block (U+2100-U+214F).</summary>
      <returns>The Letterlike Symbols Unicode block (U+2100-U+214F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Limbu">
      <summary>Gets the Limbu Unicode block (U+1900-U+194F).</summary>
      <returns>The Limbu Unicode block (U+1900-U+194F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Lisu">
      <summary>Gets the Lisu Unicode block (U+A4D0-U+A4FF).</summary>
      <returns>The Lisu Unicode block (U+A4D0-U+A4FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Malayalam">
      <summary>Gets the Malayalam Unicode block (U+0D00-U+0D7F).</summary>
      <returns>The Malayalam Unicode block (U+0D00-U+0D7F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Mandaic">
      <summary>Gets the Mandaic Unicode block (U+0840-U+085F).</summary>
      <returns>The Mandaic Unicode block (U+0840-U+085F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.MathematicalOperators">
      <summary>Gets the Mathematical Operators Unicode block (U+2200-U+22FF).</summary>
      <returns>The Mathematical Operators Unicode block (U+2200-U+22FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.MeeteiMayek">
      <summary>Gets the Meetei Mayek Unicode block (U+ABC0-U+ABFF).</summary>
      <returns>The Meetei Mayek Unicode block (U+ABC0-U+ABFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.MeeteiMayekExtensions">
      <summary>Gets the Meetei Mayek Extensions Unicode block (U+AAE0-U+AAFF).</summary>
      <returns>The Meetei Mayek Extensions Unicode block (U+AAE0-U+AAFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.MiscellaneousMathematicalSymbolsA">
      <summary>Gets the Miscellaneous Mathematical Symbols-A Unicode block (U+27C0-U+27EF).</summary>
      <returns>The Miscellaneous Mathematical Symbols-A Unicode block (U+27C0-U+27EF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.MiscellaneousMathematicalSymbolsB">
      <summary>Gets the Miscellaneous Mathematical Symbols-B Unicode block (U+2980-U+29FF).</summary>
      <returns>The Miscellaneous Mathematical Symbols-B Unicode block (U+2980-U+29FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.MiscellaneousSymbols">
      <summary>Gets the Miscellaneous Symbols Unicode block (U+2600-U+26FF).</summary>
      <returns>The Miscellaneous Symbols Unicode block (U+2600-U+26FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.MiscellaneousSymbolsandArrows">
      <summary>Gets the Miscellaneous Symbols and Arrows Unicode block (U+2B00-U+2BFF).</summary>
      <returns>The Miscellaneous Symbols and Arrows Unicode block (U+2B00-U+2BFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.MiscellaneousTechnical">
      <summary>Gets the Miscellaneous Technical Unicode block (U+2300-U+23FF).</summary>
      <returns>The Miscellaneous Technical Unicode block (U+2300-U+23FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.ModifierToneLetters">
      <summary>Gets the Modifier Tone Letters Unicode block (U+A700-U+A71F).</summary>
      <returns>The Modifier Tone Letters Unicode block (U+A700-U+A71F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Mongolian">
      <summary>Gets the Mongolian Unicode block (U+1800-U+18AF).</summary>
      <returns>The Mongolian Unicode block (U+1800-U+18AF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Myanmar">
      <summary>Gets the Myanmar Unicode block (U+1000-U+109F).</summary>
      <returns>The Myanmar Unicode block (U+1000-U+109F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.MyanmarExtendedA">
      <summary>Gets the Myanmar Extended-A Unicode block (U+AA60-U+AA7F).</summary>
      <returns>The Myanmar Extended-A Unicode block (U+AA60-U+AA7F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.MyanmarExtendedB">
      <summary>Gets the Myanmar Extended-B Unicode block (U+A9E0-U+A9FF).</summary>
      <returns>The Myanmar Extended-B Unicode block (U+A9E0-U+A9FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.NewTaiLue">
      <summary>Gets the New Tai Lue Unicode block (U+1980-U+19DF).</summary>
      <returns>The New Tai Lue Unicode block (U+1980-U+19DF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.NKo">
      <summary>Gets the NKo Unicode block (U+07C0-U+07FF).</summary>
      <returns>The NKo Unicode block (U+07C0-U+07FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.None">
      <summary>Gets an empty Unicode range.</summary>
      <returns>A Unicode range with no elements.</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.NumberForms">
      <summary>Gets the Number Forms Unicode block (U+2150-U+218F).</summary>
      <returns>The Number Forms Unicode block (U+2150-U+218F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Ogham">
      <summary>Gets the Ogham Unicode block (U+1680-U+169F).</summary>
      <returns>The Ogham Unicode block (U+1680-U+169F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.OlChiki">
      <summary>Gets the Ol Chiki Unicode block (U+1C50-U+1C7F).</summary>
      <returns>The Ol Chiki Unicode block (U+1C50-U+1C7F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.OpticalCharacterRecognition">
      <summary>Gets the Optical Character Recognition Unicode block (U+2440-U+245F).</summary>
      <returns>The Optical Character Recognition Unicode block (U+2440-U+245F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Oriya">
      <summary>Gets the Oriya Unicode block (U+0B00-U+0B7F).</summary>
      <returns>The Oriya Unicode block (U+0B00-U+0B7F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Phagspa">
      <summary>Gets the Phags-pa Unicode block (U+A840-U+A87F).</summary>
      <returns>The Phags-pa Unicode block (U+A840-U+A87F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.PhoneticExtensions">
      <summary>Gets the Phonetic Extensions Unicode block (U+1D00-U+1D7F).</summary>
      <returns>The Phonetic Extensions Unicode block (U+1D00-U+1D7F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.PhoneticExtensionsSupplement">
      <summary>Gets the Phonetic Extensions Supplement Unicode block (U+1D80-U+1DBF).</summary>
      <returns>The Phonetic Extensions Supplement Unicode block (U+1D80-U+1DBF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Rejang">
      <summary>Gets the Rejang Unicode block (U+A930-U+A95F).</summary>
      <returns>The Rejang Unicode block (U+A930-U+A95F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Runic">
      <summary>Gets the Runic Unicode block (U+16A0-U+16FF).</summary>
      <returns>The Runic Unicode block (U+16A0-U+16FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Samaritan">
      <summary>Gets the Samaritan Unicode block (U+0800-U+083F).</summary>
      <returns>The Samaritan Unicode block (U+0800-U+083F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Saurashtra">
      <summary>Gets the Saurashtra Unicode block (U+A880-U+A8DF).</summary>
      <returns>The Saurashtra Unicode block (U+A880-U+A8DF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Sinhala">
      <summary>Gets the Sinhala Unicode block (U+0D80-U+0DFF).</summary>
      <returns>The Sinhala Unicode block (U+0D80-U+0DFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.SmallFormVariants">
      <summary>Gets the Small Form Variants Unicode block (U+FE50-U+FE6F).</summary>
      <returns>The Small Form Variants Unicode block (U+FE50-U+FE6F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.SpacingModifierLetters">
      <summary>Gets the Spacing Modifier Letters Unicode block (U+02B0-U+02FF).</summary>
      <returns>The Spacing Modifier Letters Unicode block (U+02B0-U+02FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Specials">
      <summary>Gets the Specials Unicode block (U+FFF0-U+FFFF).</summary>
      <returns>The Specials Unicode block (U+FFF0-U+FFFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Sundanese">
      <summary>Gets the Sundanese Unicode block (U+1B80-U+1BBF).</summary>
      <returns>The Sundanese Unicode block (U+1B80-U+1BBF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.SundaneseSupplement">
      <summary>Gets the Sundanese Supplement Unicode block (U+1CC0-U+1CCF).</summary>
      <returns>The Sundanese Supplement Unicode block (U+1CC0-U+1CCF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.SuperscriptsandSubscripts">
      <summary>Gets the Superscripts and Subscripts Unicode block (U+2070-U+209F).</summary>
      <returns>The Superscripts and Subscripts Unicode block (U+2070-U+209F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.SupplementalArrowsA">
      <summary>Gets the Supplemental Arrows-A Unicode block (U+27F0-U+27FF).</summary>
      <returns>The Supplemental Arrows-A Unicode block (U+27F0-U+27FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.SupplementalArrowsB">
      <summary>Gets the Supplemental Arrows-B Unicode block (U+2900-U+297F).</summary>
      <returns>The Supplemental Arrows-B Unicode block (U+2900-U+297F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.SupplementalMathematicalOperators">
      <summary>Gets the Supplemental Mathematical Operators Unicode block (U+2A00-U+2AFF).</summary>
      <returns>The Supplemental Mathematical Operators Unicode block (U+2A00-U+2AFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.SupplementalPunctuation">
      <summary>Gets the Supplemental Punctuation Unicode block (U+2E00-U+2E7F).</summary>
      <returns>The Supplemental Punctuation Unicode block (U+2E00-U+2E7F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.SylotiNagri">
      <summary>Gets the Syloti Nagri Unicode block (U+A800-U+A82F).</summary>
      <returns>The Syloti Nagri Unicode block (U+A800-U+A82F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Syriac">
      <summary>Gets the Syriac Unicode block (U+0700-U+074F).</summary>
      <returns>The Syriac Unicode block (U+0700-U+074F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.SyriacSupplement">
      <summary>A <see cref="T:System.Text.Unicode.UnicodeRange" /> corresponding to the 'Syriac Supplement' Unicode block (U+0860..U+086F).</summary>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Tagalog">
      <summary>Gets the Tagalog Unicode block (U+1700-U+171F).</summary>
      <returns>The Tagalog Unicode block (U+1700-U+171F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Tagbanwa">
      <summary>Gets the Tagbanwa Unicode block (U+1760-U+177F).</summary>
      <returns>The Tagbanwa Unicode block (U+1760-U+177F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.TaiLe">
      <summary>Gets the Tai Le Unicode block (U+1950-U+197F).</summary>
      <returns>The Tai Le Unicode block (U+1950-U+197F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.TaiTham">
      <summary>Gets the Tai Tham Unicode block (U+1A20-U+1AAF).</summary>
      <returns>The Tai Tham Unicode block (U+1A20-U+1AAF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.TaiViet">
      <summary>Gets the Tai Viet Unicode block (U+AA80-U+AADF).</summary>
      <returns>The Tai Viet Unicode block (U+AA80-U+AADF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Tamil">
      <summary>Gets the Tamil Unicode block (U+0B80-U+0BFF).</summary>
      <returns>The Tamil Unicode block (U+0B82-U+0BFA).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Telugu">
      <summary>Gets the Telugu Unicode block (U+0C00-U+0C7F).</summary>
      <returns>The Telugu Unicode block (U+0C00-U+0C7F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Thaana">
      <summary>Gets the Thaana Unicode block (U+0780-U+07BF).</summary>
      <returns>The Thaana Unicode block (U+0780-U+07BF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Thai">
      <summary>Gets the Thai Unicode block (U+0E00-U+0E7F).</summary>
      <returns>The Thai Unicode block (U+0E00-U+0E7F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Tibetan">
      <summary>Gets the Tibetan Unicode block (U+0F00-U+0FFF).</summary>
      <returns>The Tibetan Unicode block (U+0F00-U+0FFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Tifinagh">
      <summary>Gets the Tifinagh Unicode block (U+2D30-U+2D7F).</summary>
      <returns>The Tifinagh Unicode block (U+2D30-U+2D7F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.UnifiedCanadianAboriginalSyllabics">
      <summary>Gets the Unified Canadian Aboriginal Syllabics Unicode block (U+1400-U+167F).</summary>
      <returns>The Unified Canadian Aboriginal Syllabics Unicode block (U+1400-U+167F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.UnifiedCanadianAboriginalSyllabicsExtended">
      <summary>Gets the Unified Canadian Aboriginal Syllabics Extended Unicode block (U+18B0-U+18FF).</summary>
      <returns>The Unified Canadian Aboriginal Syllabics Extended Unicode block (U+18B0-U+18FF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.Vai">
      <summary>Gets the Vai Unicode block (U+A500-U+A63F).</summary>
      <returns>The Vai Unicode block (U+A500-U+A63F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.VariationSelectors">
      <summary>Gets the Variation Selectors Unicode block (U+FE00-U+FE0F).</summary>
      <returns>The Variation Selectors Unicode block (U+FE00-U+FE0F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.VedicExtensions">
      <summary>Gets the Vedic Extensions Unicode block (U+1CD0-U+1CFF).</summary>
      <returns>The Vedic Extensions Unicode block (U+1CD0-U+1CFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.VerticalForms">
      <summary>Gets the Vertical Forms Unicode block (U+FE10-U+FE1F).</summary>
      <returns>The Vertical Forms Unicode block (U+FE10-U+FE1F).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.YijingHexagramSymbols">
      <summary>Gets the Yijing Hexagram Symbols Unicode block (U+4DC0-U+4DFF).</summary>
      <returns>The Yijing Hexagram Symbols Unicode block (U+4DC0-U+4DFF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.YiRadicals">
      <summary>Gets the Yi Radicals Unicode block (U+A490-U+A4CF).</summary>
      <returns>The Yi Radicals Unicode block (U+A490-U+A4CF).</returns>
    </member>
    <member name="P:System.Text.Unicode.UnicodeRanges.YiSyllables">
      <summary>Gets the Yi Syllables Unicode block (U+A000-U+A48F).</summary>
      <returns>The Yi Syllables Unicode block (U+A000-U+A48F).</returns>
    </member>
  </members>
</doc>