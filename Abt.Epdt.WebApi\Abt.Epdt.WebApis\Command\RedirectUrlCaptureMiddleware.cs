﻿using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace Abt.Epdt.WebApis.Command
{
    public class RedirectUrlCaptureMiddleware
    {
        private readonly RequestDelegate _next;

        public RedirectUrlCaptureMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            await _next(context);

            if (context.Response.StatusCode == 302 || context.Response.StatusCode == 303)
            {
                string redirectUrl = context.Response.Headers["Location"];

                if (context.Request.Query.TryGetValue("email", out var email))
                {
                    if (!string.IsNullOrEmpty(redirectUrl))
                    {
                        context.Response.Headers["Location"] = $"{redirectUrl}&login_hint={email}";
                    }

                }

            }
        }
    }
}
