﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Abt.Epdt.WebApis.Command
{
    public class BasicAuthMiddleware
    {
        private readonly RequestDelegate _next;

        public BasicAuthMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (context.Request.Path.StartsWithSegments("/swagger"))
            {
                // 检查是否包含授权头
                if (!context.Request.Headers.ContainsKey("Authorization"))
                {
                    context.Response.Headers.Add("WWW-Authenticate", "Basic realm=\"Swagger\"");
                    context.Response.StatusCode = 401;
                    return;
                }

                var authHeader = context.Request.Headers["Authorization"].ToString();
                var encodedUsernamePassword = authHeader.Split(' ', 2, StringSplitOptions.RemoveEmptyEntries)[1]?.Trim();
                var decodedUsernamePassword = Encoding.UTF8.GetString(Convert.FromBase64String(encodedUsernamePassword));
                var username = decodedUsernamePassword.Split(':')[0];
                var password = decodedUsernamePassword.Split(':')[1];

                // 简单验证账号密码（你可以用配置文件或数据库验证）
                if (!((username == "admin1" && password == "password1") ||
                      (username == "admin2" && password == "password2")))
                {
                    context.Response.Headers.Add("WWW-Authenticate", "Basic realm=\"Swagger\"");
                    context.Response.StatusCode = 401;
                    return;
                }

                context.Items["SwaggerUser"] = username; // 将用户名存储到上下文中
            }

            await _next(context);
        }
    }
}
