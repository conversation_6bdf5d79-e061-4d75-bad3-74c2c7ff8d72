﻿
using System.Runtime.InteropServices.JavaScript;
using Newtonsoft.Json;
using SkiaSharp;
using ZXing;



//Console.WriteLine(""); //work around for main is not defined error on code compilation 

public partial class JSBridge
{
    // private static Employee employee { get; set; }

    [JSImport("globalThis.console.log")]
    internal static partial void Log([JSMarshalAs<JSType.String>] string message);

    // [JSExport]
    // internal static string GetEmployee()
    // {
    //     string jsonString = JsonSerializer.Serialize(employee, SourceGenerationContext.Default.Employee);
    //     Log(jsonString);//Logging message log to console
    //     return jsonString;
    // }

    // [JSExport]
    // internal static void SetEmployee(int _id, string _name, int _age, bool _isActive)
    // {
    //     Log($"{nameof(Employee.EmployeeId)}: {_id}, {nameof(Employee.EmployeeName)}: {_name}, {nameof(Employee.EmployeeAge)}: {_age}, {nameof(Employee.IsActive)}: {_isActive},");
    //     employee = new Employee() { EmployeeId = _id, EmployeeName = _name, EmployeeAge = _age, IsActive = _isActive };
    // }

    public static SKBitmap Base64ToBitmap(string base64String)
    {
        // 从 Base64 字符串中移除可能存在的头部信息
        // string pureBase64 = base64String.Contains(",")
        //     ? base64String.Split(',')[1]
        //     : base64String;

        // 将 Base64 字符串解码为字节数组
        byte[] imageBytes = Convert.FromBase64String(base64String);



        Log("1");
        SKMemoryStream stream = new SKMemoryStream(imageBytes);
        return SKBitmap.Decode(stream);
        // 使用 SkiaSharp 从字节数组创建 SKBitmap
        //using (SKMemoryStream stream = new SKMemoryStream(imageBytes))

        //{
        //    Log("2");
        //    return SKBitmap.Decode(stream);
        //}
    }



    public static SKBitmap ConvertToBinary(SKBitmap bitmap, byte threshold = 128)
    {
        // Create a new blank bitmap with the same size
        var binaryBitmap = new SKBitmap(bitmap.Width, bitmap.Height);

        for (int y = 0; y < bitmap.Height; y++)
        {
            for (int x = 0; x < bitmap.Width; x++)
            {
                // Get the color of the pixel
                var color = bitmap.GetPixel(x, y);

                // Convert the pixel to grayscale
                byte gray = (byte)(0.3 * color.Red + 0.59 * color.Green + 0.11 * color.Blue);

                // Apply the threshold to convert to black or white
                var newColor = gray < threshold ? SKColors.Black : SKColors.White;

                // Set the new pixel in the binary bitmap
                binaryBitmap.SetPixel(x, y, newColor);
            }
        }

        return binaryBitmap;
    }




    [JSExport]
    internal static string DecodeImage(string imgbase64)
    {

        // Log(imgbase64);
        // 读取图像文件
        //Bitmap barcodeBitmap = (Bitmap) System.Drawing.Image.from
        var barcodeBitmap = Base64ToBitmap(imgbase64);

        // barcodeBitmap = ConvertToBinary(barcodeBitmap);

        //var barcodeReader = new ZXing.BarcodeReaderGeneric();
        List<string> resList = new List<string>();


        // 创建条形码读取器对象
        var barcodeReader = new ZXing.SkiaSharp.BarcodeReader
        {
            AutoRotate = true,
            Options = new ZXing.Common.DecodingOptions
            {
                PossibleFormats = new[] { BarcodeFormat.CODE_128 },
                TryHarder = true,
                TryInverted = true,
                // ReturnCodabarStartEnd = true,
                PureBarcode = false
            }
        };
        //barcodeReader1.Decode



        // 解码图像中的条形码
        var barcodeResults = barcodeReader.DecodeMultiple(barcodeBitmap);
        // 如果成功识别到条形码
        if (barcodeResults != null && barcodeResults.Length > 0)
        {

            barcodeResults = GetResultsOrderedByPosition(barcodeResults);
            //Console.WriteLine($"Found {barcodeResults.Length} barcodes:");
            Log($"Found {barcodeResults.Length} barcodes:");
            for (int i = 0; i < barcodeResults.Length; i++)
            {


                Log($"Barcode {i + 1}:");
                Log($"  Text: {barcodeResults[i].Text}");
                resList.Add(barcodeResults[i].Text);
                if (barcodeResults[i].ResultPoints.Length > 0)
                {
                    Log($"  Position: ({barcodeResults[i].ResultPoints[0].X}, {barcodeResults[i].ResultPoints[0].Y})");
                    //Console.WriteLine($"  Position: ({barcodeResults[i].ResultPoints[0].X}, {barcodeResults[i].ResultPoints[0].Y})");
                }
            }
        }
        else
        {
            Console.WriteLine("No CODE128 barcodes found in the image.");
        }
        var resJson = JsonConvert.SerializeObject(resList);
        return resJson;
        // return JsonSerializer.Serialize(resList);
        // return String.Join(",", resList.ToArray());
    }


    public static Result[] GetResultsOrderedByPosition(Result[] results)
    {
        // 根据结果的左上角坐标从上到下排序
        var orderedResults = results.OrderBy(r => r.ResultPoints[0].Y)
                                   .ThenBy(r => r.ResultPoints[0].X)
                                   .ToArray();

        return orderedResults;
    }


    [JSExport]
    internal static string Compress(string base64, int maxWidth, int quality = 75, int filterQuality = 0, int imageFormat = 0)
    {

        SKEncodedImageFormat format;
        SKFilterQuality sKFilterQuality;
        if (filterQuality == 0)
        {
            sKFilterQuality = SKFilterQuality.Medium;
        }
        else
        {
            sKFilterQuality = SKFilterQuality.High;
        }

        if (imageFormat == 0)
        {
            format = SKEncodedImageFormat.Jpeg;
        }
        else
        {
            format = SKEncodedImageFormat.Png;
        }


        var bitmap = Base64ToBitmap(base64);
        var width = (decimal)bitmap.Width;
        var height = (decimal)bitmap.Height;
        var newWidth = width;
        var newHeight = height;
        if (width > maxWidth)
        {
            newWidth = maxWidth;
            newHeight = height / width * maxWidth;
        }

        bitmap = bitmap.Resize(new SKImageInfo((int)newWidth, (int)newHeight), sKFilterQuality);
        return Convert.ToBase64String(bitmap.Encode(format, quality).ToArray());
        // bitmap = bitmap.Resize(new SKImageInfo((int)newWidth, (int)newHeight), SKFilterQuality.Medium);
        // return Convert.ToBase64String(bitmap.Encode(SKEncodedImageFormat.Jpeg, quality).ToArray());

        // return bitmap.Encode(SKEncodedImageFormat.Png, quality).SaveTo(output);
    }




}


// [JsonSourceGenerationOptions(WriteIndented = true)]
// [JsonSerializable(typeof(List<String>))]
// internal partial class SourceGenerationContext : JsonSerializerContext
// {

// }



// [JsonSourceGenerationOptions(WriteIndented = true)]
// [JsonSerializable(typeof(Employee))]
// internal partial class SourceGenerationContext : JsonSerializerContext
// {

// }

// public class Employee
// {
//     public int EmployeeId { get; set; }
//     public string EmployeeName { get; set; }
//     public int EmployeeAge { get; set; }
//     public bool IsActive { get; set; }
// }




public class Program
{


    public static string ConvertToBase64(string imagePath)
    {
        byte[] imageArray = File.ReadAllBytes(imagePath);
        string base64ImageRepresentation = Convert.ToBase64String(imageArray);
        return base64ImageRepresentation;
    }

    [STAThread]
    static void Main(params string[] paramaters)
    {

        //string base64String = ConvertToBase64("C:\\Users\\<USER>\\Downloads\\test3_cut3.jpg");
        Console.WriteLine("Base64 string (Method 1):");
        //Console.WriteLine(base64String);

    }
}