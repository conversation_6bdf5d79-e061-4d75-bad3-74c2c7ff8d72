﻿using System;
using System.Activities.Presentation.PropertyEditing;
using System.Collections.Generic;
using System.IdentityModel.Metadata;
using System.Linq;
using System.Runtime.Remoting;
using System.Security.Principal;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Metadata;
using Microsoft.Xrm.Sdk.Query;


namespace Abbott.Onepdt.Plugins
{

    public class MasterDataVerification : IPlugin
    {
        //必填项字典
        private readonly Dictionary<string, string[]> entityRequiredFields = new Dictionary<string, string[]>
        {
            { "onepdt_t_standradcard_config", new[] { "onepdt_name", "onepdt_guarantee_period", "onepdt_main_pacing_mode", "onepdt_mini_wires",  "onepdt_selected_pacing_mode", "statecode" }},
            { "onepdt_t_specify_hospital_config", new[] { "onepdt_name", "onepdt_hospital_sfe","onepdt_card_type", "statecode" } },
            { "onepdt_t_hospital_branch_mapping", new[] { "onepdt_name", "onepdt_hospital_name", "onepdt_sfe", "onepdt_hospital_sap" , "statecode" } },
            { "onepdt_t_mri_management", new[] { "onepdt_name", "statecode" } },
            { "onepdt_t_id_cardtype_management", new[] { "onepdt_name" , "onepdt_type", "statecode" } },
            { "onepdt_t_extended_warranty_config", new[] { "onepdt_name" , "onepdt_max_interval_days", "onepdt_min_interval_days" ,"onepdt_time_expand" , "statecode" } },
            { "onepdt_t_nonmainland_management", new[] { "onepdt_code", "onepdt_sn", "onepdt_name" , "onepdt_expiration_date", "onepdt_production_date", "statecode" } },
            { "onepdt_t_hcp_basic_mdata", new[] { "onepdt_name", "onepdt_sfe_hospital_id", "onepdt_sfe_hospital_name", "onepdt_customer_type", "onepdt_department", "onepdt_vip_status", "onepdt_classification", "onepdt_applicant", "statecode" } },
            //{ "onepdt_t_hospital_basic_mdata", new[] { "onepdt_sfe_name", "onepdt_sfe_code", "onepdt_province_text", "onepdt_city_text", "onepdt_contact", "onepdt_address", "onepdt_classification" } },
            { "onepdt_t_product_mdata", new[] { "onepdt_name", "onepdt_id", "onepdt_type", "onepdt_category", "onepdt_subcategory", "statecode" } },
            { "epdt_t_hospital_basic_mdata",new[] { "onepdt_sfe_name", "onepdt_sfe_code", "epdt_hospital_province", "epdt_hospital_city", "onepdt_tel", "onepdt_address", "onepdt_classification", "statecode" } },
            // 添加其他实体及其必填字段
            { "onepdt_t_print_saleman_mapping",new[] { "onepdt_hospital_code", "onepdt_saleman_hr_code", "statecode" } },
            { "onepdt_t_employee_basic_mdata",new[] { "onepdt_code", "onepdt_code511", "onepdt_name", "onepdt_mail", "onepdt_phone", "onepdt_buid", "onepdt_position_id", "onepdt_role", "onepdt_hiredate", "onepdt_employment_status", "statecode" } }

            


        };
        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService service = serviceFactory.CreateOrganizationService(context.UserId);

            if (context.InputParameters.Contains("Target") && context.InputParameters["Target"] is Entity)
            {
                // 获取实体信息
                Entity targetEntity = (Entity)context.InputParameters["Target"];
                //校验字段必填
                if (entityRequiredFields.ContainsKey(targetEntity.LogicalName))
                {
                    string[] requiredFields = entityRequiredFields[targetEntity.LogicalName];

                    if (context.MessageName.ToLower() == "create")
                    {
                        ValidateRequiredFields(service, targetEntity, requiredFields);
                        ValidateSepcialFields(service, targetEntity);
                        //ValidateHospitalNameUniqueness(service, targetEntity, context);
                        //ValidateHcpCodeUniqueness(service, targetEntity, context);
                        ValidateCardtypeUniqueness(service, targetEntity, context);
                        ValidateHospitalCodeUniqueness(service, targetEntity, context);
                    }
                    else if (context.MessageName.ToLower() == "update")
                    {
                        Entity completeEntity = service.Retrieve(targetEntity.LogicalName, targetEntity.Id, new ColumnSet(true));
                        //targetEntity.Attributes.ToList().ForEach(x => completeEntity[x.Key] = x.Value);
                        foreach (var attribute in completeEntity.Attributes)
                        {
                            if (!targetEntity.Contains(attribute.Key))
                            {
                                targetEntity[attribute.Key] = attribute.Value;
                            }
                        }
                        ValidateRequiredFields(service, targetEntity, requiredFields);
                        ValidateSepcialFields(service, targetEntity);
                        //ValidateHospitalNameUniqueness(service, completeEntity, context);
                        //ValidateHcpCodeUniqueness(service, targetEntity, context);
                        ValidateCardtypeUniqueness(service, targetEntity, context);
                        ValidateHospitalCodeUniqueness(service, targetEntity, context);
                    }
                }
            }
        }

        private void ValidateRequiredFields(IOrganizationService service, Entity entity, string[] requiredFields)
        {

            // 获取元数据以查找显示名称
            RetrieveEntityRequest request = new RetrieveEntityRequest
            {
                EntityFilters = EntityFilters.Attributes,
                LogicalName = entity.LogicalName
            };
            RetrieveEntityResponse response = (RetrieveEntityResponse)service.Execute(request);

            List<string> missingFields = new List<string>();
            foreach (string field in requiredFields)
            {
                if (!entity.Contains(field) || entity[field] == null || string.IsNullOrEmpty(entity[field].ToString()))
                {
                    // 查找字段的显示名称
                    string displayName = response.EntityMetadata.Attributes
                        .FirstOrDefault(a => a.LogicalName == field)?.DisplayName?.UserLocalizedLabel?.Label ?? field;

                    missingFields.Add(displayName);
                }
            }
            if (missingFields.Count > 0)
            {
                throw new InvalidPluginExecutionException($"以下字段是必填的: {string.Join(", ", missingFields)}。");
            }

        }




        private void ValidateSepcialFields(IOrganizationService service, Entity targetEntity)
        {
            string[] productEntities = { "onepdt_t_mri_management", "onepdt_t_specify_hospital_config", "onepdt_t_standradcard_config", "onepdt_t_extended_warranty_config" , "onepdt_t_nonmainland_management" };
            string[] hospitalEntities = { "onepdt_t_specify_hospital_config" };
            string[] hospitalMappingEntities = { "onepdt_t_hospital_branch_mapping" };
            string[] hospitalMDataEntities = { "epdt_t_hospital_basic_mdata" };
            string[] productMDataEntities = { "onepdt_t_product_mdata" };
            string[] hospitalPrintEntities = { "onepdt_t_print_saleman_mapping" };

            ///校验医院代码主数据是否存在
            if (hospitalEntities.Contains(targetEntity.LogicalName))
            {
                ValidateHospitalCode(service, targetEntity, "onepdt_hospital_sfe", "epdt_hospital_code", "epdt_name", "onepdt_hospital_name");
            }
            if (hospitalPrintEntities.Contains(targetEntity.LogicalName))
            {
                ValidateHospitalCode(service, targetEntity, "onepdt_hospital_code", "epdt_hospital_code", "epdt_name", "onepdt_hospital_name");

                ValidateEmployeeCode(service, targetEntity, "onepdt_saleman_hr_code", "onepdt_code", "onepdt_name", "onepdt_saleman_name");
                
            }
            if (hospitalMappingEntities.Contains(targetEntity.LogicalName))
            {
                ValidateHospitalCode(service, targetEntity, "onepdt_sfe", "onepdt_sfe_code", "onepdt_sfe_name", "onepdt_name");
                ValidateHospitalCode(service, targetEntity, "onepdt_hospital_sap", "epdt_hospital_code", "epdt_name", "onepdt_hospital_name");
            }
            ///校验产品型号主数据是否存在
            if (productEntities.Contains(targetEntity.LogicalName))
            {
                QueryExpression query = new QueryExpression();
                string productModel = targetEntity.Contains("onepdt_name") ? targetEntity["onepdt_name"].ToString() : null;
                if (string.IsNullOrEmpty(productModel))
                {
                    throw new InvalidPluginExecutionException("产品型号不存在.");
                }
                query.EntityName = "onepdt_t_product_mdata";
                query.ColumnSet = new ColumnSet("onepdt_name", "statecode");
                query.Criteria.AddCondition("onepdt_name", ConditionOperator.Equal, productModel);
                if (targetEntity.LogicalName == "onepdt_t_extended_warranty_config") {
                  query.Criteria.AddCondition("onepdt_type", ConditionOperator.Equal, "主机");
                }

                EntityCollection results = service.RetrieveMultiple(query);

                if (results.Entities.Count == 0)
                {
                    if (targetEntity.LogicalName == "onepdt_t_extended_warranty_config")
                    {
                        throw new InvalidPluginExecutionException($"主机型号 '{productModel}' 在产品信息不存在.");
                    }

                    throw new InvalidPluginExecutionException($"产品型号 '{productModel}' 在产品信息不存在.");
                }

                Entity productEntity = results.Entities.First();
                if (productEntity.GetAttributeValue<OptionSetValue>("statecode").Value != 0)
                {
                    throw new InvalidPluginExecutionException($"产品型号 '{productModel}' 在产品信息已被停用.");
                }
                if (targetEntity.LogicalName != "onepdt_t_nonmainland_management")
                {
                    targetEntity["onepdt_product_id"] = new EntityReference("onepdt_t_product_mdata", productEntity.Id);
                }
            }
            ///校验产品主数据
            if (productMDataEntities.Contains(targetEntity.LogicalName))
            {
                QueryExpression query = new QueryExpression();
                string typeName = targetEntity["onepdt_type"].ToString();
                string classificationName = targetEntity.Contains("onepdt_classification") ? targetEntity["onepdt_classification"].ToString() : null;
                string categoryName = targetEntity["onepdt_category"].ToString();
                string subcategoryName = targetEntity["onepdt_subcategory"].ToString();
                string brandName = targetEntity.Contains("onepdt_brand") ? targetEntity["onepdt_brand"].ToString() : null;
                string brandgroupName = targetEntity.Contains("onepdt_brandgroup") ? targetEntity["onepdt_brandgroup"].ToString() : null;

                query.EntityName = "onepdt_t_configuration";
                query.ColumnSet = new ColumnSet("onepdt_name", "onepdt_table_name", "onepdt_field_name");
                query.Criteria.AddCondition("onepdt_table_name", ConditionOperator.Equal, "产品信息");
                EntityCollection results = service.RetrieveMultiple(query);


                Entity typeEntity = results.Entities.FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_name") == typeName && e.GetAttributeValue<string>("onepdt_field_name") == "产品类别")
                ?? throw new InvalidPluginExecutionException($"产品类别 '{typeName}' 不存在.");

                if (!string.IsNullOrEmpty(classificationName))
                {
                    Entity classificationEntity = results.Entities.FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_name") == classificationName && e.GetAttributeValue<string>("onepdt_field_name") == "产品分类")
                    ?? throw new InvalidPluginExecutionException($"产品分类 '{classificationName}' 不存在.");
                    targetEntity["onepdt_classification_id"] = new EntityReference("onepdt_t_configuration", classificationEntity.Id);
                }
                Entity categoryEntity = results.Entities.FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_name") == categoryName && e.GetAttributeValue<string>("onepdt_field_name") == "产品大类")
                ?? throw new InvalidPluginExecutionException($"产品大类 '{categoryName}' 不存在.");

                Entity subcategoryEntity = results.Entities.FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_name") == subcategoryName && e.GetAttributeValue<string>("onepdt_field_name") == "产品小类")
                ?? throw new InvalidPluginExecutionException($"产品小类 '{subcategoryName}' 不存在.");

                if (!string.IsNullOrEmpty(brandName))
                {
                    Entity brandEntity = results.Entities.FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_name") == brandName && e.GetAttributeValue<string>("onepdt_field_name") == "品牌")
                    ?? throw new InvalidPluginExecutionException($"品牌 '{brandName}' 不存在.");
                    targetEntity["onepdt_brand_id"] = new EntityReference("onepdt_t_configuration", brandEntity.Id);
                }
                if (!string.IsNullOrEmpty(brandgroupName))
                {
                    Entity brandgroupEntity = results.Entities.FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_name") == brandgroupName && e.GetAttributeValue<string>("onepdt_field_name") == "品牌组")
                    ?? throw new InvalidPluginExecutionException($"品牌组 '{brandgroupName}' 不存在.");
                    targetEntity["onepdt_brandgroup_id"] = new EntityReference("onepdt_t_configuration", brandgroupEntity.Id);

                }
                // 更新lookup字段
                targetEntity["onepdt_type_id"] = new EntityReference("onepdt_t_configuration", typeEntity.Id);
                targetEntity["onepdt_category_id"] = new EntityReference("onepdt_t_configuration", categoryEntity.Id);
                targetEntity["onepdt_subcategory_id"] = new EntityReference("onepdt_t_configuration", subcategoryEntity.Id);
            }
            ///校验医院主数据省市县校验
            if (hospitalMDataEntities.Contains(targetEntity.LogicalName))
            {
                QueryExpression query = new QueryExpression();
                string province = targetEntity.Contains("epdt_hospital_province") ? targetEntity["epdt_hospital_province"].ToString() : "";
                string city = targetEntity.Contains("epdt_hospital_city") ? targetEntity["epdt_hospital_city"].ToString() : "";
                string district = targetEntity.Contains("epdt_hospital_districtandcounty") ? targetEntity["epdt_hospital_districtandcounty"].ToString() : "";
                

                query.EntityName = "onepdt_t_administrative_region_basic_mdata";
                query.ColumnSet = new ColumnSet("onepdt_name", "onepdt_code", "onepdt_parent_code", "onepdt_level");
                List<string> validNames = new List<string>();
                if (!string.IsNullOrEmpty(province)) validNames.Add(province);
                if (!string.IsNullOrEmpty(city)) validNames.Add(city);
                if (!string.IsNullOrEmpty(district)) validNames.Add(district);
                if (validNames.Count > 0)
                {
                    query.Criteria.AddCondition("onepdt_name", ConditionOperator.In, validNames.ToArray());
                }
                EntityCollection results = service.RetrieveMultiple(query);

                // 校验省市县是否符合onepdt_t_administrative_region_basic_mdatas表的结构
                if (!string.IsNullOrEmpty(province))
                {
                    Entity provinceEntity = results.Entities.FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_name") == province && e.GetAttributeValue<int>("onepdt_level") == 1)
                        ?? throw new InvalidPluginExecutionException($"省份 '{province}' 不存在.");

                    targetEntity["onepdt_province"] = new EntityReference("onepdt_t_administrative_region_basic_mdata", provinceEntity.Id);

                    if (!string.IsNullOrEmpty(city))
                    {
                        string provinceCode = provinceEntity.GetAttributeValue<string>("onepdt_code");

                        Entity cityEntity = results.Entities.FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_name") == city && e.GetAttributeValue<int>("onepdt_level") == 2 && e.GetAttributeValue<string>("onepdt_parent_code") == provinceCode)
                            ?? throw new InvalidPluginExecutionException($"城市 '{city}' 不存在或不属于省份 '{province}'.");

                        targetEntity["onepdt_city"] = new EntityReference("onepdt_t_administrative_region_basic_mdata", cityEntity.Id);

                        if (!string.IsNullOrEmpty(district))
                        {
                            string cityCode = cityEntity.GetAttributeValue<string>("onepdt_code");

                            Entity districtEntity = results.Entities.FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_name") == district && e.GetAttributeValue<int>("onepdt_level") == 3 && e.GetAttributeValue<string>("onepdt_parent_code") == cityCode)

                            ?? throw new InvalidPluginExecutionException($"区/县 '{district}' 不存在或不属于城市 '{city}'.");

                            targetEntity["onepdt_district"] = new EntityReference("onepdt_t_administrative_region_basic_mdata", districtEntity.Id);
                        }

                    }
                }
            }
        }


        private void ValidateHospitalCode(IOrganizationService service, Entity targetEntity, string codeFieldName, string dbCodeFieldName, string dbNameFieldName, string nameFieldName)
            {
            string fieldDisplayName;
            if (codeFieldName == "onepdt_sfe")
            {
                fieldDisplayName = "打印医院编码";
            }
            else if (codeFieldName == "onepdt_hospital_sap")
            {
                fieldDisplayName = "植入医院编码";
            }
            else {
                fieldDisplayName = "医院代码";
            }
                string hospitalCode = targetEntity.Contains(codeFieldName) ? targetEntity[codeFieldName].ToString() : null;
                if (string.IsNullOrEmpty(hospitalCode))
                {
                    throw new InvalidPluginExecutionException($"'{fieldDisplayName}'不存在.");
                }

                QueryExpression query = new QueryExpression("epdt_t_hospital_basic_mdata");
                query.ColumnSet = new ColumnSet(dbCodeFieldName, dbNameFieldName, "statecode");
                query.Criteria.AddCondition(dbCodeFieldName, ConditionOperator.Equal, hospitalCode);

                EntityCollection results = service.RetrieveMultiple(query);

                if (results.Entities.Count == 0)
                {
                    throw new InvalidPluginExecutionException($"{fieldDisplayName} '{hospitalCode}' 在医院信息不存在.");
                }

                Entity hospitalEntity = results.Entities.First();
                if (hospitalEntity.GetAttributeValue<OptionSetValue>("statecode").Value != 0)
                {
                    throw new InvalidPluginExecutionException($"{fieldDisplayName} '{hospitalCode}' 在医院信息已被停用.");
                }

                if (targetEntity.Contains(nameFieldName))
                {
                    string hospitalName = targetEntity[nameFieldName].ToString();
                    if (hospitalEntity.GetAttributeValue<string>(dbNameFieldName) != hospitalName)
                    {
                        throw new InvalidPluginExecutionException($"医院名称 '{hospitalName}' 和{fieldDisplayName}不匹配.");
                    }
                }

                // 更新字段
                if (codeFieldName == "onepdt_hospital_sap") { targetEntity["onepdt_implant_hospitalid"] = new EntityReference("epdt_t_hospital_basic_mdata", hospitalEntity.Id); }

                if (codeFieldName == "onepdt_sfe") { targetEntity["onepdt_print_hospitalid"] = new EntityReference("epdt_t_hospital_basic_mdata", hospitalEntity.Id); }

                if (codeFieldName == "onepdt_hospital_sfe") { targetEntity["onepdt_hospitalid"] = new EntityReference("epdt_t_hospital_basic_mdata", hospitalEntity.Id); }

                if (codeFieldName == "onepdt_hospital_code") { targetEntity["onepdt_hospitalid"] = new EntityReference("epdt_t_hospital_basic_mdata", hospitalEntity.Id); }
            

        }

        private void ValidateEmployeeCode(IOrganizationService service, Entity targetEntity, string codeFieldName, string dbCodeFieldName, string dbNameFieldName, string nameFieldName)
        {
            string employeeCode = targetEntity.Contains(codeFieldName) ? targetEntity[codeFieldName].ToString() : null;
            if (string.IsNullOrEmpty(employeeCode))
            {
                throw new InvalidPluginExecutionException("销售代码不存在.");
            }

            QueryExpression employeeQuery = new QueryExpression("onepdt_t_employee_basic_mdata");
            employeeQuery.ColumnSet = new ColumnSet(dbCodeFieldName, dbNameFieldName, "statecode");
            employeeQuery.Criteria.AddCondition(dbCodeFieldName, ConditionOperator.Equal, employeeCode);

            EntityCollection results = service.RetrieveMultiple(employeeQuery);

            if (results.Entities.Count == 0)
            {
                throw new InvalidPluginExecutionException($"销售代码 '{employeeCode}' 在员工信息不存在.");
            }

            Entity employeeEntity = results.Entities.First();
            if (employeeEntity.GetAttributeValue<OptionSetValue>("statecode").Value != 0)
            {
                throw new InvalidPluginExecutionException($"销售代码 '{employeeCode}' 在员工信息已被停用.");
            }

            if (targetEntity.Contains(nameFieldName))
            {
                string employeeName = targetEntity[nameFieldName].ToString();
                if (employeeEntity.GetAttributeValue<string>(dbNameFieldName) != employeeName)
                {
                    throw new InvalidPluginExecutionException($"销售姓名 '{employeeName}' 和销售代码不匹配.");
                }
            }

            // 更新字段
            if (codeFieldName == "onepdt_saleman_hr_code") { targetEntity["onepdt_employeeid"] = new EntityReference("onepdt_t_employee_basic_mdata", employeeEntity.Id); }

        }

        private void ValidateHospitalNameUniqueness(IOrganizationService service, Entity targetEntity ,IPluginExecutionContext context){
            string[] hospitalMDataEntities = { "epdt_t_hospital_basic_mdata" };
             if (hospitalMDataEntities.Contains(targetEntity.LogicalName))
            {
                QueryExpression query = new QueryExpression();
                string hospitalName = targetEntity.Contains("onepdt_sfe_name") ? targetEntity["onepdt_sfe_name"].ToString() : null;

                // Check if hospitalName is unique in onepdt_t_hospital_basic_mdata
                if (!string.IsNullOrEmpty(hospitalName))
                {
                    QueryExpression hospitalQuery = new QueryExpression("epdt_t_hospital_basic_mdata");
                    hospitalQuery.ColumnSet = new ColumnSet("onepdt_sfe_name");
                    hospitalQuery.Criteria.AddCondition("onepdt_sfe_name", ConditionOperator.Equal, hospitalName);

                    // If this is an update operation, exclude the current record
                    if (context.MessageName.ToLower() == "update")
                    {
                        hospitalQuery.Criteria.AddCondition("epdt_t_hospital_basic_mdataid", ConditionOperator.NotEqual, targetEntity.Id);
                    }

                    EntityCollection hospitalResults = service.RetrieveMultiple(hospitalQuery);

                    if (hospitalResults.Entities.Any(e => e.GetAttributeValue<string>("onepdt_sfe_name") == hospitalName))
                    {
                        throw new InvalidPluginExecutionException($"医院名称 '{hospitalName}' 已存在，请使用其他名称。");
                    }
                }
            }
        }

        private void ValidateHospitalCodeUniqueness(IOrganizationService service, Entity targetEntity, IPluginExecutionContext context)
        {
            string[] hospitalMDataEntities = { "onepdt_t_print_saleman_mapping" };
            if (hospitalMDataEntities.Contains(targetEntity.LogicalName))
            {
                string hospitalCode = targetEntity.Contains("onepdt_hospital_code") ? targetEntity["onepdt_hospital_code"].ToString() : null;

                // Check if hospitalName is unique in onepdt_t_hospital_basic_mdata
                if (!string.IsNullOrEmpty(hospitalCode))
                {
                    QueryExpression hospitalQuery = new QueryExpression("onepdt_t_print_saleman_mapping");
                    hospitalQuery.ColumnSet = new ColumnSet("onepdt_hospital_code");
                    hospitalQuery.Criteria.AddCondition("onepdt_hospital_code", ConditionOperator.Equal, hospitalCode);
                    
                    // If this is an update operation, exclude the current record
                    if (context.MessageName.ToLower() == "update")
                    {
                        hospitalQuery.Criteria.AddCondition("onepdt_t_print_saleman_mappingid", ConditionOperator.NotEqual, targetEntity.Id);
                    }

                    EntityCollection hospitalResults = service.RetrieveMultiple(hospitalQuery);

                    if (hospitalResults.Entities.Any(e => e.GetAttributeValue<string>("onepdt_hospital_code") == hospitalCode))
                    {
                        throw new InvalidPluginExecutionException($"医院编码 '{hospitalCode}' 已存在，请使用其他名称。");
                    }
                }
            }
        }


        private void ValidateHcpCodeUniqueness(IOrganizationService service, Entity targetEntity, IPluginExecutionContext context)
        {
            string[] hcpMDataEntities = { "onepdt_t_hcp_basic_mdata" };
            if (hcpMDataEntities.Contains(targetEntity.LogicalName))
            {
                QueryExpression query = new QueryExpression();
                string hcpCode = targetEntity.Contains("onepdt_hcp_code") ? targetEntity["onepdt_hcp_code"].ToString() : null;

                // Check  is unique 
                if (!string.IsNullOrEmpty(hcpCode))
                {
                    QueryExpression hcpQuery = new QueryExpression("onepdt_t_hcp_basic_mdata");
                    hcpQuery.ColumnSet = new ColumnSet("onepdt_hcp_code");
                    hcpQuery.Criteria.AddCondition("onepdt_hcp_code", ConditionOperator.Equal, hcpCode);

                    // If this is an update operation, exclude the current record
                    if (context.MessageName.ToLower() == "update")
                    {
                        hcpQuery.Criteria.AddCondition("onepdt_t_hcp_basic_mdataid", ConditionOperator.NotEqual, targetEntity.Id);
                    }

                    EntityCollection hcpResults = service.RetrieveMultiple(hcpQuery);

                    if (hcpResults.Entities.Any(e => e.GetAttributeValue<string>("onepdt_hcp_code") == hcpCode))
                    {
                        throw new InvalidPluginExecutionException($"医生执业证书编号 '{hcpCode}' 已存在，请使用其他名称。");
                    }
                }
            }
        }

        private void ValidateCardtypeUniqueness(IOrganizationService service, Entity targetEntity, IPluginExecutionContext context)
        {
            string[] cardtypeMDataEntities = { "onepdt_t_id_cardtype_management" };
            if (cardtypeMDataEntities.Contains(targetEntity.LogicalName))
            {
                string cardType = targetEntity.Contains("onepdt_type") ? targetEntity["onepdt_type"].ToString() : null;
                string MAT = targetEntity.Contains("onepdt_name") ? targetEntity["onepdt_name"].ToString() : null;
                QueryExpression query = new QueryExpression("onepdt_t_id_cardtype_management");
                query.ColumnSet = new ColumnSet("onepdt_name", "onepdt_type", "statecode");
                query.Criteria.FilterOperator = LogicalOperator.Or;
                if (!string.IsNullOrEmpty(cardType))
                {
                    query.Criteria.AddCondition("onepdt_type", ConditionOperator.Equal, cardType);
                }
                if (!string.IsNullOrEmpty(MAT))
                {
                    query.Criteria.AddCondition("onepdt_name", ConditionOperator.Equal, MAT);
                }
                EntityCollection results = service.RetrieveMultiple(query);

                foreach (Entity entity in results.Entities)
                {
                if (entity.GetAttributeValue<OptionSetValue>("statecode").Value == 0) // Active state
                    {
                    entity["statecode"] = new OptionSetValue(1); // Inactive state

                    UpdateRequest request = new UpdateRequest()
                    {
                       Target = entity,
                    };
                    request.Parameters.Add("BypassBusinessLogicExecution", "CustomSync,CustomAsync");
                    service.Execute(request);
                    }
                 }
            }
        }

    }


}
