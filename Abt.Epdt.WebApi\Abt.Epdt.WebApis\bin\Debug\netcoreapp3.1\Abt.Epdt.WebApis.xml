<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Abt.Epdt.WebApis</name>
    </assembly>
    <members>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.LoginAndGetRole(System.String)">
            <summary>
            检验用户邮箱并获取角色
            </summary>
            <param name="email">邮箱</param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.GetTodoList(System.String,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            获取待办列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.GetOnepdtConfig(System.String,System.String)">
            <summary>
            获取植入类型
            </summary>
            <returns></returns>
            <summary>
            获取跟台配置
            </summary>
            <param name="tablename"></param>
            <param name="fieldname"></param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.GetProductname(System.String)">
            <summary>
            获取主机型号
            </summary>
            <param name="operationid"></param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.GetPintStatus(System.String)">
            <summary>
            获取打印状态
            </summary>
            <param name="operationid"></param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.GetDapplications(System.String)">
            <summary>
            获取电子保卡
            </summary>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.GetOperationApplication">
            <summary>
            获取跟台数据的保卡信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.GetHospitals(System.String)">
            <summary>
            获取医院信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.GetHCPs(System.String,System.Boolean,System.String)">
            <summary>
            获取医生信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.EditOperation(System.String,Abt.Epdt.WebApis.Model.OperationEditData)">
            <summary>
            跟台数据编辑
            </summary>
            <param name="email"></param>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.GetSAPProduct(System.String,System.Boolean)">
            <summary>
            根据产品序列号获取SAP产品
            </summary>
            <param name="name"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.GetOnePDTProduct(System.String)">
            <summary>
            根据产品序列号获取产品
            </summary>
            <param name="name"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.GetOperation(System.String,System.String)">
            <summary>
            获取跟台数据详情
            </summary>
            <param name="operationid">跟台数据id</param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.SubmitOperation(System.String)">
            <summary>
            提交跟台
            </summary>
            <param name="operationid">跟台数据id</param>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.Delete(System.String,System.String)">
            <summary>
            删除
            </summary>
            <param name="entityname">实体名</param>
            <param name="entityid">实体id</param>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.BatchDelete(Abt.Epdt.WebApis.Model.CardTypeModel.BatchDeleteModel)">
            <summary>
            批量删除
            </summary>
            <param name="model">删除对象</param>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.GetCardTypes(System.String,System.Boolean)">
            <summary>
            预览保卡
            </summary>
            <param name="operationid">跟台数据id</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.GetOperationQuery(System.String,System.String,System.String,System.Int32,System.Int32,System.String,System.String,System.String,System.String)">
            <summary>
            跟台查询
            </summary>
            <param name="roleid">角色id</param>
            <param name="proxyemail">代理邮箱</param>
            <param name="email">邮箱</param>
            <param name="searchText">搜索框</param>
            <param name="implantType">植入类型</param>
            <param name="starttime">植入时间起</param>
            <param name="endtime">植入时间止</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.UploadFileAsync(System.String,System.String,System.String,Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            上传图片
            </summary>
            <param name="operationid">跟台id</param>
            <param name="photoid">图片id</param>
            <param name="classify">图片类型</param>
            <param name="file">文件</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.GetUserManual">
            <summary>
            获取用户使用说明
            </summary>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.GetConfigFile(System.String)">
            <summary>
            获取参数配置文件
            </summary>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AppCommand.CkeckOperation(Abt.Epdt.WebApis.Model.OperationEditData)">
            <summary>
            跟台数据校验
            </summary>
            <param name="model"></param>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.AuthCommand.Checkappid(System.String)">
            <summary>
            检验appid
            </summary>
            <param name="appid">Appid</param>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.EmployeeCommand.GetEmployees(System.String,System.String)">
            <summary>
            获取员工主数据
            </summary>
            <param name="startmodifiedon">开始修改时间</param>
            <param name="endmodifiedon">结束修改时间</param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.HcpCommand.GetHcps(System.String,System.String)">
            <summary>
            获取医生主数据
            </summary>
            <param name="startmodifiedon">开始修改时间</param>
            <param name="endmodifiedon">结束修改时间</param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.PolicyCommand.GetPolicies(System.String,System.String[])">
            <summary>
            获取最新政策
            </summary>
            <param name="applicationID">应用ID</param>
            <param name="policyID">政策ID</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.PolicyCommand.Consent(Abt.Epdt.WebApis.Model.ConsentModel)">
            <summary>
            用户确认
            </summary>
            <param name="param">用户确认参数</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.PolicyCommand.CheckPolicy(System.Int32,System.String,System.String)">
            <summary>
            用户检查
            </summary>
            <param name="type">用户类别</param>
            <param name="applicationID">应用ID</param>
            <param name="id">用户id</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.PolicyCommand.RevokePolicy(System.Int32,System.String,System.String,System.String)">
            <summary>
            用户授权撤销
            </summary>
            <param name="type">用户类别</param>
            <param name="applicationID">应用id</param>
            <param name="id">用户id</param>
            <param name="policyID">政策id</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.PolicyCommand.GetNewPolicies(System.String,System.Int32)">
            <summary>
            获取最新政策
            </summary>
            <param name="applicationID">应用ID</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Command.SFTPCommand.DownloadSftp(System.String)">
            <summary>
            获取SFTP文件
            </summary>
            <param name="env">环境</param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.GetUserInfo(System.String)">
            <summary>
            获取用户信息
            </summary>
            <param name="id">用户id</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.LoginAndGetRole(System.String)">
            <summary>
            检验用户邮箱并获取角色
            </summary>
            <param name="email">邮箱</param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.GetTodoList(System.String,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            获取待办列表
            </summary>
            <param name="email">邮箱</param>
            <param name="type">待办类型</param>
            <param name="pagesize">分页大小</param>
            <param name="pageindex">分页索引</param>
            <param name="searchText">搜索内容</param>
            <param name="implantType">植入类型</param>
            <param name="status">状态</param>
            <param name="hospital">医院</param>
            <param name="starttime">植入开始时间</param>
            <param name="endtime">植入结束时间</param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.GetOnepdtConfig(System.String,System.String)">
            <summary>
            获取跟台配置
            </summary>
            <param name="tablename">配置表名</param>
            <param name="fieldname">配置字段名</param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.GetDapplications(System.String)">
            <summary>
            获取电子保卡
            </summary>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.GetHospitals(System.String)">
            <summary>
            获取医院信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.GetHCPs(System.String,System.Boolean,System.String)">
            <summary>
            获取医生信息
            </summary>
            <param name="isout">是否外部医生</param>
            <param name="hospitalcode">医院编码</param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.EditOperation(Abt.Epdt.WebApis.Model.OperationEditData)">
            <summary>
            跟台数据编辑
            </summary>
            <param name="model">跟台数据Model</param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.GetSAPProduct(System.String,System.Boolean)">
            <summary>
            根据产品序列号SAP产品
            </summary>
            <param name="name">序列号</param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.GetOnePDTProduct(System.String)">
            <summary>
            根据产品序列号获取产品
            </summary>
            <param name="name">序列号</param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.GetOperation(System.String,System.String)">
            <summary>
            获取跟台数据详情
            </summary>
            <param name="operationid">跟台数据id</param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.SubmitOperation(System.String)">
            <summary>
            提交跟台
            </summary>
            <param name="operationid">跟台数据id</param>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.Delete(System.String,System.String)">
            <summary>
            删除
            </summary>
            <param name="entityname">实体名</param>
            <param name="entityid">实体id</param>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.BatchDelete(Abt.Epdt.WebApis.Model.CardTypeModel.BatchDeleteModel)">
            <summary>
            批量删除
            </summary>
            <param name="model">删除对象</param>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.GetCardTypes(System.String,System.Boolean)">
            <summary>
            预览保卡
            </summary>
            <param name="operationid">跟台数据id</param>
            <param name="IsPreview">是否预览</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.GetOperationQuery(System.String,System.String,System.String,System.Int32,System.Int32,System.String,System.String,System.String,System.String)">
            <summary>
            跟台查询
            </summary>
            <param name="roleid">角色id</param>
            <param name="proxyemail">代理邮箱</param>
            <param name="email">邮箱</param>
            <param name="pagesize">分页大小</param>
            <param name="pageindex">分页索引</param>
            <param name="searchText">搜索框</param>
            <param name="implantType">植入类型</param>
            <param name="starttime">植入时间起</param>
            <param name="endtime">植入时间止</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.UploadFileAsync(System.String,System.String,System.String,Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            上传图片
            </summary>
            <param name="operationid">跟台id</param>
            <param name="photoid">图片id</param>
            <param name="classify">图片类型</param>
            <param name="file">文件</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.GetConfigFile(System.String)">
            <summary>
            获取参数文件
            </summary>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.GetUserManual">
            <summary>
            获取用户使用说明
            </summary>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AppController.CkeckOperation(Abt.Epdt.WebApis.Model.OperationEditData)">
            <summary>
            跟台数据校验
            </summary>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AuthController.GetAccessToken(System.String)">
            <summary>
            获取Token
            </summary>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AuthController.ssoindex">
            <summary>
            获取Token
            </summary>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.AuthController.GetSSOAccessToken">
            <summary>
            获取Token
            </summary>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.EmployeeController.GetEmployees(System.String,System.String)">
            <summary>
            获取员工主数据
            </summary>
            <param name="startmodifiedon">开始修改时间</param>
            <param name="endmodifiedon">结束修改时间</param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.HcpController.GetHcps(System.String,System.String)">
            <summary>
            获取医生主数据
            </summary>
            <param name="startmodifiedon">开始修改时间</param>
            <param name="endmodifiedon">结束修改时间</param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.PolicyController.GetPolicies(System.String,System.String[])">
            <summary>
            获取最新政策
            </summary>
            <param name="applicationID">应用ID</param>
            <param name="policyID">政策ID</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.PolicyController.Consent(Abt.Epdt.WebApis.Model.ConsentModel)">
            <summary>
            用户确认
            </summary>
            <param name="param">用户确认参数</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.PolicyController.CheckPolicy(System.Int32,System.String,System.String)">
            <summary>
            用户检查
            </summary>
            <param name="type">用户类别</param>
            <param name="applicationID">应用ID</param>
            <param name="id">用户id</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.PolicyController.RevokePolicy(System.Int32,System.String,System.String,System.String)">
            <summary>
            用户授权撤销
            </summary>
            <param name="type">用户类别</param>
            <param name="applicationID">应用id</param>
            <param name="userID">用户id</param>
            <param name="policyID">政策id</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Controller.SFTPController.DownloadSftp(System.String)">
            <summary>
            获取SFTP文件
            </summary>
            <param name="env">环境</param>
            <returns></returns>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.EmployeeModel.sign">
            <summary>
            数据标识
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.sign">
            <summary>
            数据标识
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.name">
            <summary>
            医生姓名
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.gender">
            <summary>
            性别
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.code">
            <summary>
            医生编码
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.title">
            <summary>
            医生称呼
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.classification">
            <summary>
            医生分类
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.electrophysiologynum">
            <summary>
            电生理手术量
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.coronarynum">
            <summary>
            冠脉手术量
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.heartdiseasenum">
            <summary>
            结构性心脏病手术量
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.department">
            <summary>
            科室
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.customertype">
            <summary>
            客户类型
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.preference">
            <summary>
            使用偏好
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.isvip">
            <summary>
            是否VIP
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.abbottlecturecount">
            <summary>
            为雅培讲课次数
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.administrative">
            <summary>
            行政职务
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.institutionposition">
            <summary>
            学会职称
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.academictitle">
            <summary>
            学术职称
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.abbottrank">
            <summary>
            雅培份额排名
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.hcpcode">
            <summary>
            医生执业证书编号
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.sfehospitalid">
            <summary>
            主要执业机构编码
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.sfehospitalname">
            <summary>
            主要执业机构名称
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.hvclassification">
            <summary>
            HV医生分类
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.application">
            <summary>
            申请人
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.comment">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.HcpModel.modifiedon">
            <summary>
            修改时间
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.id">
            <summary>
            数据id
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.onepdt_device_applicationname">
            <summary>
            关联电子保卡
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.entityname">
            <summary>
            数据实体名称
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.onepdt_name">
            <summary>
            手术编号
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.onepdt_hospitalname">
            <summary>
            医院名称
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.onepdt_patient">
            <summary>
            患者姓名
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.onepdt_submit_status_label">
            <summary>
            提交状态 周报/跟台
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.onepdt_date">
            <summary>
            植入日期
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.onepdt_submittername">
            <summary>
            填报人名称
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.onepdt_submitterid">
            <summary>
            填报人id
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.onepdt_approval_status_label">
            <summary>
            审批状态
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.onepdt_address_approval_status_label">
            <summary>
            邮寄审批状态
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.onepdt_approver_text">
            <summary>
            审批人
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.onepdt_product_name">
            <summary>
            主机型号
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.onepdt_archival_statuslabel">
            <summary>
            归档状态
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.onepdt_print_statuslabel">
            <summary>
            打印状态
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationModel.onepdt_comment">
            <summary>
            退回原因
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.deviceapplicationid">
            <summary>
            关联系统信息——保卡
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.patientName">
            <summary>
            患者姓名
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.patientGender">
            <summary>
            患者性别
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.implantHospital">
            <summary>
            植入医院名字
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.implantHospitalCode">
            <summary>
            医院代码
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.implantHospitalId">
            <summary>
            植入医院id
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.implantDate">
            <summary>
            植入日期
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.firstSurgeon">
            <summary>
            第一术者名称
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.firstSurgeonid">
            <summary>
            第一术者id
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.secondSurgeon">
            <summary>
            第二术者名称
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.secondSurgeonid">
            <summary>
            第二术者id
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.surgicalAssistantType">
            <summary>
            跟台类型名称
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.surgicalAssistantTypeid">
            <summary>
            跟台类型id
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.surgicalAssistant">
            <summary>
            跟台姓名
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.surgeonDuration">
            <summary>
            跟台耗时
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.surgeonDurationid">
            <summary>
            跟台耗时id
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.hisPurkinjeSystemPacing">
            <summary>
            希浦系统起搏
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.hisPurkinjeSystemPacingid">
            <summary>
            希浦系统起搏id
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.icdSurgeryProphylacticLevel">
            <summary>
            ICD手术预防等级
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.icdSurgeryProphylacticLevelid">
            <summary>
            ICD手术预防等级id
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.isIcdPatientWithCadHistory">
            <summary>
            ICD患者是否有冠脉病史
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.implantTypeid">
            <summary>
            植入类型id
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.implantType">
            <summary>
            植入类型
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.originaldeviceid">
            <summary>
            原主机类型id
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.originaldevice">
            <summary>
            原主机类型
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.replaceddeviceid">
            <summary>
            现主机类型id
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.replaceddevice">
            <summary>
            现主机类型
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.isWarrantyCardMailed">
            <summary>
            是否邮寄保卡
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.ksheathvalue">
            <summary>
            是否使用K鞘value
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.ksheathlabel">
            <summary>
            是否使用K鞘
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.receType">
            <summary>
            收件人类型
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.address">
            <summary>
            邮寄地址
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.rece">
            <summary>
            收件人姓名
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.tel">
            <summary>
            联系电话
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.messageNote">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.onepdt_submit_status_label">
            <summary>
            提交状态
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.rejectreason">
            <summary>
            退回原因
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.onepdt_approval_comment">
            <summary>
            业务审批备注
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.onepdt_approval_tag">
            <summary>
            业务审批标签
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.onepdt_address_approval_comment">
            <summary>
            邮寄审批备注
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.onepdt_address_approval_tag">
            <summary>
            邮寄审批标签
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.onepdt_approval_status">
            <summary>
            业务审批状态value
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.onepdt_approval_statuslabel">
            <summary>
            业务审批状态label
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.onepdt_address_approval_status">
            <summary>
            邮寄审批状态value
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.onepdt_address_approval_statuslabel">
            <summary>
            邮寄审批状态label
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.onepdt_is_returned">
            <summary>
            是否退回
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.authorizationLetter">
            <summary>
            授权书
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.receiptForm">
            <summary>
            回执单
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.details">
            <summary>
            跟台植入产品明细
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.deletePreviousProduct">
            <summary>
            删除跟台产品
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationEditData.isedit">
            <summary>
            是否可编辑
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationProductDetail.isNonClinicalTrial">
            <summary>
            是否非临床试验
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationProductDetail.isWearAndTear">
            <summary>
            是否损耗
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationProductDetail.isNonMainlandProduct">
            <summary>
            是否非大陆产品
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationProductDetail.SAPProduct">
            <summary>
            SAP产品
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OperationProductDetail.deflag">
            <summary>
            是否需要删除
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OnePDTProduct.onepdt_name">
            <summary>
            产品型号
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OnePDTProduct.onepdt_type">
            <summary>
            产品类别
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OnePDTProduct.onepdt_classification">
            <summary>
            产品分类
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.OnePDTProduct.onepdt_category">
            <summary>
            产品大类
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.onepdt_t_operation_implantid">
            <summary>
            产品植入ID
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.Name">
            <summary>
            患者姓名
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.Gender">
            <summary>
            患者性别
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.ImpDate">
            <summary>
            植入日期
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.Physician1">
            <summary>
            植入医生1
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.Physician2">
            <summary>
            植入医生2
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.Hospital">
            <summary>
            医院
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.HospitalAdd">
            <summary>
            医院地址
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.HospitalTel">
            <summary>
            医院电话
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.CardType">
            <summary>
            卡类型（A/B/C）
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.CardNo">
            <summary>
            卡编号
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.ModelNo">
            <summary>
            产品型号
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.SN">
            <summary>
            产品序列号
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.WarrantyPeriod">
            <summary>
            担保日期
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.SelectedMode">
            <summary>
            所选起搏模式
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.MainMode">
            <summary>
            主要起搏模式
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.ScanIntensity">
            <summary>
            扫描强度
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.Accessories">
            <summary>
            配件列表
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.Accessory.ModelNo">
            <summary>
            产品型号
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.Accessory.SN">
            <summary>
            产品序列号
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.Accessory.ImpDate">
            <summary>
            植入日期
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.CardTypeModel.Accessory.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.PolicyModel.policyID">
            <summary>
            政策id 
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.PolicyModel.privacyName">
            <summary>
            条款名称 
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.PolicyModel.privacyVersion">
            <summary>
            版本号 
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.PolicyModel.privacyType">
            <summary>
            条款类型 
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.PolicyModel.privacyTypeValue">
            <summary>
            条款类型 
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.PolicyModel.privacyContent">
            <summary>
            条款内容
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.PolicyModel.userType">
            <summary>
            用户类型
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.PolicyModel.userTypeValue">
            <summary>
            用户类别
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.ConsentModel.type">
            <summary>
            用户类别
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.ConsentModel.applicationID">
            <summary>
            应用ID
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.ConsentModel.userID">
            <summary>
            用户id
            </summary>
        </member>
        <member name="P:Abt.Epdt.WebApis.Model.ConsentModel.policyID">
            <summary>
            政策ID
            </summary>
        </member>
        <member name="M:Abt.Epdt.WebApis.Util.AppHelper.ReadAppSettings(System.String[])">
            <summary>
            读取指定节点的字符串
            </summary>
            <param name="sessions"></param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Util.AppHelper.ReadAppSettings``1(System.String[])">
            <summary>
            读取实体信息
            </summary>
            <typeparam name="T"></typeparam>
            <param name="session"></param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Util.CommandHelper.CreateApiLog(System.String,System.String,System.String,System.String,System.String,Microsoft.Xrm.Sdk.IOrganizationService)">
            <summary>
            创建接口日志
            </summary>
            <param name="name">接口名称</param>
            <param name="reqparam">请求参数</param>
            <param name="res">返回结果</param>
            <param name="desc">描述</param>
            <param name="status">状态描述</param>
        </member>
        <member name="M:Abt.Epdt.WebApis.Util.CommandHelper.InvokeAction(Microsoft.Xrm.Sdk.IOrganizationService,System.String,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            调用操作.
            </summary>
            <param name="organizationService"></param>
            <param name="actionName"></param>
            <param name="paramList"></param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Util.CommandHelper.GetFileBase64(Microsoft.Xrm.Sdk.IOrganizationService,System.String,System.String,System.String)">
            <summary>
            获取File类型文件Base64字符
            </summary>
            <param name="svc"></param>
            <param name="entityName"></param>
            <param name="recordGuid"></param>
            <param name="fileAttributeName"></param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Util.CommandHelper.DownloadFile(Microsoft.Xrm.Sdk.IOrganizationService,Microsoft.Xrm.Sdk.EntityReference,System.String)">
            <summary>
            Downloads a file or image in chunks (max 4 MB per chunk)
            </summary>
            <param name="service">The service</param>
            <param name="entityReference">A reference to the record with the file or image column</param>
            <param name="attributeName">The name of the file or image column</param>
            <returns>Base64 encoded string of the downloaded file</returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Util.CommandHelper.UploadFile(Microsoft.Xrm.Sdk.IOrganizationService,Microsoft.Xrm.Sdk.EntityReference,System.String,System.String,System.Byte[],System.String)">
            <summary>
            Downloads a file or image in chunks (max 4 MB per chunk)
            </summary>
            <param name="service">The service</param>
            <param name="entityReference">A reference to the record with the file or image column</param>
            <param name="attributeName">The name of the file or image column</param>
            <returns>Base64 encoded string of the downloaded file</returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Util.CommandHelper.GetMimeType(System.String)">
            <summary>
            根据文件的扩展名获取文件的MIME Type
            </summary>
            <param name="fileName">文件名</param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Util.CommandHelper.GetPsrid(System.String,Microsoft.Xrm.Sdk.IOrganizationService)">
            <summary>
            获取用户id
            </summary>
            <param name="psremail"></param>
            <param name="service"></param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Util.CommandHelper.GetSaleHospital(System.String,Microsoft.Xrm.Sdk.IOrganizationService)">
            <summary>
            获取用户负责医院code
            </summary>
            <param name="psrid"></param>
            <param name="service"></param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Util.CommandHelper.GetEmployee(System.String,Microsoft.Xrm.Sdk.IOrganizationService)">
            <summary>
            获取员工
            </summary>
            <param name="email">邮箱</param>
            <param name="service"></param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Util.CommandHelper.GetRoles(System.String,Microsoft.Xrm.Sdk.IOrganizationService)">
            <summary>
            获取用户角色
            </summary>
            <param name="email"></param>
            <param name="service"></param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Util.CommandHelper.BuildRoleModel(Microsoft.Xrm.Sdk.Entity)">
            <summary>
            返回用户角色
            </summary>
            <param name="item"></param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Util.CommandHelper.GetSalesOrgCode(System.String,System.String,Microsoft.Xrm.Sdk.IOrganizationService)">
            <summary>
            获取销售代表架构人员编码
            </summary>
            <param name="roleid">角色id</param>
            <param name="email">邮箱</param>
            <param name="service"></param>
            <returns></returns>
        </member>
        <member name="M:Abt.Epdt.WebApis.Util.CommandHelper.GetHospitalCode(System.Collections.Generic.List{System.String},Microsoft.Xrm.Sdk.IOrganizationService)">
            <summary>
            获取权限内医院代码
            </summary>
            <param name="psrid">销售代表id</param>
            <param name="service"></param>
            <returns></returns>
        </member>
    </members>
</doc>
