﻿using Microsoft.Xrm.Sdk;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.Onepdt.Plugins.Plugins
{
    public class UpdateFirstSubmissionTime : IPlugin
    {
        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService service = serviceFactory.CreateOrganizationService(null); // 权限
            ITracingService tracingService = (ITracingService)serviceProvider.GetService(typeof(ITracingService));
            try
            {
                if (context.InputParameters.Contains("Target") && context.InputParameters["Target"] is Entity)
                {
                    // 获取实体信息
                    Entity targetEntity = (Entity)context.InputParameters["Target"];

                    // 获取当前记录
                    Entity currentRecord = service.Retrieve(targetEntity.LogicalName, targetEntity.Id, 
                        new Microsoft.Xrm.Sdk.Query.ColumnSet("onepdt_submit_status", "onepdt_first_submit_datetime"));
                    
                    bool preSubmitStatus = currentRecord.Contains("onepdt_submit_status") ? 
                        (bool)currentRecord["onepdt_submit_status"] : false;
                        
                    bool currentSubmitStatus = targetEntity.Contains("onepdt_submit_status") ? 
                        (bool)targetEntity["onepdt_submit_status"] : false;

                    // 检查首次提交时间是否为空
                    bool hasNoFirstSubmitTime = !currentRecord.Contains("onepdt_first_submit_datetime") || 
                        currentRecord["onepdt_first_submit_datetime"] == null;

                    // 只有当状态从false变为true且首次提交时间为空时才更新
                    if (!preSubmitStatus && currentSubmitStatus && hasNoFirstSubmitTime)
                    {
                        targetEntity["onepdt_first_submit_datetime"] = DateTime.UtcNow;
                    }
                    

                }

            }
            catch (Exception ex)
            {
                tracingService.Trace(ex.Message + ex.StackTrace + ex.StackTrace);
                throw;
            }
        }
    }
}
         

