﻿using Abbott.Onepdt.Plugins.Models;
using Microsoft.Xrm.Sdk.Query;
using Microsoft.Xrm.Sdk;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ServiceModel.Channels;

namespace Abbott.Onepdt.Plugins.Service
{
    public class ImplantVerficationService
    {
        public static ImplantModel GetImplantSAPModel(IOrganizationService service,ITracingService tracingService,string sn , string model, DateTime implantDate, string id,string operationId)
        {
            ImplantModel implantModel = new ImplantModel();
            implantModel.SN = sn;
            implantModel.model = model;
            var query = new QueryExpression("onepdt_t_sap_product_mdata");
            query.ColumnSet.AddColumns(
                "onepdt_batch",
                "onepdt_equnr",
                "onepdt_gtin",
                "onepdt_manufacturedate",
                "onepdt_sn",
                "onepdt_valtodate",
                "onepdt_zmod");
            query.Criteria.AddCondition("onepdt_sn", ConditionOperator.Equal, sn);
            query.Criteria.AddCondition("onepdt_zmod", ConditionOperator.Equal, model);
            EntityCollection results = service.RetrieveMultiple(query);

            // 该产品信息不存在

            if (results.Entities.Count > 0)
            {
                implantModel = GetImplantModel(service,tracingService, implantModel, id, operationId);
                if (results.Entities[0].Contains("onepdt_valtodate"))
                {
                    implantModel.IsExpired = results.Entities[0].GetAttributeValue<DateTime>("onepdt_valtodate") < implantDate;
                }
            }
            else {
                implantModel.ResultType = "E";
                implantModel.Message = "该产品信息不存在！";
            }

            return implantModel;
        }


        public static ImplantModel GetImplantNonmainlandModel(IOrganizationService service,ITracingService tracingService, string sn, string model, DateTime implantDate, string id,string operationId)
        {
            ImplantModel implantModel = new ImplantModel();
            implantModel.SN = sn;
            implantModel.model = model;
            var query = new QueryExpression("onepdt_t_nonmainland_management");
            query.ColumnSet.AddColumns(
                "onepdt_sn",
                "onepdt_name",
                "onepdt_expiration_date"
                );
            query.Criteria.AddCondition("onepdt_sn", ConditionOperator.Equal, sn);
            query.Criteria.AddCondition("onepdt_name", ConditionOperator.Equal, model);
            EntityCollection results = service.RetrieveMultiple(query);

            // 该产品信息不存在

            if (results.Entities.Count > 0)
            {
                implantModel = GetImplantModel(service,tracingService, implantModel, id,operationId);
                if (results.Entities[0].Contains("onepdt_expiration_date"))
                {
                    implantModel.IsExpired = results.Entities[0].GetAttributeValue<DateTime>("onepdt_expiration_date") < implantDate;
                }
            }
            else
            {
                implantModel.ResultType = "E";
                implantModel.Message = "该产品信息不存在！";
            }

            return implantModel;
        }
        public static ImplantModel GetImplantModel(IOrganizationService service,ITracingService tracingService, ImplantModel implantModel ,string id,string operationId)
        {
            bool hasExistingRecord =  false;
            string conditionFetch = "";
            if (!string.IsNullOrEmpty(operationId)) {
                conditionFetch = $"<condition attribute='onepdt_operation' operator='ne' value='{operationId}' />";
            }
            string fetchXml = $@"<fetch>
                  <entity name='onepdt_t_operation_implant'>
                    <attribute name='onepdt_name' />
                    <attribute name='onepdt_product_mdata' />
                    <filter>
                      <condition entityname='onepdt_t_product_mdata' attribute='onepdt_name' operator='eq' value='{implantModel.model}' />
                      <condition entityname='onepdt_t_operation' attribute='onepdt_approval_status' operator='ne' value='3' />
                      <condition attribute='onepdt_name' operator='eq' value='{implantModel.SN}' />
                      <condition entityname='onepdt_t_operation' attribute='statecode' operator='eq' value='0' />
                      <condition attribute='statecode' operator='eq' value='0' />
                      {conditionFetch}
                    </filter>
                    <link-entity name='onepdt_t_product_mdata' from='onepdt_t_product_mdataid' to='onepdt_product_mdata' link-type='inner' alias='onepdt_t_product_mdata'>
                      <attribute name='onepdt_category' />
                      <attribute name='onepdt_name' />
                      <attribute name='onepdt_type' />
                    </link-entity>
                    <link-entity name='onepdt_t_operation' from='onepdt_t_operationid' to='onepdt_operation' link-type='inner' alias='onepdt_t_operation'>
                      <attribute name='onepdt_approval_status' />
                        <filter>
                         <condition attribute='statecode'  operator='eq' value='0' />
                         <condition attribute='onepdt_approval_status' operator='ne' value='3' />
                        </filter>
                    </link-entity>
                  </entity>
                </fetch>";

            EntityCollection results = service.RetrieveMultiple(new FetchExpression(fetchXml));

            hasExistingRecord = results.Entities.Any();

            if (hasExistingRecord)
            {
                implantModel.ResultType = "E";
                implantModel.Message = "该产品已有使用记录！";
                tracingService.Trace("该产品已有使用记录");

                tracingService.Trace(fetchXml);

            }
            else
            {
                implantModel.ResultType = "S";
            }
            return implantModel;
        }


    }
}
