﻿using Abt.Epdt.WebApis.Command;
using Abt.Epdt.WebApis.Util;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OAuth.Claims;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Sustainsys.Saml2.AspNetCore2;
using Sustainsys.Saml2.Saml2P;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Abt.Epdt.WebApis.Controller
{
    [Route("api/auth")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly IMemoryCache _memoryCache;
        private readonly AuthCommand com;
        private readonly AppCommand app;
        private readonly IWebHostEnvironment _env;
        private static readonly List<string> AllowedDomains = new List<string>
        {
            "abbott.com.cn",
            "localhost"
        };

        #region MyRegion
        public AuthController(IMemoryCache memoryCache, IWebHostEnvironment env)
        {
            _memoryCache = memoryCache;
            _env = env;
            com = new AuthCommand(_memoryCache);
            app = new AppCommand(_memoryCache);

        }

        /// <summary>
        /// SFE获取Token
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("token")]
        public ActionResult GetAccessToken(string appid)
        {
            try
            {
                //验证appid
                com.Checkappid(appid);
            }
            catch (Exception ex)
            {
                return BadRequest("Invalid Request：" + ex.Message);
            }

            var claims = new[]
            {
                new Claim(ClaimTypes.Name, appid),
                new Claim(ClaimTypes.Role, ""),
            };

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(TokenParameter.SFESecret));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
            var jwtToken = new JwtSecurityToken(TokenParameter.Issuer, TokenParameter.Audience, claims, expires: DateTime.UtcNow.AddMinutes(TokenParameter.AccessExpiration), signingCredentials: credentials);
            var token = new JwtSecurityTokenHandler().WriteToken(jwtToken);

            return Ok(token);
        }

        /// <summary>
        /// 获取Token
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("ssotoken")]
        public ActionResult GetSSOAccessToken()
        {

            //try
            //{
            //    //验证appid
            //    com.Checkappid(appid);
            //}
            //catch (Exception ex)
            //{
            //    return BadRequest("Invalid Request：" + ex.Message);
            //}

            //var claims = new[]
            //{
            //    new Claim(ClaimTypes.Name, ""),
            //    new Claim(ClaimTypes.Role, ""),
            //};

            //var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(TokenParameter.Secret));
            //var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
            //var jwtToken = new JwtSecurityToken(TokenParameter.Issuer, TokenParameter.Audience, claims, expires: DateTime.UtcNow.AddMinutes(TokenParameter.AccessExpiration), signingCredentials: credentials);
            //var token = new JwtSecurityTokenHandler().WriteToken(jwtToken);

            //return Ok(token);
            return Redirect("/ssoindex");
        }


        [HttpGet("login")]
        public IActionResult Login(string signonurl, string errorurl)
        {

            var redirectUri = "";
            redirectUri = $"/api/auth/login/success?signonurl={HttpUtility.UrlEncode(signonurl)}&errorurl={HttpUtility.UrlEncode(errorurl)}";
            return Challenge(new AuthenticationProperties
            {
                RedirectUri = redirectUri
            }, Saml2Defaults.Scheme);
        }

        [HttpGet("me")]
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        public ActionResult Me()
        {
            //var userdata = User.FindFirst(ClaimTypes.UserData)?.Value;
            //return Ok(userdata);
            var result = app.LoginAndGetRole(User.FindFirst(ClaimTypes.Email).Value);
            return Ok(result);
        }
        #endregion

        [HttpGet("login/skip")]
        public async Task<ActionResult> LoginSkipSsoSuccess(string signonurl, string abbottemail, string errorurl)
        {

            String environment = AppHelper.ReadAppSettings("Environment");
            if (environment == "prod")
            {
                return NotFound();
            }


            try
            {
                var originalKey = Encoding.UTF8.GetBytes(TokenParameter.Secret);
                var trimmedKey = new byte[32];
                Array.Copy(originalKey, trimmedKey, 32); // 裁剪为 32 字节

                var encryptingKey = new SymmetricSecurityKey(trimmedKey);

                // 设置签名密钥
                var signingCredentials = new SigningCredentials(encryptingKey, SecurityAlgorithms.HmacSha256);

                var encryptingCredentials = new EncryptingCredentials(
                            encryptingKey,
                            SecurityAlgorithms.Aes256KW,
                            SecurityAlgorithms.Aes256CbcHmacSha512);


                var result = app.LoginAndGetRole(abbottemail);
                var claims = new[]
                {
                    new Claim(ClaimTypes.Name, result.username),
                    new Claim(ClaimTypes.Email,abbottemail),
                    new Claim(ClaimTypes.UserData, JsonConvert.SerializeObject(result)),
                };

                // TimeZoneInfo chinaTimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");
                // DateTime chinaNow = TimeZoneInfo.ConvertTime(DateTime.UtcNow, chinaTimeZone);

                // // 计算第二天凌晨两点的时间
                // DateTime expiry = new DateTime(chinaNow.Year, chinaNow.Month, chinaNow.Day, 2, 0, 0).AddDays(1);


                // DateTime expiryUtc = TimeZoneInfo.ConvertTimeToUtc(expiry, chinaTimeZone);

                DateTime expiryUtc = DateTime.UtcNow.AddHours(24);
                //DateTime expiry = DateTime.Today.AddDays(1).AddTicks(-1).AddHours(2);



                // 创建令牌描述
                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(claims),
                    Expires = expiryUtc, // 设置过期时间
                    Issuer = TokenParameter.Issuer, // 设置颁发者
                    Audience = TokenParameter.Audience, // 设置接收者
                    SigningCredentials = signingCredentials,
                    EncryptingCredentials = encryptingCredentials // 设置加密凭据
                };

                // 生成 JWT
                var tokenHandler = new JwtSecurityTokenHandler();
                var token = tokenHandler.CreateToken(tokenDescriptor);

                // 输出加密后的 JWT
                var encryptedJwt = tokenHandler.WriteToken(token);

                //double maxAge = (expiry - DateTime.UtcNow).TotalSeconds; // 计算 MaxAge 的秒数
                Response.Cookies.Append("onepdtToken", encryptedJwt, new Microsoft.AspNetCore.Http.CookieOptions
                {
                    MaxAge = TimeSpan.FromHours(24)
                });

                Uri uri;
                if (signonurl.Contains("localhost") || (Uri.TryCreate(signonurl, UriKind.Absolute, out uri)))
                {
                    return Redirect(signonurl);
                }
                // 如果 URL 不合法或域名不受信任
                return BadRequest("Invalid redirect URL.");
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                if (ex.Message.StartsWith("不存在"))
                {
                    return Redirect($"{errorurl}?type=accountnotexist&message={HttpUtility.UrlEncode(ex.Message)}");
                }
                throw ex;
            }
        }


        [HttpGet("login/success")]
        [Authorize(AuthenticationSchemes = "Cookies")]
        public async Task<ActionResult> LoginSuccess(string signonurl, string errorurl)
        {
            try
            {
                var Users = User.Claims;
                var userClaims = HttpContext.User;
                var emailClaim = userClaims?.FindFirst(ClaimTypes.Email);
                if (emailClaim != null)
                {
                    var originalKey = Encoding.UTF8.GetBytes(TokenParameter.Secret);
                    var trimmedKey = new byte[32];
                    Array.Copy(originalKey, trimmedKey, 32); // 裁剪为 32 字节

                    var encryptingKey = new SymmetricSecurityKey(trimmedKey);

                    // 设置签名密钥
                    var signingCredentials = new SigningCredentials(encryptingKey, SecurityAlgorithms.HmacSha256);

                    var encryptingCredentials = new EncryptingCredentials(
                                encryptingKey,
                                SecurityAlgorithms.Aes256KW,
                                SecurityAlgorithms.Aes256CbcHmacSha512);

                    var email = "";
                    email = emailClaim.Value;
                    var result = app.LoginAndGetRole(email);
                    var claims = new[]
                    {
                        new Claim(ClaimTypes.Name, result.username),
                        new Claim(ClaimTypes.Email,email),
                        new Claim(ClaimTypes.UserData, JsonConvert.SerializeObject(result)),
                    };

                    DateTime expiryUtc = DateTime.UtcNow.AddHours(24);

                    // 创建令牌描述
                    var tokenDescriptor = new SecurityTokenDescriptor
                    {
                        Subject = new ClaimsIdentity(claims),
                        Expires = expiryUtc, // 设置过期时间
                        Issuer = TokenParameter.Issuer, // 设置颁发者
                        Audience = TokenParameter.Audience, // 设置接收者
                        SigningCredentials = signingCredentials,
                        EncryptingCredentials = encryptingCredentials // 设置加密凭据
                    };

                    // 生成 JWT
                    var tokenHandler = new JwtSecurityTokenHandler();
                    var token = tokenHandler.CreateToken(tokenDescriptor);

                    // 输出加密后的 JWT
                    var encryptedJwt = tokenHandler.WriteToken(token);

                    var identity = new ClaimsIdentity(claims, "OnepdtAuth");
                    var principal = new ClaimsPrincipal(identity);


                    var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(TokenParameter.Secret));
                    var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);



                    //SSO认证完成后删除cookie，不然访问除了认证URL的页面会触发WAF 403.目前只有api/auth/login/success和Saml2/Acs会有放行
                    foreach (string name in Request.Cookies.Keys)
                    {
                        if (name != "onepdtToken")
                        {
                            Response.Cookies.Delete(name);
                        }
                    }
                    Response.Cookies.Append("onepdtToken", encryptedJwt, new Microsoft.AspNetCore.Http.CookieOptions
                    {
                        //Secure = true,
                        MaxAge = TimeSpan.FromHours(24)
                    });

                    //Response.Cookies.Delete



                    Uri uri;
                    if (signonurl.Contains("localhost") || (Uri.TryCreate(signonurl, UriKind.Absolute, out uri)))
                    {
                        return Redirect(signonurl);
                    }
                    // 如果 URL 不合法或域名不受信任
                    return BadRequest("Invalid redirect URL.");
                }
                else
                {
                    return Unauthorized();
                    // 如果没有找到电子邮件地址，可能是 SAML 断言中没有提供该信息
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);

                if (ex.Message.StartsWith("不存在"))
                {
                    return Redirect($"{errorurl}?type=accountnotexist&message={HttpUtility.UrlEncode(ex.Message)}");

                }
                throw ex;

            }
        }
    }



    public class TokenParameter
    {
        public const string Issuer = "CRM";//颁发者        
        public const string Audience = "Other";//接收者        
        public const string Secret = "K8306O8nMxYouwFeeMKwRQZakVo069yde9S5dz167nB3jKuS405yAUyZ2GmamNZ2";//签名秘钥
        public const string SFESecret = "y8F0eT7gkD45Nxzv9BpYZVtm4XChvklJG2pRdsy7M3K5a9QpT46Wbf0c8Vh3JY6P8dNqChTzVbmQp72J9L8QwVmN2Zh8RqCt";//签名秘钥
        public const int AccessExpiration = 120;//AccessToken过期时间（分钟）

        public const string TokenEncryptionKey = "SSljsdkkdlo4454Maakikjhsd55GaRTP"; // 32字节的密钥

    }
}
