﻿using System;
using System.Linq;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System.Activities;
using System.Collections.Generic;

namespace Abbott.Onepdt.Plugins
{
    public class ReverseOperationAction : IPlugin
    {
        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService service = serviceFactory.CreateOrganizationService(null);
            ITracingService tracingService = (ITracingService)serviceProvider.GetService(typeof(ITracingService));

            try
            {
                string operationId = (string)context.InputParameters["OperationId"];
               
                // Retrieve the onepdt_t_operation record
                Entity operation = service.Retrieve("onepdt_t_operation", new Guid(operationId), new ColumnSet(true));

                // Update the approval status to "已冲销"
                operation["onepdt_approval_status"] = new OptionSetValue(3);
                service.Update(operation);

                QueryExpression patientIDCardquery = new QueryExpression("onepdt_t_patient_id_card")
                {
                    ColumnSet = new ColumnSet()
                };
                patientIDCardquery.Criteria.AddCondition("onepdt_t_operation", ConditionOperator.Equal, operation.Id);

                EntityCollection patientIDCards = service.RetrieveMultiple(patientIDCardquery);

                foreach (Entity patientIDCard in patientIDCards.Entities)
                {
                    patientIDCard["statecode"] = new OptionSetValue(1);
                    patientIDCard["statuscode"] = new OptionSetValue(2);
                    service.Update(patientIDCard);
                }

                // Try to retrieve existing record
                QueryExpression query = new QueryExpression("epdt_t_device_mdata")
                {
                    ColumnSet = new ColumnSet()
                };
                query.Criteria.AddCondition("epdt_operation_number", ConditionOperator.Equal, operation.GetAttributeValue<string>("onepdt_name"));

                EntityCollection existingDevices = service.RetrieveMultiple(query);

                foreach (Entity existingDevice in existingDevices.Entities)
                {
                    existingDevice["epdt_device_status"] = "已冲销";
                    service.Update(existingDevice);
                }

                // Retrieve related onepdt_t_operation_implant records
                QueryExpression implantQuery = new QueryExpression("onepdt_t_operation_implant")
                {
                    ColumnSet = new ColumnSet(true)
                };
                implantQuery.Criteria.AddCondition("onepdt_operation", ConditionOperator.Equal, operation.Id);
                EntityCollection implants = service.RetrieveMultiple(implantQuery);

                foreach (Entity implant in implants.Entities)
                {
                    implant["statecode"] = new OptionSetValue(1);
                    implant["statuscode"] = new OptionSetValue(2);
                    service.Update(implant);
                }

                // Retrieve related onepdt_t_operation_implant records
                QueryExpression imageQuery = new QueryExpression("onepdt_t_opreation_photo")
                {
                    ColumnSet = new ColumnSet(true) // Include photo field explicitly
                };
                imageQuery.Criteria.AddCondition("onepdt_operation", ConditionOperator.Equal, operation.Id);
                EntityCollection images = service.RetrieveMultiple(imageQuery);



                // Create a copy of the onepdt_t_operation record with a new operation number
                var operationRemoveList = new string[] { "onepdt_t_operationid", "onepdt_patient_id_card", "onepdt_is_returned", "onepdt_approval_status", "onepdt_address_approval_status", "onepdt_submit_status" };
                foreach (var attribute in operationRemoveList)
                {
                    operation.Attributes.Remove(attribute);
                }
                operation["onepdt_is_returned"] = true;
                string oldOperationNumber = operation["onepdt_name"].ToString();
                int lastTwoDigits;
                string newOperationNumber;
                //历史迁移手术编号
                if (oldOperationNumber.Length == 15)
                {
                    lastTwoDigits = 1;
                    newOperationNumber = oldOperationNumber.Substring(0, oldOperationNumber.Length) + (lastTwoDigits + 1).ToString("D2");
                    operation["onepdt_name"] = newOperationNumber;
                }
                else
                {
                    lastTwoDigits = int.Parse(oldOperationNumber.Substring(oldOperationNumber.Length - 2));
                    newOperationNumber = oldOperationNumber.Substring(0, oldOperationNumber.Length - 2) + (lastTwoDigits + 1).ToString("D2");
                    operation["onepdt_name"] = newOperationNumber;
                }
                operation.Id = Guid.NewGuid();
                var newOperationId = service.Create(operation);

                //Create copies of the related onepdt_t_operation_implant records with the new operation id

                foreach (Entity updateImplant in implants.Entities)
                {
                    updateImplant.Attributes.Remove("onepdt_t_operation_implantid");
                    updateImplant.Attributes.Remove("statecode");
                    updateImplant.Attributes.Remove("statuscode");
                    updateImplant["onepdt_operation"] = new EntityReference("onepdt_t_operation", newOperationId);
                    updateImplant.Id = Guid.NewGuid();
                    service.Create(updateImplant);
                }

                //Update of the related onepdt_t_opreation_photo records with the new operation id
                foreach (Entity image in images.Entities)
                {
                    image["onepdt_operation"] = new EntityReference("onepdt_t_operation", newOperationId);
                    image["onepdt_operation_number"] = newOperationNumber;
                    service.Update(image);
                }


            }
            catch (Exception ex)
            {
                tracingService.Trace(ex.Message + ex.StackTrace);
                throw;
            }
        }
    }
}
