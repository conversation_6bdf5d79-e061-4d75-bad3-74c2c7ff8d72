﻿using Abt.Epdt.WebApis.Util;
using Renci.SshNet;
using System.IO;
using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.Extensions.Caching.Memory;
using System.Linq;

namespace Abt.Epdt.WebApis.Command
{
    //public class SFTPCommand : OrgService
    //{
    //    //string host = "sjmdfpaa01";
    //    //string host = "***********"; //dev
    //    //string host = "***********"; //test
    //    //string host = "**********"; //prod
    //    //string host = "sjmsftp.oneabbott.com";
    //    //string host = "sjmsftp.freestyle-libre.cn";
    //    int port = 22;
    //    string username = "onepdtadm";
    //    string password = "AumoC@td*c@2!PLH";
    //    string OutDirectory = "/MDCN/ECC/ONEPDT/Out";
    //    string ArchiveDirectory = "/MDCN/ECC/ONEPDT/Archive";

    //    public SFTPCommand(IMemoryCache memoryCache) : base(memoryCache)
    //    {
    //    }

    //    /// <summary>
    //    /// 获取SFTP文件
    //    /// </summary>
    //    /// <param name="env">环境</param>
    //    /// <returns></returns>
    //    /// <exception cref="Exception"></exception>
    //    public List<sftpfilemodel> DownloadSftp(string env)
    //    {
    //        var list = new List<sftpfilemodel>();
    //        var host = string.Empty;
    //        if (env == "dev")
    //        {
    //            host = "***********";
    //        }
    //        else if (env == "test")
    //        {
    //            host = "***********";
    //        }
    //        else if (env == "prod")
    //        {
    //            host = "**********";
    //        }
    //        try
    //        {
    //            // 创建SFTP连接
    //            using (var sftp = new SftpClient(host, port, username, password))
    //            {
    //                sftp.Connect();
    //                if (sftp.IsConnected)
    //                {
    //                    // 获取指定目录下的文件列表
    //                    var files = sftp.ListDirectory(OutDirectory).Where(f => !f.IsDirectory && f.Name != "." && f.Name != "..").ToList(); ;
    //                    foreach (var fileitem in files)
    //                    {
    //                        var mod = new sftpfilemodel();
    //                        mod.name = fileitem.Name;
    //                        list.Add(mod);
    //                    }
    //                    //foreach (var file in files)
    //                    //{
    //                    //    if (!file.IsDirectory)
    //                    //    {
    //                    //        string localFilePath = Path.Combine(Directory.GetCurrentDirectory(), file.Name);
    //                    //        using (var fileStream = File.Create(localFilePath))
    //                    //        {
    //                    //            var mod = new sftpfilemodel();
    //                    //            //sftp.DownloadFile(Path.Combine(OutDirectory, file.Name), fileStream);

    //                    //            byte[] buffer = new byte[fileStream.Length];
    //                    //            fileStream.Read(buffer, 0, (int)fileStream.Length);
    //                    //            string base64String = Convert.ToBase64String(buffer);
    //                    //            mod.name = file.Name;
    //                    //            list.Add(mod);
    //                    //        }
    //                    //        Console.WriteLine($"已下载文件: {file.Name}");
    //                    //    }
    //                    //}
    //                    sftp.Disconnect();
    //                }
    //                else
    //                {
    //                    Console.WriteLine("无法连接到SFTP服务器。");
    //                }
    //            }
    //        }
    //        catch (Exception ex)
    //        {

    //            throw new Exception(ex.Message);
    //        }

    //        return list;
    //    }
    //}

    //public class sftpfilemodel
    //{
    //    public string name { get; set; }
    //    public string base64str { get; set; }
    //}
}
