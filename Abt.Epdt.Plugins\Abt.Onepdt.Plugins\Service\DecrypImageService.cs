﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace Abbott.Onepdt.Plugins.Service
{
    public class DecrypImageService
    {
        public static byte[] Encrypt(byte[] byteContent ,string SEED)
        {
            try
            {
                using (var aes = Aes.Create())
                {
                    aes.Key = Encoding.UTF8.GetBytes(SEED);
                    aes.IV = new byte[16]; // AES block size in bytes

                    using (var encryptor = aes.CreateEncryptor(aes.Key, aes.IV))
                    {
                        using (var memoryStream = new MemoryStream())
                        {
                            using (var cryptoStream = new CryptoStream(memoryStream, encryptor, CryptoStreamMode.Write))
                            {
                                cryptoStream.Write(byteContent, 0, byteContent.Length);
                                cryptoStream.FlushFinalBlock();
                                // Since we're not using the IV, make sure to save it for the decrypt method
                                // prepend it to the encrypted data
                                var encryptedData = memoryStream.ToArray();
                                var combinedIvAndCiphertext = new byte[aes.IV.Length + encryptedData.Length];
                                Array.Copy(aes.IV, 0, combinedIvAndCiphertext, 0, aes.IV.Length);
                                Array.Copy(encryptedData, 0, combinedIvAndCiphertext, aes.IV.Length, encryptedData.Length);

                                return combinedIvAndCiphertext;
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                throw new Exception("Error encrypting: " + e.Message);
            }
        }

        public static string Decrypt(byte[] content, string key)
        {
            try
            {

                using (AesCryptoServiceProvider aesProvider = new AesCryptoServiceProvider())
                {
                    aesProvider.Key = Convert.FromBase64String(key);
                    aesProvider.Mode = CipherMode.ECB;
                    aesProvider.Padding = PaddingMode.PKCS7;
                    using (ICryptoTransform cryptoTransform = aesProvider.CreateDecryptor())
                    {
                        byte[] inputBuffers = content;
                        byte[] results = cryptoTransform.TransformFinalBlock(inputBuffers, 0, inputBuffers.Length);
                        aesProvider.Clear();
                        return Convert.ToBase64String(results);
                    }
                }

            }
            catch (Exception e)
            {
                throw new Exception("Error decrypting: " + e.Message);
            }
        }



    }

}
