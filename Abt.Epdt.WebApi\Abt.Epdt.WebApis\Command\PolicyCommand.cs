﻿using Abt.Epdt.WebApis.Model;
using Abt.Epdt.WebApis.Util;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using static System.Net.Mime.MediaTypeNames;

namespace Abt.Epdt.WebApis.Command
{
    public class PolicyCommand : OrgService
    {
        public PolicyCommand(IMemoryCache memoryCache) : base(memoryCache)
        {
        }

        /// <summary>
        /// 获取最新政策
        /// </summary>
        /// <param name="applicationID">应用ID</param>
        /// <param name="policyID">政策ID</param>
        /// <returns></returns>
        public ResModel GetPolicies(string applicationID, string[] policyID)
        {
            var res = new ResModel();

            res.code = "200000";

            try
            {
                if (string.IsNullOrWhiteSpace(applicationID))
                {
                    throw new Exception("请传入应用ID");
                }

                var list = new List<PolicyModel>();

                var qe = new QueryExpression("epdt_privacy_policy_terms");
                qe.ColumnSet.AllColumns = true;
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition("epdt_appid", ConditionOperator.Equal, applicationID);
                qe.Criteria.AddCondition("statuscode", ConditionOperator.Equal, 6);
                if (policyID != null && policyID.Length > 0)
                {
                        qe.Criteria.AddCondition("epdt_name", ConditionOperator.In, policyID);
                }
                //if(!Array.)
                //if (!string.IsNullOrWhiteSpace(policyID))
                //{
                //    qe.Criteria.AddCondition("epdt_name", ConditionOperator.Equal, policyID);
                //}
                qe.AddOrder("createdon", OrderType.Descending);
                var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                if (ec != null && ec.Entities.Count > 0)
                {
                    foreach (var item in ec.Entities)
                    {
                        var model = new PolicyModel();
                        //政策id
                        if (item.Contains("epdt_name"))
                            model.policyID = item.GetAttributeValue<string>("epdt_name");
                        //条款类型
                        if (item.Contains("epdt_term_type"))
                        {
                            model.privacyType = item.FormattedValues["epdt_term_type"];
                            model.privacyTypeValue = item.GetAttributeValue<OptionSetValue>("epdt_term_type").Value;
                        }
                        //条款名称
                        if (item.Contains("epdt_policy_term_id"))
                            model.privacyName = item.GetAttributeValue<string>("epdt_policy_term_id");
                        //条款版本
                        if (item.Contains("epdt_policy_term_version"))
                            model.privacyVersion = item.GetAttributeValue<string>("epdt_policy_term_version");
                        //条款内容
                        if (item.Contains("epdt_policy_term_content"))
                            model.privacyContent = item.GetAttributeValue<string>("epdt_policy_term_content");
                        //条款用户类别
                        if (item.Contains("epdt_usertype"))
                        {
                            model.userType = item.FormattedValues["epdt_usertype"];
                            model.userTypeValue = item.GetAttributeValue<OptionSetValue>("epdt_usertype").Value;
                        }
                        list.Add(model);
                    }
                }
                res.data = JsonConvert.SerializeObject(list);

                //接口日志
                CommandHelper.CreateApiLog("获取最新政策", $"applicationID={applicationID}&policyID={policyID}", JsonConvert.SerializeObject(res), "获取最新政策", "成功", OrganizationServiceAdmin);
            }
            catch (Exception ex)
            {
                res.flag = "E";
                res.message = ex.Message;
                //接口日志
                CommandHelper.CreateApiLog("获取最新政策", $"applicationID={applicationID}&policyID={policyID}", JsonConvert.SerializeObject(res), "获取最新政策", "失败", OrganizationServiceAdmin);
            }
            return res;
        }

        /// <summary>
        /// 用户确认
        /// </summary>
        /// <param name="param">用户确认参数</param>
        /// <returns></returns>
        public ResModel Consent(ConsentModel param,string id)
        {
            var res = new ResModel() { message = "确认成功" };

            try
            {
                #region 校验参数
                if (param == null || param.type == 0 || string.IsNullOrWhiteSpace(param.applicationID) || string.IsNullOrWhiteSpace(id) || string.IsNullOrWhiteSpace(param.policyID))
                {
                    throw new Exception("请检查传入参数是否有效");
                }
                #endregion

                var qe = new QueryExpression("epdt_privacy_policy_terms");
                qe.ColumnSet.AllColumns = true;
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition("epdt_appid", ConditionOperator.Equal, param.applicationID);
                qe.Criteria.AddCondition("epdt_name", ConditionOperator.Equal, param.policyID);
                var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                if (ec == null || ec.Entities.Count == 0)
                {
                    throw new Exception($"【{param.applicationID}】政策条款不存在");
                }

                var entity = new Entity("onepdt_t_privacy_policy_auth_log");
                entity["onepdt_policy_term_id"] = ec.Entities[0].ToEntityReference();
                entity["onepdt_usertype"] = new OptionSetValue(param.type);
                entity["onepdt_version"] = ec.Entities[0].GetAttributeValue<string>("epdt_policy_term_version");
                entity["onepdt_appid"] = param.applicationID;
                entity["onepdt_userid"] = id;
                OrganizationServiceAdmin.Create(entity);

                //接口日志
                CommandHelper.CreateApiLog("用户确认", $"{JsonConvert.SerializeObject(param)}", JsonConvert.SerializeObject(res), "用户确认", "成功", OrganizationServiceAdmin);
            }
            catch (Exception ex)
            {
                res.flag = "E";
                res.message = ex.Message;
                //接口日志
                CommandHelper.CreateApiLog("用户确认", $"{JsonConvert.SerializeObject(param)}", JsonConvert.SerializeObject(res), "用户确认", "失败", OrganizationServiceAdmin);
            }
            return res;
        }

        /// <summary>
        /// 用户检查
        /// </summary>
        /// <param name="type">用户类别</param>
        /// <param name="applicationID">应用ID</param>
        /// <param name="id">用户id</param>
        /// <returns></returns>
        public ResModel CheckPolicy(int type, string applicationID, string id)
        {
            var res = new ResModel() { code = "200000", message = "当前用户政策已经是最新，无需更新" };

            try
            {
                #region 校验参数
                if (type == 0 || string.IsNullOrWhiteSpace(applicationID) || string.IsNullOrWhiteSpace(id))
                {
                    throw new Exception("请检查传入参数是否有效");
                }
                #endregion
                //获取最新政策
                var policys = GetNewPolicies(applicationID, type);

                var updatePolicyList = new List<string>();

                var qe = new QueryExpression("onepdt_t_privacy_policy_auth_log");
                qe.ColumnSet.AllColumns = true;
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition("onepdt_appid", ConditionOperator.Equal, applicationID);
                qe.Criteria.AddCondition("onepdt_userid", ConditionOperator.Equal, id);
                var link = new LinkEntity("onepdt_t_privacy_policy_auth_log", "epdt_privacy_policy_terms", "onepdt_policy_term_id", "epdt_privacy_policy_termsid", JoinOperator.Inner);
                link.Columns.AddColumn("epdt_name");
                qe.LinkEntities.Add(link);
                qe.AddOrder("createdon", OrderType.Descending);
                var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                if (ec != null && ec.Entities.Count > 0)
                {
                    foreach (var item in policys)
                    {
                        if (ec.Entities.All(consentLog => {
                        var policyID = consentLog.GetAttributeValue<EntityReference>("onepdt_policy_term_id").Name;
                            return consentLog.GetAttributeValue<string>("onepdt_version") != item.privacyVersion && id != item.policyID;
                        }))
                        {
                               res.code = "200003";
                            updatePolicyList.Add(item.policyID);
                        }

                    }
                }
                else
                {
                    res.code = "200004";
                    res.data = JsonConvert.SerializeObject(policys);
                    throw new Exception("当前用户暂无授权");
                }
                if (res.code == "200003")
                {
                    res.data = JsonConvert.SerializeObject(updatePolicyList);
                    throw new Exception("用户当前政策需要更新");

                }

                //接口日志
                CommandHelper.CreateApiLog("用户检查", $"type={type}&applicationID={applicationID}&id={id}", JsonConvert.SerializeObject(res), "用户检查", "成功", OrganizationServiceAdmin);
            }
            catch (Exception ex)
            {
                res.flag = "E";
                res.message = ex.Message;
                //接口日志
                CommandHelper.CreateApiLog("用户检查", $"type={type}&applicationID={applicationID}&id={id}", JsonConvert.SerializeObject(res), "用户检查", "失败", OrganizationServiceAdmin);
            }
            return res;
        }

        /// <summary>
        /// 用户授权撤销
        /// </summary>
        /// <param name="type">用户类别</param>
        /// <param name="applicationID">应用id</param>
        /// <param name="id">用户id</param>
        /// <param name="policyID">政策id</param>
        /// <returns></returns>
        public ResModel RevokePolicy(int type, string applicationID, string id, string policyID)
        {
            var res = new ResModel();

            try
            {
                #region 校验参数
                if (type == 0 || string.IsNullOrWhiteSpace(applicationID) || string.IsNullOrWhiteSpace(id) || string.IsNullOrWhiteSpace(policyID))
                {
                    throw new Exception("请检查传入参数是否有效");
                }
                #endregion

                var qe = new QueryExpression("onepdt_t_privacy_policy_auth_log");
                qe.ColumnSet.AllColumns = true;
                qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                qe.Criteria.AddCondition("onepdt_appid", ConditionOperator.Equal, applicationID);
                qe.Criteria.AddCondition("onepdt_userid", ConditionOperator.Equal, id);
                qe.Criteria.AddCondition("onepdt_usertype", ConditionOperator.Equal, type);
                qe.AddOrder("createdon", OrderType.Descending);
                var link = new LinkEntity("onepdt_t_privacy_policy_auth_log", "epdt_privacy_policy_terms", "onepdt_policy_term_id", "epdt_privacy_policy_termsid", JoinOperator.Inner);
                link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                link.LinkCriteria.AddCondition("epdt_name", ConditionOperator.Equal, policyID);
                link.EntityAlias = "a";
                qe.LinkEntities.Add(link);
                var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                if (ec != null && ec.Entities.Count > 0)
                {
                    foreach (var item in ec.Entities)
                    {
                        var update = new Entity(item.LogicalName, item.Id);
                        update["statecode"] = new OptionSetValue(1);
                        OrganizationServiceAdmin.Update(update);
                    }
                }
                else
                {
                    throw new Exception("不存在用户政策条款授权");
                }

                //接口日志
                CommandHelper.CreateApiLog("用户授权撤销", $"type={type}&applicationID={applicationID}&id={id}&policyID={policyID}", JsonConvert.SerializeObject(res), "用户授权撤销", "成功", OrganizationServiceAdmin);
            }
            catch (Exception ex)
            {
                res.flag = "E";
                res.message = ex.Message;
                //接口日志
                CommandHelper.CreateApiLog("用户授权撤销", $"type={type}&applicationID={applicationID}&id={id}&policyID={policyID}", JsonConvert.SerializeObject(res), "用户授权撤销", "失败", OrganizationServiceAdmin);
            }

            return res;
        }

        /// <summary>
        /// 获取最新政策
        /// </summary>
        /// <param name="applicationID">应用ID</param>
        /// <returns></returns>
        public List<PolicyModel> GetNewPolicies(string applicationID, int type)
        {
            if (string.IsNullOrWhiteSpace(applicationID))
            {
                throw new Exception("请传入应用ID");
            }

            var list = new List<PolicyModel>();

            var qe = new QueryExpression("epdt_privacy_policy_terms");
            qe.ColumnSet.AllColumns = true;
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("statuscode", ConditionOperator.Equal, 6);
            qe.Criteria.AddCondition("epdt_appid", ConditionOperator.Equal, applicationID);
            if (type > 0)
            {
                qe.Criteria.AddCondition("epdt_usertype", ConditionOperator.Equal, type);
            }
            qe.AddOrder("createdon", OrderType.Descending);
            var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
            if (ec != null && ec.Entities.Count > 0)
            {
                foreach (var item in ec.Entities)
                {
                    var model = new PolicyModel();
                    //政策id
                    if (item.Contains("epdt_name"))
                        model.policyID = item.GetAttributeValue<string>("epdt_name");
                    //条款类型
                    if (item.Contains("epdt_term_type"))
                    {
                        model.privacyType = item.FormattedValues["epdt_term_type"];
                        model.privacyTypeValue = item.GetAttributeValue<OptionSetValue>("epdt_term_type").Value;
                    }
                    //条款名称
                    if (item.Contains("epdt_policy_term_id"))
                        model.privacyName = item.GetAttributeValue<string>("epdt_policy_term_id");
                    //条款版本
                    if (item.Contains("epdt_policy_term_version"))
                        model.privacyVersion = item.GetAttributeValue<string>("epdt_policy_term_version");
                    //条款内容
                    if (item.Contains("epdt_policy_term_content"))
                        model.privacyContent = item.GetAttributeValue<string>("epdt_policy_term_content");
                    //条款用户类别
                    if (item.Contains("epdt_usertype"))
                    {
                        model.userType = item.FormattedValues["epdt_usertype"];
                        model.userTypeValue = item.GetAttributeValue<OptionSetValue>("epdt_usertype").Value;
                    }
                    list.Add(model);
                }
            }
            return list;
        }

    }
}
