﻿using System.Collections.Generic;

namespace Abt.Epdt.WebApis.Model
{
    public class UserInfo
    {
        public string username { get; set; }
        public string userid { get; set; }

        public string code { get; set; }

        public string useremail { get; set; }
        public bool isexitstodolist { get; set; } = true;
        public List<UserRoleModel> role { get; set; }
    }
    public class UserRoleModel
    {
        public string phone { get; set; }
        public int roleseq { get; set; }
        public string roleid { get; set; }
        public string rolename { get; set; }
        public bool istodo { get; set; }
        public bool isquery { get; set; }
        public bool ismy { get; set; }
        public bool isproxy { get; set; }
        public string proxyemail { get; set; } = "";
    }
}
