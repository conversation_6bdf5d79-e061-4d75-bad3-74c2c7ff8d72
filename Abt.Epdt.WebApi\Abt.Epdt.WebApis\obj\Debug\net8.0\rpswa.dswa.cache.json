{"GlobalPropertiesHash": "R6AL5nSa0oRbmMO9NEhMR1NnYYip0+8Netfdzu6Uqi8=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["qF502Ut+Q2RRjNELTdrFH6oKf2+k/fE21qiSJtcdCl0=", "RJloIYt81PEFn4pcEZwob7YZC7az9mzNMNt+0QckJNI=", "Nso2T7rEW8TqaoAdA5k4i6VKaPq7z9qgu6a/aJKhHKs=", "33Ic0tpKr6jDtsrxjqD8/qUPlGp0gpAOktFQuVHPE6M=", "duxr8W/fK95a95Jv81w/9o3lxuDsTeG2rKApCKOoeVQ=", "qoe7QSzc7lYGS32G2Z+T3vAxD0TxZyAi1s2IBQ00TBk=", "vwzts7ClcrTUNYpN2PnrJz67h9kB193w3lr+vW/Evns=", "rOnpoljD7DzGBuDN/+MnqFrjE44Ga57i9Mn7ec0dIHA=", "vBKZwS1KpwrZtw17KXGfjlARpqqyK9qwXsMStodfvLI=", "jCaVCIPrvrOcml6fLMX04C00D4sEdvRYAykD6kt9jsM=", "BGsDvkrh4HNDl37HFF6M/ogeudaqIyIhwbAWu8lYaAU=", "cQV4ghvtMOxdqby5WrmjA/+VT+pw+jSKgtc6/+H+OiM=", "wgjfkSnq/10rWGuQqqDDzVjDpW2p9y05zfDetVDjoZg=", "ceEpcpEZ2Xow4IgQHnCOSis0K0izP5ZOPce8lUaM8yI=", "DCchgNcKLtIhyD7j4YE1+Wxki6oebMeecBDglj/Kq54=", "rp8ofqasOCR2oMRgltio83znfiRMv4o3HAR5eHyIZwY=", "hzt9bvnFeZzX+TbOTr9V/cupn75xQDd1+6d/7K4dYDA=", "frkO2dBJXOiZvhmhRkHv3Qm934FHiDyO+klwErBLmB8=", "7NflGFdiR2FjQmCOhQft0qzysPf2TmJzyfX4WrndgHk=", "6ldCVqrG3hTUch1gLFwMKDNE8yjGin2VeWrhJQlRs6Y=", "mICwXedvBSbnpbqjgAxcX7sIg3JxsqvjlNf9q5hKVs0=", "89MYzT9amFUaNyEsbuBtSGLD1COqAWdG3N0awcWOyc4=", "Q40Fl98Ddtj4oo8zN5fshuBOt0r5oS9O/JXJm6gZtV0=", "ADh0DskKLYdoHDC6tsItDdpJZbC09g9F541bkpkVAGA=", "wn+eZvB+jvm2jRlIi34H0PH2qN4lXLvyZ+VB5degYqk=", "qQFuvus1uk21CzNbeJzHNMq1Xiy8qouJpd/Iofp/mHY=", "W7mwPjY+yPpEaBJvfiBmz8gs5iMYhtb6dqPBSgwX2mE=", "JQ5UHTYXpty7Q7Nj1ZJCA1r0qG+lgw7bOFhZBNI4390=", "UY2uhn458k3dYseT2PhfZezmtTjwQN+eafxMU/q7wv0=", "EUoCEMgqw5CYUgP3bDAo13l0Mqm614YMin+FH8vp/oY=", "cIOWt79nV2YXQHC/A2oahQoFy3EOLvN58iME6Ug5XvA=", "afF0nkgtMAC2cZiVBI1xfgmrpPyUoWrOrYi1cHFlYeE=", "ua6nz1sToCDcdx3EcbkgMbpcCa8DWKbdgBgvI2SIFms=", "11Sylc39dePRMyzjZM18lohrYVTYUB9pSdFTPsTyWgs=", "R+t5MXl7OhqVnH7Royilq1xB3PPl+7Z76T0nJwCIYe8=", "SFsqSyxWLjYHxIQ1Bf6j6pbojSok/JNGGkviqA/YgWs=", "KkBZmm9IQc2IPqBtHGM5kymraLY0OpgK75jKV0xqd6s=", "4/G8bINzk4FWhuY3ak+7EEwucd9NvqoaB53G7atAHT8=", "sxbJhZE7qGqFkPQDpGkW9o/tu7LpaOqK0eLGiPKpCBk=", "lU0pkXgjlA+9sZF6Sg1X5T0T101VwlCORLsGgxfUgZE=", "ob9SHRwgQNbgGW9PC0UrE6D+FHezF18z/StXAtGkceU=", "5LOIHxol2gnaliH2i8QsQvDpMw1rdWBUjFoXZIYZMwk=", "FqPwxW43RQYyC/OS+goZi9PRwu2MAvV58LhB/vIRhd4=", "yxAVjZ7zk7Sj2fez1Ny5P2hqAgZqpmzwM9Qbi2ngLuQ=", "5i6WQbDbISn0Js1zzJQwOTERl3IiL9eaJFx6urVSOc0=", "jme/3k6wMZybXXsm8d0/Ni/Z5PQmHi5fmfVHxWjSACs=", "FAxkH55kkPaNMzVTEz4yxLygDNwvRW7BmevPGw6kEno=", "qFrYvBC9v+FWz/WFODE1/hoJHfD/k6rGQr2Bp9U9WVo="], "CachedAssets": {"qF502Ut+Q2RRjNELTdrFH6oKf2+k/fE21qiSJtcdCl0=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\css\\site.css", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hdhjllpya4", "Integrity": "S2ihmzMFFc3FWmBWsR+NiddZWa8kbyaQYBx2FDkIoHs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 1417, "LastWriteTime": "2024-09-10T03:24:43.3546376+00:00"}, "RJloIYt81PEFn4pcEZwob7YZC7az9mzNMNt+0QckJNI=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\favicon.ico", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "90yqlj465b", "Integrity": "qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 32038, "LastWriteTime": "2024-09-10T03:24:43.3556671+00:00"}, "Nso2T7rEW8TqaoAdA5k4i6VKaPq7z9qgu6a/aJKhHKs=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\js\\site.js", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0n9f8i66x6", "Integrity": "dLGP40S79Xnx6GqUthRF6NWvjvhQ1nOvdVSwaNcgG18=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 230, "LastWriteTime": "2024-09-10T03:24:43.3566743+00:00"}, "33Ic0tpKr6jDtsrxjqD8/qUPlGp0gpAOktFQuVHPE6M=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9n0ta5ieki", "Integrity": "ezlzqfTqJZTjEDR18neKc2tUTZtWoz3oQ/ouWY9WmO8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 68266, "LastWriteTime": "2024-09-10T03:24:43.3580804+00:00"}, "duxr8W/fK95a95Jv81w/9o3lxuDsTeG2rKApCKOoeVQ=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i1dcxd824", "Integrity": "2MgjO0zpqYZscatQSGWJ9Io9U8EjvXk0iYygYz1Q+Ms=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 151749, "LastWriteTime": "2024-09-10T03:24:43.3613921+00:00"}, "qoe7QSzc7lYGS32G2Z+T3vAxD0TxZyAi1s2IBQ00TBk=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vxs71z90fw", "Integrity": "vdSFRAWr6LTognRmxyi6QlSO5O+MC+VGyMbziTrBmBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 48494, "LastWriteTime": "2024-09-10T03:24:43.3633911+00:00"}, "vwzts7ClcrTUNYpN2PnrJz67h9kB193w3lr+vW/Evns=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jzb7jyrjvs", "Integrity": "kZzlXLpTC0WvL0AL7nXf07BJ28WnY/1H/HrKgS68Q4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 108539, "LastWriteTime": "2024-09-10T03:24:43.3644472+00:00"}, "rOnpoljD7DzGBuDN/+MnqFrjE44Ga57i9Mn7ec0dIHA=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nynt4yc5xr", "Integrity": "cZWhU8ntBevnbhNEh3hunnIoi6dE1eUwX41sB6+bHmo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5227, "LastWriteTime": "2024-09-10T03:24:43.3654601+00:00"}, "vBKZwS1KpwrZtw17KXGfjlARpqqyK9qwXsMStodfvLI=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tgg2bl5mrw", "Integrity": "3e6awqXPijx918dzyzbbHKwaDBTsIQ8Us38QY30GRms=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 76483, "LastWriteTime": "2024-09-10T03:24:43.3664698+00:00"}, "jCaVCIPrvrOcml6fLMX04C00D4sEdvRYAykD6kt9jsM=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "brwg1hntyu", "Integrity": "1z7sRzwNyqpcTrqPd/iXuzoApqDdjpRbHwjAZ8QMhOk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4028, "LastWriteTime": "2024-09-10T03:24:43.3674846+00:00"}, "BGsDvkrh4HNDl37HFF6M/ogeudaqIyIhwbAWu8lYaAU=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gf2dxac9qe", "Integrity": "dIm3VZXztwbIlhOzVt+ggg5Dvhp28MJQGJoweOH9cAE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 32461, "LastWriteTime": "2024-09-10T03:24:43.3684995+00:00"}, "cQV4ghvtMOxdqby5WrmjA/+VT+pw+jSKgtc6/+H+OiM=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gawgt6fljy", "Integrity": "eEx7gvq+uEM0o4kUBiy/+Mxl6rHH9NQ9UzRBWHe9mXg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 202385, "LastWriteTime": "2024-09-10T03:24:43.3694987+00:00"}, "wgjfkSnq/10rWGuQqqDDzVjDpW2p9y05zfDetVDjoZg=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k9n1kkbua6", "Integrity": "CMAZj3JKoZUVYSxVPS/FeGatmYSvHkujo5oaZLNXx0o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 492048, "LastWriteTime": "2024-09-10T03:24:43.3704916+00:00"}, "ceEpcpEZ2Xow4IgQHnCOSis0K0izP5ZOPce8lUaM8yI=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "etnb7xlipe", "Integrity": "rldnE7wZYJj3Q43t5v8fg1ojKRwyt0Wtfm+224CacZs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 155764, "LastWriteTime": "2024-09-10T03:24:43.3704916+00:00"}, "DCchgNcKLtIhyD7j4YE1+Wxki6oebMeecBDglj/Kq54=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kao5znno1s", "Integrity": "xMZ0SaSBYZSHVjFdZTAT/IjRExRIxSriWcJLcA9nkj0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 625953, "LastWriteTime": "2024-09-10T03:24:43.3718413+00:00"}, "rp8ofqasOCR2oMRgltio83znfiRMv4o3HAR5eHyIZwY=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u0biprgly9", "Integrity": "srIwGYgANrjaabGVuC3G7O0jv1Xh3Kt7dIc3/P0Ebf0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 229924, "LastWriteTime": "2024-09-10T03:24:43.3743709+00:00"}, "hzt9bvnFeZzX+TbOTr9V/cupn75xQDd1+6d/7K4dYDA=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pxamm17y9e", "Integrity": "3UpdqvoTc6M2sug8WtFhr/m3tg+4zLMgoMgjqpn5n1I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 402249, "LastWriteTime": "2024-09-10T03:24:43.3778846+00:00"}, "frkO2dBJXOiZvhmhRkHv3Qm934FHiDyO+klwErBLmB8=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hju<PERSON><PERSON>ly30", "Integrity": "XZfkOGd6FuhF88h5GgEmRIpXbm+hBkFo74yYDPY5rbw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78641, "LastWriteTime": "2024-09-10T03:24:43.3788859+00:00"}, "7NflGFdiR2FjQmCOhQft0qzysPf2TmJzyfX4WrndgHk=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u9xms436mi", "Integrity": "8i3JQdKYQQcJzmbkwhwY+1XPe7Utf1LdBnYZCvNmKWc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 311949, "LastWriteTime": "2024-09-10T03:24:43.3812245+00:00"}, "6ldCVqrG3hTUch1gLFwMKDNE8yjGin2VeWrhJQlRs6Y=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "48vr37mrsy", "Integrity": "LKpkBN2w3iudGRseLItcNcaMpI8qlSEUC7+DsnwGNwA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 136072, "LastWriteTime": "2024-09-10T03:24:43.3824908+00:00"}, "mICwXedvBSbnpbqjgAxcX7sIg3JxsqvjlNf9q5hKVs0=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vaswmcjbo4", "Integrity": "R81NA2DWe8EPjZ2OUhieXYgvvXBnm78oMdeqOtSEr7c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 250568, "LastWriteTime": "2024-09-10T03:24:43.3835033+00:00"}, "89MYzT9amFUaNyEsbuBtSGLD1COqAWdG3N0awcWOyc4=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zu238p5lxg", "Integrity": "O82ALp93hJ58HpPIcnn7uwTUWUnSvnmwNWbOrN4psVg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 58078, "LastWriteTime": "2024-09-10T03:24:43.3835033+00:00"}, "Q40Fl98Ddtj4oo8zN5fshuBOt0r5oS9O/JXJm6gZtV0=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sgi57dik4g", "Integrity": "vMfBbEXmojM9AaHrIyKSo+20n5JM7KMyJkBCfL4pgL4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 190253, "LastWriteTime": "2024-09-10T03:24:43.3844978+00:00"}, "ADh0DskKLYdoHDC6tsItDdpJZbC09g9F541bkpkVAGA=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "weyt030wr8", "Integrity": "iMA3h2QDzhvwmVtohviPpq8VwRv3UMY/PEE+ao7bBq0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2024-09-10T03:24:43.3566743+00:00"}, "wn+eZvB+jvm2jRlIi34H0PH2qN4lXLvyZ+VB5degYqk=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dvc2bfcndg", "Integrity": "qbS02vMHZxdLNYKUtLPSYaSHXj1/ZwH1fv9f3XAY0LU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19798, "LastWriteTime": "2024-09-10T03:24:43.3866153+00:00"}, "qQFuvus1uk21CzNbeJzHNMq1Xiy8qouJpd/Iofp/mHY=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gb7ocvbhts", "Integrity": "9GycpJnliUjJDVDqP0UEu/bsm9U+3dnQUH8+3W10vkY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5871, "LastWriteTime": "2024-09-10T03:24:43.3866153+00:00"}, "W7mwPjY+yPpEaBJvfiBmz8gs5iMYhtb6dqPBSgwX2mE=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "aBc/n/nO9esvq0e80G57lEYLonvE5dKKUgoozaiVfLM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 587, "LastWriteTime": "2024-09-10T03:24:43.3854971+00:00"}, "JQ5UHTYXpty7Q7Nj1ZJCA1r0qG+lgw7bOFhZBNI4390=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wus95c49fh", "Integrity": "qLDpf9Urms7R6rnASrjHz38WdQfOvSOmTgLDfzQSzIQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 43184, "LastWriteTime": "2024-09-10T03:24:43.3890256+00:00"}, "UY2uhn458k3dYseT2PhfZezmtTjwQN+eafxMU/q7wv0=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sjab29p8z5", "Integrity": "2F/T6dcoSumcuA/fcU4W36VpSKPtq4nQf/0/vNFsC+w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 18467, "LastWriteTime": "2024-09-10T03:24:43.3902297+00:00"}, "EUoCEMgqw5CYUgP3bDAo13l0Mqm614YMin+FH8vp/oY=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d2pxujwhw3", "Integrity": "27gs04nyeNuL9zc/GLQLjdbZqhNGvH+xIYgnYVPIawE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 50276, "LastWriteTime": "2024-09-10T03:24:43.3912397+00:00"}, "cIOWt79nV2YXQHC/A2oahQoFy3EOLvN58iME6Ug5XvA=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4dm14o4hmc", "Integrity": "eItLFOyfQ4d/OGzEnGchi2ZMVF8EhGgzS0k7fSOPifQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 23264, "LastWriteTime": "2024-09-10T03:24:43.3931786+00:00"}, "afF0nkgtMAC2cZiVBI1xfgmrpPyUoWrOrYi1cHFlYeE=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2024-09-10T03:24:43.3878151+00:00"}, "ua6nz1sToCDcdx3EcbkgMbpcCa8DWKbdgBgvI2SIFms=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ukz1g2vv03", "Integrity": "oo12yYOwbYfrLG1t6v9+HU+vMvEnlKkr1eIcdUwG7Zs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 298502, "LastWriteTime": "2024-09-10T03:24:43.3968994+00:00"}, "11Sylc39dePRMyzjZM18lohrYVTYUB9pSdFTPsTyWgs=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rm8d4uw1xt", "Integrity": "82hEkGrSMJh3quMSG4f7FbngmAPLTDM63H4eNayS4Us=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89478, "LastWriteTime": "2024-09-10T03:24:43.3993688+00:00"}, "R+t5MXl7OhqVnH7Royilq1xB3PPl+7Z76T0nJwCIYe8=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dsw5v3fbc5", "Integrity": "PVB97BePQ5JED6qSU7EvSun5iseF5FinCKtqsv5X9uA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137974, "LastWriteTime": "2024-09-10T03:24:43.4007631+00:00"}, "SFsqSyxWLjYHxIQ1Bf6j6pbojSok/JNGGkviqA/YgWs=": {"Identity": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "Abt.Epdt.WebApis", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\wwwroot\\", "BasePath": "_content/Abt.Epdt.WebApis", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "afgyafcsqt", "Integrity": "WtKlDye/qV68IA2W1tHFTyXyc/HjQvQKBpIK1CEulFM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1641, "LastWriteTime": "2024-09-10T03:24:43.3948834+00:00"}}, "CachedCopyCandidates": {}}