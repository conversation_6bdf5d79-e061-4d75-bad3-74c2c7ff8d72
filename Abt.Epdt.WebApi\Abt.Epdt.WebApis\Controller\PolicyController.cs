﻿using Abt.Epdt.WebApis.Command;
using Abt.Epdt.WebApis.Model;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using System;
using System.Security.Claims;

namespace Abt.Epdt.WebApis.Controller
{
    [ApiController]
    [Route("api/policy")]
    public class PolicyController : ControllerBase
    {
        private readonly IMemoryCache _memoryCache;
        private readonly PolicyCommand com;

        public PolicyController(IMemoryCache memoryCache)
        {
            _memoryCache = memoryCache;
            com = new PolicyCommand(_memoryCache);
        }
        /// <summary>
        /// 获取最新政策
        /// </summary>
        /// <param name="applicationID">应用ID</param>
        /// <param name="policyID">政策ID</param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = "JwtBearer,OnepdtJwtBearer")]
        [HttpGet]
        [Route("GetPolicies")]
        public ResModel GetPolicies(string applicationID, [FromQuery] string[] policyID)
        {
            return com.GetPolicies(applicationID, policyID);
        }

        /// <summary>
        /// 用户确认
        /// </summary>
        /// <param name="param">用户确认参数</param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = "JwtBearer,OnepdtJwtBearer")]
        [HttpPost]
        [Route("Consent")]
        public ResModel Consent([FromBody] ConsentModel param)
        {
            return com.Consent(param,param.userID);
        }

        /// <summary>
        /// 用户检查
        /// </summary>
        /// <param name="type">用户类别</param>
        /// <param name="applicationID">应用ID</param>
        /// <param name="id">用户id</param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = "JwtBearer,OnepdtJwtBearer")]
        [HttpGet]
        [Route("CheckPolicy")]
        public ResModel CheckPolicy(int type, string applicationID, string id)
        {
            return com.CheckPolicy(type, applicationID, id);
        }

        /// <summary>
        /// 用户授权撤销
        /// </summary>
        /// <param name="type">用户类别</param>
        /// <param name="applicationID">应用id</param>
        /// <param name="userID">用户id</param>
        /// <param name="policyID">政策id</param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = "JwtBearer,OnepdtJwtBearer")]
        [HttpGet]
        [Route("RevokePolicy")]
        public ResModel RevokePolicy(int type, string applicationID, string userID, string policyID)
        {
            return com.RevokePolicy(type, applicationID, userID, policyID);
        }





        /// <summary>
        /// 用户确认
        /// </summary>
        /// <param name="param">用户确认参数</param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = "JwtBearer,OnepdtJwtBearer")]
        [HttpPost]
        [Route("ConsentByUser")]
        public ResModel ConsentByUser([FromBody] ConsentModel param)
        {
            UserInfo userInfo = JsonConvert.DeserializeObject<UserInfo>(User.FindFirst(ClaimTypes.UserData).Value);

            return com.Consent(param,userInfo.code);
        }

        /// <summary>
        /// 用户检查
        /// </summary>
        /// <param name="type">用户类别</param>
        /// <param name="applicationID">应用ID</param>
        /// <param name="id">用户id</param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = "JwtBearer,OnepdtJwtBearer")]
        [HttpGet]
        [Route("CheckPolicyByUser")]
        public ResModel CheckPolicyByUser(int type, string applicationID)
        {
            UserInfo userInfo = JsonConvert.DeserializeObject<UserInfo>(User.FindFirst(ClaimTypes.UserData).Value);

            return com.CheckPolicy(type, applicationID, userInfo.code);
        }

        /// <summary>
        /// 用户授权撤销
        /// </summary>
        /// <param name="type">用户类别</param>
        /// <param name="applicationID">应用id</param>
        /// <param name="userID">用户id</param>
        /// <param name="policyID">政策id</param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = "OnepdtJwtBearer")]
        [HttpGet]
        [Route("RevokePolicyByUser")]
        public ResModel RevokePolicyByUser(int type, string applicationID, string policyID)
        {
            UserInfo userInfo = JsonConvert.DeserializeObject<UserInfo>(User.FindFirst(ClaimTypes.UserData).Value);
            return com.RevokePolicy(type, applicationID, userInfo.code, policyID);
        }
    }
}
