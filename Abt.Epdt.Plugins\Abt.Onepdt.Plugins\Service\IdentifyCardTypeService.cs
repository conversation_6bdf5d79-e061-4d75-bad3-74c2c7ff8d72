﻿using Microsoft.Xrm.Sdk.Query;
using Microsoft.Xrm.Sdk;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Abbott.Onepdt.Plugins.Models;
using javax.xml.transform;

namespace Abbott.Onepdt.Plugins.Service
{
    public class IdentifyCardTypeService
    {

        public static List<CardTypeModel> GetCardTypeModels(IOrganizationService service, string operationId)
        {
            List<CardTypeModel> cardTypeModels = new List<CardTypeModel>();

            // 查询跟台记录的所有字段
            string fetchXml = $@"
            <fetch top='50'>
              <entity name='onepdt_t_operation_implant'>
                <attribute name='onepdt_name' />
                <attribute name='onepdt_is_scrapped' />
                <attribute name='onepdt_product_mdata' />
                <attribute name='onepdt_t_operation_implantid' />
                <filter>
                  <condition attribute='statecode' operator='eq' value='0' />
                  <condition attribute='onepdt_is_scrapped' operator='eq' value='0' />
                  <condition attribute='onepdt_operation' operator='eq' value='{operationId}' />
                </filter>
                <link-entity name='onepdt_t_product_mdata' from='onepdt_t_product_mdataid' to='onepdt_product_mdata' link-type='inner' alias='onepdt_product_mdata'>
                  <attribute name='onepdt_name' />
                  <attribute name='onepdt_type' />
                </link-entity>
                <link-entity name='onepdt_t_operation' from='onepdt_t_operationid' to='onepdt_operation' link-type='inner' alias='onepdt_t_operation'>
                  <attribute name='onepdt_name' />
                  <attribute name='onepdt_hospitalcode' />
                  <attribute name='onepdt_hospitalname' />
                  <attribute name='onepdt_hcp_text' />
                  <attribute name='onepdt_date' />
                  <attribute name='onepdt_gender' />
                  <attribute name='onepdt_patient' />
                  <attribute name='onepdt_primary_hcp' />
                  <attribute name='onepdt_secondary_hcp' />
                      <link-entity name='epdt_t_hospital_basic_mdata' from='epdt_hospital_code' to='onepdt_hospitalcode' link-type='inner' alias='onepdt_hospital_basic'>
                        <attribute name='epdt_t_hospital_basic_mdataid' />
                        <attribute name='onepdt_address' />
                        <attribute name='onepdt_tel' />
                        <attribute name='epdt_hospital_code' />
                        <attribute name='epdt_name' />
                        <attribute name='epdt_hospital_province' />
                        <attribute name='epdt_hospital_city' />
                        <attribute name='epdt_hospital_districtandcounty' />
                      </link-entity>
                </link-entity>
              </entity>
            </fetch>";

            EntityCollection results = service.RetrieveMultiple(new FetchExpression(fetchXml));

            if (results.Entities.Count > 0)
            {
                var filteredResults = results.Entities.Where(e =>
                    ((AliasedValue)e["onepdt_product_mdata.onepdt_type"]).Value.ToString() == "主机"
                ).ToList();

                // 查询可用的标准制卡类型
                QueryExpression standradCardConfigQuery = new QueryExpression("onepdt_t_standradcard_config")
                {
                    ColumnSet = new ColumnSet(true)
                };
                standradCardConfigQuery.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

                EntityCollection standradCardConfigEntites = service.RetrieveMultiple(standradCardConfigQuery);
                EntityCollection HospitalBranchMappings = GetHospitalBranchMappings(service);

                foreach (var entity in filteredResults)
                {

                    var implantingHospitalId = ((AliasedValue)entity["onepdt_hospital_basic.epdt_t_hospital_basic_mdataid"])?.Value.ToString();
                    var matchingHospital = HospitalBranchMappings.Entities.FirstOrDefault(h => h.GetAttributeValue<EntityReference>("onepdt_implant_hospitalid").Id.ToString() == implantingHospitalId);

                    var implantingHospitalName1 = entity.Contains("onepdt_hospital_basic.epdt_name") ? ((AliasedValue)entity["onepdt_hospital_basic.epdt_name"]).Value?.ToString() : null;
                    var implantingHospitalCode1 = entity.Contains("onepdt_hospital_basic.epdt_hospital_code") ? ((AliasedValue)entity["onepdt_hospital_basic.epdt_hospital_code"]).Value?.ToString() : null;
                    var hospitalAddress1 = entity.Contains("onepdt_hospital_basic.onepdt_address")
                        ? string.Join(" ", new[]
                        {
                                ((AliasedValue)entity["onepdt_hospital_basic.epdt_hospital_province"])?.Value,
                                ((AliasedValue)entity["onepdt_hospital_basic.epdt_hospital_city"])?.Value != ((AliasedValue)entity["onepdt_hospital_basic.epdt_hospital_province"])?.Value ? ((AliasedValue)entity["onepdt_hospital_basic.epdt_hospital_city"])?.Value : null,
                                ((AliasedValue)entity["onepdt_hospital_basic.epdt_hospital_districtandcounty"])?.Value,
                                ((AliasedValue)entity["onepdt_hospital_basic.onepdt_address"])?.Value
                        }.Where(v => v != null))
                        : null;
                    var hospitalPhone1 = entity.Contains("onepdt_hospital_basic.onepdt_tel") ? ((AliasedValue)entity["onepdt_hospital_basic.onepdt_tel"]).Value?.ToString() : null;
                    if (matchingHospital != null)
                    {
                        implantingHospitalName1 = ((AliasedValue)matchingHospital["onepdt_print_hospital.epdt_name"]).Value?.ToString();
                        implantingHospitalCode1 = ((AliasedValue)matchingHospital["onepdt_print_hospital.epdt_hospital_code"]).Value?.ToString();
                        hospitalAddress1 = matchingHospital.Contains("onepdt_print_hospital.onepdt_address")
                        ? string.Join(" ", new[]
                        {
                              ((AliasedValue)matchingHospital["onepdt_print_hospital.epdt_hospital_province"])?.Value,
                              ((AliasedValue)matchingHospital["onepdt_print_hospital.epdt_hospital_city"])?.Value != ((AliasedValue)matchingHospital["onepdt_print_hospital.epdt_hospital_province"])?.Value ? ((AliasedValue)matchingHospital["onepdt_print_hospital.epdt_hospital_city"])?.Value : null,
                              ((AliasedValue)matchingHospital["onepdt_print_hospital.epdt_hospital_districtandcounty"])?.Value,
                              ((AliasedValue)matchingHospital["onepdt_print_hospital.onepdt_address"])?.Value
                        }.Where(v => v != null))
                        : null;
                        hospitalPhone1 = entity.Contains("onepdt_hospital_basic.onepdt_tel") ? ((AliasedValue)matchingHospital["onepdt_print_hospital.onepdt_tel"]).Value?.ToString() : null;

                    }

                        CardTypeModel cardTypeModel = new CardTypeModel
                    {
                        onepdt_t_operation_implantid = entity.Id.ToString(),
                        Operationnumber = entity.Contains("onepdt_t_operation.onepdt_name") ? ((AliasedValue)entity["onepdt_t_operation.onepdt_name"]).Value.ToString() : string.Empty,
                        Name = entity.Contains("onepdt_t_operation.onepdt_patient") ? ((AliasedValue)entity["onepdt_t_operation.onepdt_patient"]).Value.ToString() : string.Empty,
                        Gender = entity.Contains("onepdt_t_operation.onepdt_gender") ? ((AliasedValue)entity["onepdt_t_operation.onepdt_gender"]).Value.ToString() : string.Empty,
                        ImpDate = ((DateTime)((AliasedValue)entity["onepdt_t_operation.onepdt_date"]).Value).ToString("yyyy-MM-dd"),
                        Physician1 = entity.Contains("onepdt_t_operation.onepdt_hcp_text") ? ((AliasedValue)entity["onepdt_t_operation.onepdt_hcp_text"]).Value.ToString() : string.Empty,
                        //entity.Contains("onepdt_t_operation.onepdt_primary_hcp") ? ((EntityReference)((AliasedValue)entity["onepdt_t_operation.onepdt_primary_hcp"]).Value).Name : string.Empty,
                        //Physician2 = entity.Contains("onepdt_t_operation.onepdt_secondary_hcp") ? ((EntityReference)((AliasedValue)entity["onepdt_t_operation.onepdt_secondary_hcp"]).Value).Name : string.Empty,
                        Hospital = implantingHospitalName1,
                        HospitalCode = implantingHospitalCode1,
                        HospitalAdd = hospitalAddress1,
                        HospitalTel = hospitalPhone1,
                        ModelNo = entity.Contains("onepdt_product_mdata.onepdt_name") ? ((AliasedValue)entity["onepdt_product_mdata.onepdt_name"]).Value.ToString() : string.Empty,
                        SN = entity.GetAttributeValue<string>("onepdt_name"),
                        WarrantyPeriod = "",
                        Manufacturer = "",
                        SelectedMode = "",
                        MainMode = "",
                        ScanIntensity = "",
                        isShowScan = "",
                        Accessories = new List<CardTypeModel.Accessory>()
                    };

                    var standradCardConfig = standradCardConfigEntites.Entities
                    .FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_name") == ((AliasedValue)entity["onepdt_product_mdata.onepdt_name"]).Value.ToString());

                    if (standradCardConfig != null)
                    {
                        cardTypeModel.WarrantyPeriod = standradCardConfig.GetAttributeValue<string>("onepdt_guarantee_period");
                        cardTypeModel.Manufacturer = standradCardConfig.GetAttributeValue<string>("onepdt_manufacturer");
                        cardTypeModel.SelectedMode = standradCardConfig.GetAttributeValue<string>("onepdt_selected_pacing_mode");
                        cardTypeModel.MainMode = standradCardConfig.GetAttributeValue<string>("onepdt_main_pacing_mode");
                        cardTypeModel.ScanIntensity = standradCardConfig.GetAttributeValue<string>("onepdt_scan_intensity");
                        cardTypeModel.isShowScan = standradCardConfig.GetAttributeValue<Boolean>("onepdt_display_scan_intensity").ToString();
                    }

                    var leadEntities = results.Entities.Where(e =>
                        ((AliasedValue)e["onepdt_product_mdata.onepdt_type"]).Value.ToString() == "电极"
                    ).ToList();

                    if (leadEntities.Count > 0)
                    {
                        foreach (var leadEntity in leadEntities)
                        {
                            CardTypeModel.Accessory accessory = new CardTypeModel.Accessory
                            {
                                ModelNo = ((AliasedValue)leadEntity["onepdt_product_mdata.onepdt_name"]).Value.ToString(),
                                SN = leadEntity.GetAttributeValue<string>("onepdt_name"),
                                ImpDate = cardTypeModel.ImpDate,
                                //Manufacturer = cardTypeModel.Manufacturer
                            };
                            cardTypeModel.Accessories.Add(accessory);
                        }
                    }

                    retrunMAT result =  IdentifyCardType(service, entity, results) ;

                    cardTypeModel.MAT = result.MAT;

                    cardTypeModel.CardType = result.CardType;

                    // 找到 MAT 对应卡的类型
                    QueryExpression cardtypeManagementQuery = new QueryExpression("onepdt_t_id_cardtype_management")
                    {
                        ColumnSet = new ColumnSet(true)
                    };
                    cardtypeManagementQuery.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    EntityCollection cardtypeManagementEntites = service.RetrieveMultiple(cardtypeManagementQuery);
                    if (cardTypeModel.MAT != "")
                    {
                        var cardTypeEntity = cardtypeManagementEntites.Entities.FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_name") == cardTypeModel.MAT) ?? throw new Exception($"该卡的MAT不存在！");

                        cardTypeModel.CardType = cardTypeEntity.GetAttributeValue<string>("onepdt_type");
                        cardTypeModels.Add(cardTypeModel);
                    }
                    else if (cardTypeModel.CardType == "A" || cardTypeModel.CardType == "B" || cardTypeModel.CardType == "C")
                    {
                        var cardTypeEntity = cardtypeManagementEntites.Entities.FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_type") == cardTypeModel.CardType
                        ) ?? throw new Exception($"卡类型{cardTypeModel.CardType}不存在！"); 
                        cardTypeModel.MAT = cardTypeEntity.GetAttributeValue<string>("onepdt_name");
                        cardTypeModels.Add(cardTypeModel);
                    }

                }
            }

            return cardTypeModels;
        }


        public static EntityCollection GetHospitalBranchMappings(IOrganizationService service)
        {
            QueryExpression hospitalBranchMappingQuery = new QueryExpression("onepdt_t_hospital_branch_mapping")
            {
                ColumnSet = new ColumnSet(
                    "onepdt_hospital_name",
                    "onepdt_hospital_sap",
                    "onepdt_implant_hospitalid",
                    "onepdt_name",
                    "onepdt_print_hospitalid",
                    "onepdt_sfe")
            };
            hospitalBranchMappingQuery.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            LinkEntity onepdtPrintHospital = hospitalBranchMappingQuery.AddLink(
                "epdt_t_hospital_basic_mdata",
                "onepdt_print_hospitalid",
                "epdt_t_hospital_basic_mdataid");
            onepdtPrintHospital.EntityAlias = "onepdt_print_hospital";
            onepdtPrintHospital.Columns.AddColumns(
                "epdt_hospital_city",
                "epdt_hospital_code",
                "epdt_hospital_districtandcounty",
                "epdt_hospital_province",
                "epdt_name",
                "onepdt_address",
                "onepdt_tel");
            onepdtPrintHospital.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            EntityCollection hospitalBranchMappings = service.RetrieveMultiple(hospitalBranchMappingQuery);
            return hospitalBranchMappings;
        }
        
        public static Dictionary<string, string> ProcessExtendedWarranty(IOrganizationService service, EntityCollection printCards)
        {
            Dictionary<string, string> warrantyDetails = new Dictionary<string, string>();

            string fetchXml = $@"
            <fetch>
              <entity name='epdt_t_device_mdata'>
                <attribute name='epdt_name' />
                <attribute name='epdt_implant_device_model' />
                <attribute name='epdt_guarantee_period' />
                <attribute name='epdt_implant_date' />
                <filter>
                  <condition attribute='epdt_name' operator='in'>
                    {string.Join("", printCards.Entities.Select(e => $"<value>{e.GetAttributeValue<string>("onepdt_name")}</value>"))}
                  </condition>
                </filter>
                <link-entity name='epdt_t_device_application' from='epdt_application_find_main_data' to='epdt_t_device_mdataid' link-type='matchfirstrowusingcrossapply' alias='epdt_t_device_application'>
                  <attribute name='createdon' />
                  <attribute name='epdt_register_complete_day' />
                  <attribute name='epdt_registration_interval_days' />
                  <filter>
                    <condition attribute='statecode' operator='eq' value='0' />
                  </filter>
                </link-entity>
              </entity>
            </fetch>";
            //识别卡主数据
            EntityCollection deviceMdatas = service.RetrieveMultiple(new FetchExpression(fetchXml));

            if (deviceMdatas.Entities.Count > 0)
            {
                QueryExpression extendedWarrantyConfigQuery = new QueryExpression("onepdt_t_extended_warranty_config");
                extendedWarrantyConfigQuery.ColumnSet.AllColumns = true;
                extendedWarrantyConfigQuery.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                EntityCollection extendedWarrantyConfigResult = service.RetrieveMultiple(extendedWarrantyConfigQuery);

                foreach (var result in deviceMdatas.Entities)
                {
                    var epdtNo = result.GetAttributeValue<string>("epdt_name");
                    var epdtImplantDeviceModel = result.GetAttributeValue<string>("epdt_implant_device_model");
                    var epdtGuaranteePeriod = result.GetAttributeValue<string>("epdt_guarantee_period");
                    DateTime? createdOn = result.Contains("epdt_t_device_application.createdon") ? ((AliasedValue)result["epdt_t_device_application.createdon"]).Value as DateTime? : null;
                    var epdtRegistrationIntervalDays = createdOn.HasValue ? (createdOn.Value - result.GetAttributeValue<DateTime>("epdt_implant_date")).Days : 0;

                    var extendedWarrantyConfig = extendedWarrantyConfigResult.Entities
                        .FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_name") == epdtImplantDeviceModel
                        && e.GetAttributeValue<int>("onepdt_min_interval_days") <= epdtRegistrationIntervalDays
                        && e.GetAttributeValue<int>("onepdt_max_interval_days") >= epdtRegistrationIntervalDays);

                    if (extendedWarrantyConfig != null)
                    {
                        var printCardEntity = printCards.Entities.FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_name") == epdtNo);
                        Entity patientIdCard = new Entity("onepdt_t_patient_id_card", printCardEntity.Id);
                        var guaranteeDate = epdtGuaranteePeriod + extendedWarrantyConfig.GetAttributeValue<string>("onepdt_time_expand");
                        patientIdCard["onepdt_main_unit1_warranty_period"] = guaranteeDate;
                        patientIdCard["onepdt_is_extended_warranty"] = true;
                        service.Update(patientIdCard);

                        Entity deviceMdata = new Entity("epdt_t_device_mdata", result.Id);
                        deviceMdata["epdt_guarantee_period"] = guaranteeDate;
                        service.Update(deviceMdata);
                        warrantyDetails.Add(epdtNo, guaranteeDate);
                    }
                }
            }

            return warrantyDetails;
        }

        public static string GetExtendedWarranty(IOrganizationService service, Guid applicationId , string implantDeviceModel ,string implantDate)
        {

            Entity deviceApplication = service.Retrieve("epdt_t_device_application", applicationId, new ColumnSet("createdon"));

            DateTime? createdOn = deviceApplication.GetAttributeValue<DateTime>("createdon");
            var implantDateTime = DateTime.ParseExact(implantDate, "yyyy-MM-dd", null);
            var epdtRegistrationIntervalDays = createdOn.HasValue ? (createdOn.Value - implantDateTime).Days : 0;

            QueryExpression extendedWarrantyConfigQuery = new QueryExpression("onepdt_t_extended_warranty_config");
            extendedWarrantyConfigQuery.ColumnSet.AllColumns = true;
            extendedWarrantyConfigQuery.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            EntityCollection extendedWarrantyConfigResult = service.RetrieveMultiple(extendedWarrantyConfigQuery);

            var extendedWarrantyConfig = extendedWarrantyConfigResult.Entities
                        .FirstOrDefault(e => e.GetAttributeValue<string>("onepdt_name") == implantDeviceModel
                        && e.GetAttributeValue<int>("onepdt_min_interval_days") <= epdtRegistrationIntervalDays
                        && e.GetAttributeValue<int>("onepdt_max_interval_days") >= epdtRegistrationIntervalDays);

            return extendedWarrantyConfig != null ? extendedWarrantyConfig.GetAttributeValue<string>("onepdt_time_expand") : "";
        }

        public static retrunMAT IdentifyCardType(IOrganizationService service, Entity implant, EntityCollection results)
        {
            //1.在 onepdt_t_standradcard_config 用产品型号mapping 对应的 记录 ，记录中的卡类型字段有值就直接返回值

            //2.没有值就去 onepdt_t_specify_hospital_config 用产品型号mapping 对应的 记录 ，mapping到记录的卡类型字段有值就直接返回值

            //3.没有值就去 onepdt_t_mri_management 用产品型号mapping 对应的 记录 ，没找到就返回 卡类型 A

            //4.找到就去检查 onepdt_t_operation_implant 的植入导线的数量如果 小于 onepdt_t_standradcard_config的最少导线数量 或 植入导线的产品型号 不在onepdt_t_mri_management 就返回 卡类型 A

            //5.全部不满足就 按照  1  记录中的是否显示扫描的 true就是 B false就是 C



            EntityReference model = implant.GetAttributeValue<EntityReference>("onepdt_product_mdata");
            QueryExpression standradCardConfigQuery = new QueryExpression("onepdt_t_standradcard_config")
            {
                ColumnSet = new ColumnSet(true)
            };
            standradCardConfigQuery.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            standradCardConfigQuery.Criteria.AddCondition("onepdt_name", ConditionOperator.Equal, model.Name);
            EntityCollection standradCardConfigEntites = service.RetrieveMultiple(standradCardConfigQuery);
            Entity standradCardConfigEntity = standradCardConfigEntites.Entities.FirstOrDefault();

            retrunMAT retrunMAT = new retrunMAT {
                CardType = "",
                MAT = ""
            };


            if (standradCardConfigEntity != null)
            {
                if (standradCardConfigEntity.Contains("onepdt_card_type"))
                {
                    retrunMAT.MAT = standradCardConfigEntity.GetAttributeValue<string>("onepdt_card_type");
                    return retrunMAT;
                }

                // Step 2: Check onepdt_t_specify_hospital_config
                QueryExpression querySpecifyHospital = new QueryExpression("onepdt_t_specify_hospital_config");
                querySpecifyHospital.ColumnSet = new ColumnSet("onepdt_card_type");
                querySpecifyHospital.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                querySpecifyHospital.Criteria.AddCondition("onepdt_name", ConditionOperator.Equal, implant.GetAttributeValue<EntityReference>("onepdt_product_mdata").Name);
                if (implant.Contains("onepdt_t_operation.onepdt_hospitalcode"))
                {
                    querySpecifyHospital.Criteria.AddCondition("onepdt_hospital_sfe", ConditionOperator.Equal, ((AliasedValue)implant["onepdt_t_operation.onepdt_hospitalcode"]).Value.ToString());
                }
                EntityCollection specifyHospitalResults = service.RetrieveMultiple(querySpecifyHospital);
                if (specifyHospitalResults.Entities.Count > 0 && specifyHospitalResults.Entities[0].Contains("onepdt_card_type"))
                {
                    retrunMAT.MAT = specifyHospitalResults.Entities[0].GetAttributeValue<string>("onepdt_card_type");
                    return retrunMAT;
                }

                // Step 3 & 4: Check onepdt_t_mri_management and lead conditions
                QueryExpression queryMriManagement = new QueryExpression("onepdt_t_mri_management");
                queryMriManagement.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                queryMriManagement.ColumnSet = new ColumnSet("onepdt_name");
                EntityCollection mriManagementResults = service.RetrieveMultiple(queryMriManagement);

                //主机的mri数量
                var MRIEntities = mriManagementResults.Entities.Where(e => e.GetAttributeValue<string>("onepdt_name") == implant.GetAttributeValue<EntityReference>("onepdt_product_mdata").Name
                ).ToList();


                if (MRIEntities.Count == 0)
                {
                    retrunMAT.CardType = "A";
                    return retrunMAT;
                }

                // Check lead conditions
                int leadCount = 0;
                var leadEntities = results.Entities.Where(e =>
                ((AliasedValue)e["onepdt_product_mdata.onepdt_type"]).Value.ToString() == "电极"
                ).ToList();
                leadCount = leadEntities.Count;

                int minLeadCount = 0;
                minLeadCount = standradCardConfigEntity.GetAttributeValue<int>("onepdt_mini_wires");




                bool allLeadsInMriManagement = leadEntities.All(e =>
                    mriManagementResults.Entities.Any(m =>
                        m.GetAttributeValue<string>("onepdt_name") == ((EntityReference)e["onepdt_product_mdata"]).Name
                    )
                );



                if (leadCount < minLeadCount || !allLeadsInMriManagement)
                {
                    retrunMAT.CardType = "A";
                    return retrunMAT;
                }

                // Step 5: Determine B or C based on onepdt_t_standradcard_config
                if (standradCardConfigEntity != null)
                {
                    bool isShowScan = standradCardConfigEntity.GetAttributeValue<bool>("onepdt_display_scan_intensity");
                    retrunMAT.CardType = isShowScan ? "B" : "C";
                    return retrunMAT;
                }

            }

            return  retrunMAT;

        }
    }
}
