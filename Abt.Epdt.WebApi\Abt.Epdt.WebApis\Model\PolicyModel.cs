﻿namespace Abt.Epdt.WebApis.Model
{
    public class PolicyModel
    {
        /// <summary>
        /// 政策id 
        /// </summary>
        public string policyID { get; set; } = "";
        /// <summary>
        /// 条款名称 
        /// </summary>
        public string privacyName { get; set; } = "";
        /// <summary>
        /// 版本号 
        /// </summary>
        public string privacyVersion { get; set; } = "";
        /// <summary>
        /// 条款类型 
        /// </summary>
        public string privacyType { get; set; } = "";
        /// <summary>
        /// 条款类型 
        /// </summary>
        public int privacyTypeValue { get; set; }
        /// <summary>
        /// 条款内容
        /// </summary>
        public string privacyContent { get; set; } = "";
        /// <summary>
        /// 用户类型
        /// </summary>
        public string userType { get; set; } = "";
        /// <summary>
        /// 用户类别
        /// </summary>
        public int userTypeValue { get; set; }
    }

    public class ConsentModel
    {
        /// <summary>
        /// 用户类别
        /// </summary>
        public int type { get; set; }
        /// <summary>
        /// 应用ID
        /// </summary>
        public string applicationID { get; set; }
        /// <summary>
        /// 用户id
        /// </summary>
        public string userID { get; set; }
        /// <summary>
        /// 政策ID
        /// </summary>
        public string policyID { get; set; }
    }
}
