﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abt.Epdt.Plugins
{


    /// <summary>
    /// Abt.Epdt.Plugins.SystemUserRolesAssociate: AssignUserRoles of role Entity - PostOperation
    /// Abt.Epdt.Plugins.SystemUserRolesAssociate: Associate of any Entity - PostOperation
    /// Abt.Epdt.Plugins.SystemUserRolesAssociate: Disassociate of any Entity - PostOperation
    /// </summary>

    public class SystemUserRolesAssociate : IPlugin
    {
        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService service = serviceFactory.CreateOrganizationService(null); // 权限
            var tracing = (ITracingService)serviceProvider.GetService(typeof(ITracingService));


            if (context.MessageName == "Associate" || context.MessageName == "Disassociate" || context.MessageName == "AssignUserRole")
            {

                var target = (EntityReference)context.InputParameters["Target"];

                if (target.LogicalName.ToUpper() != "SYSTEMUSER") return;
                try
                {
                    string sql = $@"<fetch xmlns:generator='MarkMpn.SQL4CDS' >
    <entity name='role' >
      <attribute name='name' />
      <link-entity name='systemuserroles' to='roleid' from='roleid' alias='systemuserroles' link-type='inner' >
        <link-entity name='systemuser' to='systemuserid' from='systemuserid' alias='systemuser' link-type='inner' >
            <attribute name='systemuserid' />
            <attribute name='domainname' />
            <attribute name='fullname' />
        </link-entity>
      </link-entity>
      <filter>
        <condition attribute='systemuserid' entityname='systemuser' operator='eq' value='{target.Id}' />
      </filter>
    </entity>
  </fetch>";

                    var entitylist = service.RetrieveMultiple(new FetchExpression(sql));

                    userroles userlist = new userroles();
                    foreach (var item in entitylist.Entities)
                    {
                        var userid = item.GetAttributeValue<Microsoft.Xrm.Sdk.AliasedValue>("systemuser.systemuserid").Value.ToString();
                        var email = item.GetAttributeValue<Microsoft.Xrm.Sdk.AliasedValue>("systemuser.domainname").Value.ToString();
                        var username = item.GetAttributeValue<Microsoft.Xrm.Sdk.AliasedValue>("systemuser.fullname").Value.ToString();
                        var role = item.GetAttributeValue<string>("name");

                        userlist.add(userid, email, username, role);                    
                    }

                    if (!userlist.roles.Any())
                    {
                        string sql2 = $@"<fetch xmlns:generator='MarkMpn.SQL4CDS' >
    <entity name='systemuser' >
      <attribute name='systemuserid' />   
<attribute name='domainname' /> 
<attribute name='fullname' /> 
      <filter>
        <condition attribute='systemuserid' operator='eq' value='{target.Id}' />
      </filter>
    </entity>
  </fetch>";
                        var entitylist2 = service.RetrieveMultiple(new FetchExpression(sql2));
                        foreach (var item in entitylist2.Entities)
                        {
                            var userid = item.Id.ToString();
                            var email = item.GetAttributeValue<string>("domainname").ToString();
                            var username = item.GetAttributeValue<string>("fullname").ToString();
                            var role = string.Empty;
                            userlist.add(userid, email, username, role);
                        }
                    }



                    if(userlist!=null && !string.IsNullOrWhiteSpace(userlist.email))
                    {
                        //判断用户是否存在这个表
                        string sql2 = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
  <entity name='epdt_t_role_management'>
    <attribute name='epdt_t_role_managementid' />
    <attribute name='epdt_name' />
    <attribute name='createdon' />
    <attribute name='epdt_email' />
    <attribute name='epdt_role_permissions' />
    <attribute name='epdt_role' />
    <attribute name='epdt_user_name' />
    <attribute name='epdt_user_code' />
    <attribute name='epdt_linkeduser' />
    <order attribute='epdt_name' descending='false' />
    <filter type='and'>
      <condition attribute='epdt_email' operator='eq' value='{userlist.email}' />
    </filter>
  </entity>
</fetch>";

                        var rolemanagelist = service.RetrieveMultiple(new FetchExpression(sql2));
                        var trackingquery = new Entity("epdt_t_role_management");
                        trackingquery["epdt_name"] = "用户系统角色分配";
                        trackingquery["epdt_user_code"] = userlist.email;
                        trackingquery["epdt_user_name"] = userlist.username;
                        trackingquery["epdt_role"] = string.Join(",", userlist.roles);
                        trackingquery["epdt_role_permissions"] = string.Join(",", userlist.roles);
                        trackingquery["epdt_email"] = userlist.email;
                        trackingquery["epdt_linkeduser"] = new EntityReference("systemuser", new Guid(userlist.userid));

                        if (rolemanagelist.Entities.Count > 0)
                        {
                            var first = rolemanagelist.Entities.FirstOrDefault();
                            trackingquery.Id = first.Id;
                            service.Update(trackingquery);
                        }
                        else
                        {
                            service.Create(trackingquery);
                        }
                    }
                }
                catch (Exception ex)
                {
                    tracing.Trace($"记录用户角色分配日志出错：{ex.Message}", ex.StackTrace);
                }
            }
        }
    }

    public class userroles
    {
        public string userid { get; set; }
        public string email { get; set; }
        public string username { get; set; }
        public List<string> roles { get; set; }

        public userroles()
        {
            roles = new List<string>();
        }

        public void add(string _userid,string _email, string _username, string _role)
        {
            userid = _userid;
            email = _email;
            username = _username;
            if (!roles.Contains(_role))
            {
                roles.Add(_role);
            }
        }
    }
}
