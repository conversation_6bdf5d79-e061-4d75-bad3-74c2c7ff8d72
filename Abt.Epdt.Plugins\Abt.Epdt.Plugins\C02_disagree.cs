﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abt.Epdt.Plugins
{
    public class C02_disagree : IPlugin
    {
        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService service = serviceFactory.CreateOrganizationService(null); // 权限

            ITracingService tracingService = (ITracingService)serviceProvider.GetService(typeof(ITracingService));


            try
            {
                var openid = (string)context.InputParameters["openid"];
                var PolicyInfolisttxt = (string)context.InputParameters["PolicyInfo"];

                var PolicyInfolist = JsonConvert.DeserializeObject<List<C02_PolicyInfo>>(PolicyInfolisttxt);
                tracingService.Trace("创建日志开始");

                string sql1 = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
  <entity name='epdt_t_wechat_registrant'>
    <attribute name='epdt_t_wechat_registrantid' />
    <attribute name='epdt_name' />
    <attribute name='createdon' />
    <attribute name='epdt_policy_term_id' />
    <attribute name='epdt_informed_consent_id' />
    <attribute name='epdt_openid' />
    <order attribute='epdt_name' descending='false' />
    <filter type='and'>
      <condition attribute='epdt_openid' operator='eq' value='{openid}' />
      <condition attribute='statecode' operator='eq' value='0' />
    </filter>
  </entity>
</fetch>";
                var register = service.RetrieveMultiple(new FetchExpression(sql1));

                foreach (var registeritem in register.Entities)
                {
                    Entity register_db = new Entity("epdt_t_wechat_registrant");
                    register_db.Id = registeritem.Id;
                    register_db.Attributes["statecode"] = new OptionSetValue(1);
                    register_db.Attributes["epdt_privacy_auth_status"] = "未授权";
                    register_db.Attributes["epdt_if_revoke_auth"] = "是";

                    var policy1 = registeritem.GetAttributeValue<EntityReference>("epdt_policy_term_id");

                    var policy2 = registeritem.GetAttributeValue<EntityReference>("epdt_informed_consent_id");


                    service.Update(register_db);
                    tracingService.Trace("解除申请人授权");

                    foreach (var policyitem in PolicyInfolist)
                    {
                        Entity log1 = new Entity("epdt_privacy_policy_auth_log");
                        log1.Attributes["epdt_privacy_auth_status1"] = "未授权";
                        log1.Attributes["epdt_privacy_auth_page1"] = new OptionSetValue(int.Parse(policyitem.privacy_auth_page1));
                        log1.Attributes["epdt_policy_term_id"] = new EntityReference("epdt_privacy_policy_terms", policy1.Id);
                        log1.Attributes["epdt_registereduser_abbottid"] = new EntityReference("epdt_t_wechat_registrant", register_db.Id);
                        log1.Attributes["epdt_operation_date"] = DateTime.Now.ToUniversalTime();

                        service.Create(log1);
                        tracingService.Trace("记录未授权日志1");

                        Entity log2 = new Entity("epdt_privacy_policy_auth_log");
                        log2.Attributes["epdt_privacy_auth_status1"] = "未授权";
                        log2.Attributes["epdt_privacy_auth_page1"] = new OptionSetValue(int.Parse(policyitem.privacy_auth_page1));
                        log2.Attributes["epdt_policy_term_id"] = new EntityReference("epdt_privacy_policy_terms", policy2.Id);
                        log2.Attributes["epdt_registereduser_abbottid"] = new EntityReference("epdt_t_wechat_registrant", register_db.Id);
                        log2.Attributes["epdt_operation_date"] = DateTime.Now.ToUniversalTime();

                        service.Create(log2);
                        tracingService.Trace("记录未授权日志2");
                    }

                    string sql2 = $@"<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
  <entity name='epdt_t_registrant_patient'>
    <attribute name='epdt_t_registrant_patientid' />
    <attribute name='epdt_patient_abbottid' />
    <filter type='and'>
      <condition attribute='epdt_registereduser_abbottid' operator='eq' value = '{register_db.Id}' />
    </filter>
  </entity>
</fetch>";

                    EntityCollection registrant_patientlist = service.RetrieveMultiple(new FetchExpression(sql2));

                    foreach (var item in registrant_patientlist.Entities)
                    {
                        var relation = new Entity("epdt_t_registrant_patient") { Id = item.Id };
                        relation.Attributes["statecode"] = new OptionSetValue(1);
                        service.Update(relation);
                        tracingService.Trace("停用申请人和患者关系");

                        var linkpatient = item.GetAttributeValue<EntityReference>("epdt_patient_abbottid");

                        //判断患者是授权患者还是注册患者
                        String fetchxml = $@"<fetch xmlns:generator='MarkMpn.SQL4CDS'>
                                                          <entity name='epdt_t_registrant_patient'>
                                                            <attribute name='epdt_t_registrant_patientid' />
                                                            <order attribute='createdon' descending='false'/>
                                                            <filter>
                                                                <condition attribute='epdt_patient_abbottid' operator='eq' value='{linkpatient.Id.ToString()}' />
                                                            </filter>
                                                          </entity>
                                                        </fetch>";
                        var ec = service.RetrieveMultiple(new FetchExpression(fetchxml));
                        if (ec != null && ec.Entities.Count > 0)
                        {
                            //自己注册的
                            if (item.Id.ToString() == ec.Entities[0].Id.ToString())
                            {
                                var patient = new Entity("epdt_t_patient") { Id = linkpatient.Id };
                                patient.Attributes["statecode"] = new OptionSetValue(1);
                                patient.Attributes["epdt_if_delete_patient"] = "是";
                                service.Update(patient);
                                tracingService.Trace("停用关联患者");


                                string sql3 = $@"<fetch xmlns:generator='MarkMpn.SQL4CDS'>
                                  <entity name='epdt_t_device_application'>
                                    <attribute name='epdt_t_device_applicationid' />
                                    <filter>
                                      <condition attribute='epdt_patient_abbottid' operator='eq' value='{linkpatient.Id}' />
                                    </filter>
                                  </entity>
                                </fetch>";

                                EntityCollection applicationlist = service.RetrieveMultiple(new FetchExpression(sql3));
                                foreach (var applicationitem in applicationlist.Entities)
                                {
                                    applicationitem.Attributes["statecode"] = new OptionSetValue(1);
                                    applicationitem.Attributes["epdt_if_delete_device_application"] = true;
                                    service.Update(applicationitem);
                                    tracingService.Trace("停用申请单");
                                }
                            }
                        }
                    }


                    Entity log = new Entity("epdt_t_wechat_user_action_log");
                    log.Attributes["epdt_openid"] = new EntityReference("epdt_t_wechat_registrant", register_db.Id);
                    log.Attributes["epdt_name"] = "解除协议授权";
                    log.Attributes["epdt_operation"] = "解除协议授权";
                    log.Attributes["epdt_operation_time"] = DateTime.Now.ToUniversalTime();
                    service.Create(log);
                }

                tracingService.Trace("创建日志结束");

                context.OutputParameters["result"] = "ok";
            }
            catch (Exception ex)
            {
                tracingService.Trace(ex.Message + ex.StackTrace + ex.StackTrace);
                throw;
            }
        }
    }
}
