﻿using Abt.Epdt.WebApis.Model;
using Abt.Epdt.WebApis.Util;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Abt.Epdt.WebApis.Command
{
    public class HcpCommand : OrgService
    {
        public HcpCommand(IMemoryCache memoryCache) : base(memoryCache)
        {
        }

        /// <summary>
        /// 获取医生主数据
        /// </summary>
        /// <param name="startmodifiedon">开始修改时间</param>
        /// <param name="endmodifiedon">结束修改时间</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ResModel GetHcps(string startmodifiedon, string endmodifiedon)
        {
            var res = new ResModel();
            try
            {
                if (!string.IsNullOrWhiteSpace(startmodifiedon) && !DateTime.TryParse(startmodifiedon, out DateTime start))
                {
                    throw new OnePDTException("【startmodifiedon】格式错误;例：yyyy-MM-dd HH:mm", "200003");

                }
                if (!string.IsNullOrWhiteSpace(endmodifiedon) && !DateTime.TryParse(endmodifiedon, out DateTime end))
                {
                    throw new OnePDTException("【endmodifiedon】格式错误;例：yyyy-MM-dd HH:mm", "200003");
                }
                //医生结果集
                var list = new List<HcpModel>();
                //设置分页循环查询
                var pageindex = 0;
                //是否有更多数据
                var hasmore = true;

                while (hasmore)
                {
                    pageindex++;
                    //查询医生主数据
                    var qe = new QueryExpression("onepdt_t_hcp_basic_mdata");
                    qe.ColumnSet.AllColumns = true;
                    //qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
                    if (!string.IsNullOrWhiteSpace(startmodifiedon))
                    {
                        qe.Criteria.AddCondition("modifiedon", ConditionOperator.OnOrAfter, startmodifiedon);
                    }
                    if (!string.IsNullOrWhiteSpace(endmodifiedon))
                    {
                        qe.Criteria.AddCondition("modifiedon", ConditionOperator.OnOrBefore, endmodifiedon);
                    }
                    qe.PageInfo = new PagingInfo()
                    {
                        PageNumber = pageindex,
                        Count = 5000,
                        ReturnTotalRecordCount = true
                    };
                    var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
                    if (ec != null && ec.Entities.Count > 0)
                    {
                        hasmore = ec.MoreRecords;

                        foreach (var item in ec.Entities)
                        {
                            var model = new HcpModel();
                            //数据标识
                            if (item.GetAttributeValue<OptionSetValue>("statecode").Value == 1)
                            {
                                model.sign = "D";
                            }
                            else
                            {
                                var modifiedon = item.GetAttributeValue<DateTime>("modifiedon").AddHours(8).ToString("yyyy-MM-dd HH:mm");
                                var createdon = item.GetAttributeValue<DateTime>("createdon").AddHours(8).ToString("yyyy-MM-dd HH:mm");
                                if (modifiedon == createdon)
                                {
                                    model.sign = "I";
                                }
                                else
                                {
                                    model.sign = "U";
                                }
                            }
                            //医生姓名
                            if (item.Contains("onepdt_name"))
                                model.name = item.GetAttributeValue<string>("onepdt_name");
                            //医生性别
                            if (item.Contains("onepdt_gender"))
                                model.gender = item.GetAttributeValue<string>("onepdt_gender");
                            //医生编码
                            if (item.Contains("onepdt_code"))
                                model.code = item.GetAttributeValue<string>("onepdt_code");
                            //医生称呼
                            if (item.Contains("onepdt_title"))
                                model.title = item.GetAttributeValue<string>("onepdt_title");
                            //医生分类
                            if (item.Contains("onepdt_classification"))
                                model.classification = item.GetAttributeValue<string>("onepdt_classification");
                            //电生理手术量
                            if (item.Contains("onepdt_electrophysiology_num"))
                                model.electrophysiologynum = item.GetAttributeValue<int>("onepdt_electrophysiology_num");
                            //冠脉手术量
                            if (item.Contains("onepdt_coronary_num"))
                                model.coronarynum = item.GetAttributeValue<int>("onepdt_coronary_num");
                            //结构性心脏病手术量
                            if (item.Contains("onepdt_structural_heartdisease_num"))
                                model.heartdiseasenum = item.GetAttributeValue<int>("onepdt_structural_heartdisease_num");
                            //科室
                            if (item.Contains("onepdt_department"))
                                model.department = item.GetAttributeValue<EntityReference>("onepdt_department").Name;
                            //客户类型
                            if (item.Contains("onepdt_customer_type"))
                                model.customertype = item.GetAttributeValue<EntityReference>("onepdt_customer_type").Name;
                            //使用偏好
                            if (item.Contains("onepdt_preference"))
                                model.preference = item.GetAttributeValue<string>("onepdt_preference");
                            //是否VIP
                            if (item.Contains("onepdt_is_vip"))
                                model.isvip = item.GetAttributeValue<bool>("onepdt_is_vip");
                            //为雅培讲课次数
                            if (item.Contains("onepdt_abbott_lecture_count"))
                                model.abbottlecturecount = item.GetAttributeValue<string>("onepdt_abbott_lecture_count");
                            //行政职务
                            if (item.Contains("onepdt_administrative"))
                                model.administrative = item.GetAttributeValue<string>("onepdt_administrative");
                            //学会职务
                            if (item.Contains("onepdt_institution_position"))
                                model.institutionposition = item.GetAttributeValue<string>("onepdt_institution_position");
                            //学术职称
                            if (item.Contains("onepdt_academic_title"))
                                model.academictitle = item.GetAttributeValue<string>("onepdt_academic_title");
                            //雅培份额排名
                            if (item.Contains("onepdt_abbottrank"))
                                model.abbottrank = item.GetAttributeValue<string>("onepdt_abbottrank");
                            //医生执业证书编号
                            if (item.Contains("onepdt_hcp_code"))
                                model.hcpcode = item.GetAttributeValue<string>("onepdt_hcp_code");
                            //主要执业机构编码
                            if (item.Contains("onepdt_sfe_hospital_id"))
                                model.sfehospitalid = item.GetAttributeValue<string>("onepdt_sfe_hospital_id");
                            //主要执业机构名称
                            if (item.Contains("onepdt_sfe_hospital_name"))
                                model.sfehospitalname = item.GetAttributeValue<string>("onepdt_sfe_hospital_name");
                            //HV医生分类
                            if (item.Contains("onepdt_hv_classification"))
                                model.hvclassification = item.GetAttributeValue<string>("onepdt_hv_classification");
                            //申请人
                            if (item.Contains("onepdt_application"))
                                model.application = item.GetAttributeValue<string>("onepdt_application");
                            //修改时间
                            model.modifiedon = item.GetAttributeValue<DateTime>("modifiedon").AddHours(8).ToString("yyyy-MM-dd HH:mm");
                            list.Add(model);
                        }
                    }
                    else
                    {
                        hasmore = false;
                    }
                }
                res.data = JsonConvert.SerializeObject(list);

                //接口日志
                CommandHelper.CreateApiLog("获取医生主数据", $"startmodifiedon={startmodifiedon}&endmodifiedon={endmodifiedon}", JsonConvert.SerializeObject(res), "获取医生主数据", "成功", OrganizationServiceAdmin);
            }
            catch (OnePDTException ex) {
                res.flag = "E";
                res.code = ex.ErrorCode;

                res.message = ex.Message;
                //接口日志
                CommandHelper.CreateApiLog("获取医生主数据", $"startmodifiedon={startmodifiedon}&endmodifiedon={endmodifiedon}", JsonConvert.SerializeObject(res), "获取医生主数据", "失败", OrganizationServiceAdmin);
            }
            catch (Exception ex)
            {
                res.flag = "E";

                res.message = ex.Message;
                //接口日志
                CommandHelper.CreateApiLog("获取医生主数据", $"startmodifiedon={startmodifiedon}&endmodifiedon={endmodifiedon}", JsonConvert.SerializeObject(res), "获取医生主数据", "失败", OrganizationServiceAdmin);
            }
            return res;
        }
    }
}
