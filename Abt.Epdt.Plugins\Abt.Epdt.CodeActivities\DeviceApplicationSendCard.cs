﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Microsoft.Xrm.Sdk.Workflow;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Activities;
using System.Activities.Statements;
using System.Collections.Generic;
using System.IdentityModel.Metadata;
using System.Linq;
using System.Security.Cryptography;

namespace Abt.Epdt.CodeActivities
{
    /// <summary>
    /// 电子保卡批量发卡操作
    /// </summary>
    public class DeviceApplicationSendCard : CodeActivity
    {
        [Input("DeviceApplicationIds")]
        public InArgument<string> DeviceApplicationIdsArgument { get; set; }

        [Output("Result")]
        public OutArgument<string> ResultArgument { get; set; }

        protected override void Execute(CodeActivityContext context)
        {
            ITracingService tracingService = context.GetExtension<ITracingService>();
            IWorkflowContext workflowExecutionContext = context.GetExtension<IWorkflowContext>();
            IOrganizationServiceFactory factory = context.GetExtension<IOrganizationServiceFactory>();
            IOrganizationService orgSvc = factory.CreateOrganizationService(workflowExecutionContext.UserId);

            string ids = DeviceApplicationIdsArgument.Get<string>(context);
            var idArr = ids.ToLower().Split(',');

            var sendFailedCard = ""; //记录发卡失败保卡号
            var sendFailedExeFailedCard = ""; //记录更新报错保卡号及报错原因
            var sendSuccCard = ""; //记录发放成功保卡号
            var sendSuccExeFailedCard = ""; //记录发放更新报错保卡号及报错原因
            var invalidExeFailedCard = ""; //记录失效更新报错保卡号及错误原因

            //查询选中的【EPDT电子植入设备识别卡发放申请】数据
            var qe = new QueryExpression("epdt_t_device_application")
            {
                ColumnSet = new ColumnSet("epdt_t_device_applicationid", "epdt_patient_abbottid", "epdt_name", "epdt_implant_date", "epdt_application_find_main_data")
            };
            qe.Criteria.AddCondition("epdt_check_status", ConditionOperator.Equal, "匹配成功");
            qe.Criteria.AddCondition("epdt_if_delete_device_application", ConditionOperator.Equal, false); //是否删除设备申请为否
            qe.Criteria.AddCondition("epdt_review_results", ConditionOperator.NotEqual, "无此植入");
            qe.Criteria.AddCondition("epdt_e_card_status", ConditionOperator.NotIn, new[] { "已冲销", "已发放" });
            qe.Criteria.AddCondition("epdt_t_device_applicationid", ConditionOperator.In, idArr);
            qe.Distinct = true;

            var link = qe.AddLink("epdt_t_device_mdata", "epdt_application_find_main_data", "epdt_t_device_mdataid", JoinOperator.Inner);
            link.Columns.AddColumns("epdt_device_status", "epdt_name","epdt_format");
            link.EntityAlias = "md";

            var ec = orgSvc.RetrieveMultiple(qe);
            if (ec == null || ec.Entities.Count == 0)
            {
                return;
            }

            List<Entity> MATEmptyList = (from p in ec.Entities.AsEnumerable()
                                where  !p.Contains("md.epdt_format")
                                select p).ToList();
            if (MATEmptyList.Count > 0) {
                throw new Exception(string.Format("存在MAT为空的设备识别卡主数据: {0}",string.Join(",",MATEmptyList.Select(md=>md.GetAttributeValue<AliasedValue>("md.epdt_name").Value.ToString()).ToList() )));
            }

            //获取关联【EPDT电子植入设备识别卡主数据】【植入设备识别卡状态】为已冲销的数据，更新状态为已冲销
            var sendFailedList = (from p in ec.Entities.AsEnumerable()
                                  where p.Contains("md.epdt_device_status") && p.GetAttributeValue<AliasedValue>("md.epdt_device_status").Value.ToString() == "已冲销"
                                  select p).ToList();

            if (sendFailedList != null && sendFailedList.Count > 0)
            {
                foreach (var item in sendFailedList)
                {
                    try
                    {
                        UpdateEntity(orgSvc, item.Id, "已冲销", "已冲销", "发放");
                        sendFailedCard += item.GetAttributeValue<string>("epdt_name") + "，";
                    }
                    catch (Exception ex)
                    {
                        sendFailedExeFailedCard += item.GetAttributeValue<string>("epdt_name") + "，报错信息：" + ex.Message + "\n";
                    }
                }
            }

            //获取关联【EPDT电子植入设备识别卡主数据】【植入设备识别卡状态】不为已冲销的数据
            var sendSuccList = ec.Entities.AsEnumerable().Except(sendFailedList).ToList();
            if (sendSuccList != null && sendSuccList.Count > 0)
            {
                var sendSuccIdArr = sendSuccList.Select(p => p.Id.ToString()).ToArray();
                var sendSuccPatientIdArr = sendSuccList.Where(p => p.Contains("epdt_patient_abbottid"))
                                                    .Select(p => p.GetAttributeValue<EntityReference>("epdt_patient_abbottid").Id.ToString()).ToArray();

                //Add: 两个申请单  同一个患者

                //查询选择的保卡关联的患者可能存在的其他保卡“发放状态”不等于“已冲销”的数据
                if (sendSuccList != null && sendSuccList.Count > 0)
                {
                    var invalidList = new List<Entity>();
                    foreach (var item in sendSuccList)
                    {
                        try
                        {
                            EntityReference patient = item.GetAttributeValue<EntityReference>("epdt_patient_abbottid");
                            //Tina Add:申请单号
                            string applicationNo = item.GetAttributeValue<string>("epdt_name");

                            if (patient == null) continue;

                            //Tina Add Remark：patientOldList代表当前患者之前的有效卡  在循环中应该找下患者最新的有效卡oldListNewest
                            //查询选择的保卡关联的患者可能存在的其他保卡“发放状态”不等于“已冲销”的数据
                            qe = new QueryExpression("epdt_t_device_application")
                            {
                                ColumnSet = new ColumnSet("epdt_patient_abbottid", "epdt_name", "epdt_implant_date", "epdt_application_find_main_data")
                            };
                            qe.Criteria.AddCondition("epdt_if_delete_device_application", ConditionOperator.Equal, false); //是否删除设备申请为否
                            qe.Criteria.AddCondition("epdt_implant_card_status", ConditionOperator.Equal, true); //生效
                            qe.Criteria.AddCondition("epdt_e_card_status", ConditionOperator.NotEqual, "已冲销");
                            qe.Criteria.AddCondition("epdt_review_results", ConditionOperator.NotEqual, "无此植入");
                            //Tina Add Remark: 找最新的患者的有效卡
                            qe.Criteria.AddCondition("epdt_name", ConditionOperator.NotEqual, applicationNo);
                            qe.Criteria.AddCondition("epdt_patient_abbottid", ConditionOperator.Equal, patient.Id);

                            var linkActive = qe.AddLink("epdt_t_device_mdata", "epdt_application_find_main_data", "epdt_t_device_mdataid", JoinOperator.Inner);
                            linkActive.Columns.AddColumns("epdt_device_status", "epdt_name");
                            linkActive.EntityAlias = "mda";

                            ec = orgSvc.RetrieveMultiple(qe);

                            List<Entity> oldList = new List<Entity>();
                            if (ec != null && ec.Entities.Count > 0)
                                oldList = ec.Entities.AsEnumerable().ToList();

                            var patientOldList = (from p in oldList
                                                  where p.GetAttributeValue<EntityReference>("epdt_patient_abbottid").Id == patient.Id
                                                  orderby p.GetAttributeValue<DateTime>("epdt_implant_date") descending
                                                  select p).ToList();

                            if (patientOldList == null || patientOldList.Count == 0)
                            {
                                //患者不存在其他植入数据，发卡成功
                                UpdateEntity(orgSvc, item.Id, "已发放", "已发放", "发放", true);
                                sendSuccCard += item.GetAttributeValue<string>("epdt_name") + "，";
                            }
                            else
                            {
                                DateTime oldImplantDate = patientOldList.FirstOrDefault().GetAttributeValue<DateTime>("epdt_implant_date");
                                DateTime implantDate = item.GetAttributeValue<DateTime>("epdt_implant_date");

                                /*Tina Add
                                 * 需要加两个变量  判断当前循环的卡号和之前患者的有效卡  是不是一个卡  如果是一个卡  那么两个都发放成功
                                 * 如果不是一个卡，进行原来逻辑的比对
                                */

                                var cardNo = item.GetAttributeValue<AliasedValue>("md.epdt_name").Value;
                                var oldCardNo = patientOldList.FirstOrDefault().GetAttributeValue<AliasedValue>("mda.epdt_name").Value ?? "";

                                if (implantDate >= oldImplantDate || String.Equals(cardNo, oldCardNo))
                                {
                                    //若当前发放设备保卡所关联的“植入日期”大于等于该患者已存在最新“植入日期”
                                    //发放成功
                                    try
                                    {
                                        UpdateEntity(orgSvc, item.Id, "已发放", "已发放", "发放", true);

                                        sendSuccCard += item.GetAttributeValue<string>("epdt_name") + "，";


                                        //失效旧卡  当当前发的卡和已发的卡不是一个卡  那么失效旧卡
                                        if (!String.Equals(cardNo, oldCardNo))
                                        {
                                            foreach (var old in patientOldList)
                                            {
                                                invalidList.Add(old);
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        tracingService.Trace("没更新成功" + ex.Message);
                                    }
                                }
                                else
                                {
                                    //若当前发放设备保卡所关联的“植入日期”小于该患者已存在最新“植入日期”@
                                    //失效当前保卡
                                    invalidList.Add(item);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            sendSuccExeFailedCard += item.GetAttributeValue<string>("epdt_name") + "，报错信息：" + ex.Message + "\n";
                        }
                    }

                    foreach (var item in invalidList)
                    {
                        //统一执行保卡失效
                        try
                        {
                            UpdateEntity(orgSvc, item.Id, "", "", "", false);
                        }
                        catch (Exception ex)
                        {
                            invalidExeFailedCard += item.GetAttributeValue<string>("epdt_name") + "，报错信息：" + ex.Message + "\n";
                        }
                    }
                }
            }

            //如果有执行错误，统一抛错。整个程序动作会回滚
            if (!string.IsNullOrWhiteSpace(sendFailedExeFailedCard) || !string.IsNullOrWhiteSpace(sendSuccExeFailedCard) || !string.IsNullOrWhiteSpace(invalidExeFailedCard))
            {
                var error = new string[] { sendFailedExeFailedCard, sendSuccExeFailedCard, invalidExeFailedCard };
                throw new Exception(string.Format("发放保卡执行出错: {0},{1},{2}", error));
            }

            try
            {
                ResultArgument.Set(context, JsonConvert.SerializeObject(
                    new
                    {
                        sendFailedCard = !string.IsNullOrWhiteSpace(sendFailedCard) ? sendFailedCard.Remove(sendFailedCard.Length - 1) : "",
                        sendSuccCard = !string.IsNullOrWhiteSpace(sendSuccCard) ? sendSuccCard.Remove(sendSuccCard.Length - 1) : ""


                    }));
            }
            catch (Exception ex)
            {
                throw new Exception("Result异常" + ex.Message);

            }
            //throw new InvalidWorkflowException("z + ex.Message");
            tracingService.Trace("发卡成功申请单" + sendSuccCard + "发卡失败申请单" + sendFailedCard);

        }

        private void UpdateEntity(IOrganizationService organizationService, Guid id, string eCardStatus, string paperCardStatus, string operation, bool? implantCardStatus = null)
        {
            var ent = new Entity("epdt_t_device_application")
            {
                Id = id
            };
            if (!string.IsNullOrWhiteSpace(eCardStatus))
                ent["epdt_e_card_status"] = eCardStatus; // 电子卡状态

            //if (!string.IsNullOrWhiteSpace(paperCardStatus))
            //    ent["epdt_paper_card_status"] = paperCardStatus; // 纸质卡状态   2023/8/28：不再更新此字段 

            if (!string.IsNullOrWhiteSpace(operation))
                ent["epdt_operation"] = operation; // 操作

            if (implantCardStatus.HasValue)
            {
                ent["epdt_implant_card_status"] = implantCardStatus.Value; //  保卡是否有效的状态  true/false
            }

            organizationService.Update(ent);
        }

    }
}
