﻿using System;
using System.Linq;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using System.Activities;
using System.Collections.Generic;
using Abbott.Onepdt.Plugins.Models;
using Abbott.Onepdt.Plugins.Service;
using System.Security.Policy;
using System.IdentityModel.Metadata;
using System.Windows.Documents;



namespace Abbott.Onepdt.Plugins.Actions
{
    public class GenerateIdCardAction : IPlugin
    {

        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService service = serviceFactory.CreateOrganizationService(null); // 权限

            ITracingService tracingService = (ITracingService)serviceProvider.GetService(typeof(ITracingService));
            try
            {
                string OpreationId = (string)context.InputParameters["OperationId"];


                ActionResult actionResult = new ActionResult();

                List<CardTypeModel> cardTypeModels = IdentifyCardTypeService.GetCardTypeModels(service, OpreationId);

                //判断跟台信息是否关联电子保卡申请表
                bool isRelatedDeviceApplication = false;
                Entity operation = service.Retrieve("onepdt_t_operation", new Guid(OpreationId), new ColumnSet("onepdt_device_application"));
                if (operation.Contains("onepdt_device_application") && operation["onepdt_device_application"] != null)
                {
                    isRelatedDeviceApplication = true;
                }

                Entity operationToUpdate = new Entity("onepdt_t_operation", new Guid(OpreationId));
                if (cardTypeModels.Count > 0)
                {
                    List<string> IdCards = new List<string>();


                    // Try to retrieve existing record
                    QueryExpression queryCardtype = new QueryExpression("onepdt_t_id_cardtype_management");
                    queryCardtype.ColumnSet = new ColumnSet(true);
                    queryCardtype.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);

                    EntityCollection cardtypes = service.RetrieveMultiple(queryCardtype);

                    foreach (var cardtypemodel in cardTypeModels)
                    {
                        // create a record in onepdt_t_patient_id_card
                        Entity patientidcard = new Entity("onepdt_t_patient_id_card");

                        patientidcard["onepdt_t_operation"] = new EntityReference("onepdt_t_operation", new Guid(OpreationId));


                        patientidcard["onepdt_print_status"] = new OptionSetValue(0); // 未打印
                        patientidcard["onepdt_print_count"] = 0;

                        patientidcard["onepdt_warranty_card_type"] = cardtypemodel.MAT;
                        patientidcard["onepdt_final_warranty_card_type"] = cardtypemodel.MAT;


                        var cardType = cardtypes.Entities.Where(e => e.GetAttributeValue<string>("onepdt_name") == cardtypemodel.MAT).FirstOrDefault();
                        if (cardType != null)
                        {
                            patientidcard["onepdt_cardtypeid"] = new EntityReference("onepdt_t_id_cardtype_management", cardType.Id);
                        }
                        patientidcard["onepdt_main_unit1_model"] = cardtypemodel.ModelNo;
                        patientidcard["onepdt_main_unit1_serial_number"] = cardtypemodel.SN;
                        patientidcard["onepdt_main_unit1_warranty_period"] = cardtypemodel.WarrantyPeriod;
                        patientidcard["onepdt_main_unit1_manufacturer_brand"] = cardtypemodel.Manufacturer;
                        patientidcard["onepdt_main_unit1_selected_pacing_defibrillation_mode"] = cardtypemodel.SelectedMode;
                        patientidcard["onepdt_main_unit1_pacing_defibrillation_mode"] = cardtypemodel.MainMode;
                        patientidcard["onepdt_main_unit1_scan_intensity"] = cardtypemodel.ScanIntensity;

                        for (int i = 0; i < cardtypemodel.Accessories.Count && i < 4; i++)
                        {
                            var accessory = cardtypemodel.Accessories[i];
                            patientidcard[$"onepdt_electrode{i + 1}_implant_date"] = DateTime.Parse(accessory.ImpDate);
                            //patientidcard[$"onepdt_electrode{i + 1}_manufacturer_brand"] = accessory.Manufacturer;
                            patientidcard[$"onepdt_electrode{i + 1}_model"] = accessory.ModelNo;
                            patientidcard[$"onepdt_electrode{i + 1}_serial_number"] = accessory.SN;
                        }
                        // Get the ID of the newly created patientidcard record
                        Guid patientIdCardId = service.Create(patientidcard);
                        Entity createdPatientIdCard = service.Retrieve("onepdt_t_patient_id_card", patientIdCardId, new ColumnSet("onepdt_name"));
                        string autoGeneratedName = createdPatientIdCard.GetAttributeValue<string>("onepdt_name");


                        IdCards.Add(autoGeneratedName);

                        // Create or update record in onepdt_t_device_mdata
                        Entity deviceMdata = new Entity("epdt_t_device_mdata");

                        // Set the composite key fields
                        deviceMdata["epdt_name"] = autoGeneratedName;
                        deviceMdata["epdt_format"] = cardtypemodel.MAT;

                        deviceMdata["epdt_operation_number"] = cardtypemodel.Operationnumber;
                        deviceMdata["epdt_device_status"] = "已审批";
                        deviceMdata["epdt_implant_serial_number"] = cardtypemodel.SN;
                        deviceMdata["epdt_implant_device_model"] = cardtypemodel.ModelNo;

                        // Set other fields
                        if(isRelatedDeviceApplication){
                          deviceMdata["epdt_epdt_epdt_if_matched1"] = "是";
                        }
                        deviceMdata["epdt_pacemaker_manufacturer_brand"] = cardtypemodel.Manufacturer;
                        deviceMdata["epdt_implant_date"] = DateTime.Parse(cardtypemodel.ImpDate);
                        deviceMdata["epdt_implant_electrode_date"] = DateTime.Parse(cardtypemodel.ImpDate);
                        deviceMdata["epdt_guarantee_period"] = cardtypemodel.WarrantyPeriod;
                        deviceMdata["epdt_selected_pacingordefibrillation_mode"] = cardtypemodel.SelectedMode;
                        deviceMdata["epdt_main_pacingordefibrillation_mode"] = cardtypemodel.MainMode;
                        deviceMdata["epdt_nuclear_magnetic_scanning_intensity"] = cardtypemodel.ScanIntensity;

                        deviceMdata["epdt_patient_name"] = cardtypemodel.Name;
                        deviceMdata["epdt_patient_gender"] = cardtypemodel.Gender;
                        deviceMdata["epdt_implant_hospital_name"] = cardtypemodel.Hospital;
                        deviceMdata["epdt_implant_hospital_code"] = cardtypemodel.HospitalCode;
                        deviceMdata["epdt_hospital_address"] = cardtypemodel.HospitalAdd;
                        deviceMdata["epdt_hospital_phone"] = cardtypemodel.HospitalTel;
                        deviceMdata["epdt_implant_doctor"] = cardtypemodel.Physician1;

                        for (int i = 0; i < cardtypemodel.Accessories.Count && i < 3; i++)
                        {
                            var accessory = cardtypemodel.Accessories[i];
                            //deviceMdata[$"epdt_electrode{i + 1}_manufacturer_brand"] = accessory.Manufacturer;
                            deviceMdata[$"epdt_electrode{i + 1}_model"] = accessory.ModelNo;
                            deviceMdata[$"epdt_electrode{i + 1}_serial_number"] = accessory.SN;
                        }

                        deviceMdata["epdt_if_valid"] = "";
                        //deviceMdata["epdt_nuclear_magnetic_compatibility_mark"] = cardtypemodel.ScanIntensity;

                        // Try to retrieve existing record
                        QueryExpression query = new QueryExpression("epdt_t_device_mdata");
                        query.ColumnSet = new ColumnSet("epdt_t_device_mdataid");
                        query.Criteria.AddCondition("epdt_name", ConditionOperator.Equal, autoGeneratedName);

                        EntityCollection existingDevices = service.RetrieveMultiple(query);

                        if (existingDevices.Entities.Count > 0)
                        {
                            // Update existing record
                            Entity existingDevice = existingDevices.Entities[0];
                            deviceMdata.Id = existingDevice.Id;
                            service.Update(deviceMdata);
                        }
                        else
                        {
                            // Create new record
                            deviceMdata.Id = service.Create(deviceMdata);
                        }

                        //判断跟台信息关联电子保卡申请表后直接匹配成功
                        if (isRelatedDeviceApplication){
                            Guid deviceApplicationId = ((EntityReference)operation["onepdt_device_application"]).Id;
                            Entity deviceApplicationToUpdate = new Entity("epdt_t_device_application", deviceApplicationId);
                            deviceApplicationToUpdate["epdt_check_date"] = DateTime.UtcNow;
                            deviceApplicationToUpdate["epdt_validated_date"] = DateTime.UtcNow;
                            deviceApplicationToUpdate["epdt_check_status"] = "匹配成功";
                            deviceApplicationToUpdate["epdt_if_verify_immediately"] = "否";
                            deviceApplicationToUpdate["epdt_matching_method"] = false;
                            deviceApplicationToUpdate["epdt_patient_device_id"] = autoGeneratedName;
                            deviceApplicationToUpdate["epdt_application_find_main_data"] = new EntityReference("epdt_t_device_mdata", deviceMdata.Id);
                            service.Update(deviceApplicationToUpdate);
                        }

                    }

                    // Update the onepdt_t_operation entity
                    operationToUpdate["onepdt_patient_id_card"] = string.Join(";", IdCards);
                }
                  

                operationToUpdate["onepdt_approval_status"] = new OptionSetValue(1);
                service.Update(operationToUpdate);

                actionResult.Code = "01";

                actionResult.Message = "success";

                actionResult.Data = cardTypeModels;

                context.OutputParameters["Result"] = Newtonsoft.Json.JsonConvert.SerializeObject(cardTypeModels);
            }
            catch (Exception ex)
            {
                tracingService.Trace(ex.Message + ex.StackTrace);
                throw;
            }
        }

     
    }
}



