<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Xrm.Sdk</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Xrm.Sdk.AliasedValue">
            <summary>
            AliasedValue used by CRM to return aggregate, group by and aliased values from a query
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.AliasedValue.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.AliasedValue"/> class.
            constructor
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.AliasedValue.#ctor(System.String,System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.AliasedValue"/> class.
            constructor to allow specifying values
            </summary>
            <param name="entityLogicalName">Name of entity that the attribute belongs to.</param>
            <param name="attributeLogicalName">Name of the attribute the value is based on.</param>
            <param name="value">Value</param>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.AliasedValue.AttributeLogicalName">
            <summary>
            Gets name of the attribute on which the aggregate, group by or select was performed.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.AliasedValue.EntityLogicalName">
            <summary>
            Gets name of entity the attribute belongs to.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.AliasedValue.Value">
            <summary>
            Gets value returned by the query.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.AliasedValue.NeedFormatting">
            <summary>
            Gets or sets a value indicating whether determines if value needs additional formatting, for internal use
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.AliasedValue.ReturnType">
            <summary>
            Gets or sets value's return type, for internal use
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.AnalyticsStoreDetails">
            <summary>
            Azure Data Lake Storage container information
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.AttributeLogicalNameAttribute.LogicalName">
            <summary>
            Gets the CRM logical name of the attribute.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.ClientEntityMetadata">
            <summary>
            Client Entity Metadata
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.ClientMetadataResults">
            <summary>
            Client Metadata results
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.EntityClientSetting">
            <summary>
            EntityClientSetting
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.DependencyDepth">
            <summary>
            The Enum defines the depth of dependencies that need to be syncd
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Metadata.DependencyDepth.Unknown">
            <summary>
            No type is specified
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Metadata.DependencyDepth.OnDemandWithContext">
            <summary>
            On demand mode where root dependencies and context need to be synced
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Metadata.DependencyDepth.OnDemandWithoutContext">
            <summary>
            On demand mode where context sync is not needed
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Metadata.DependencyDepth.OnDemandContextOnly">
            <summary>
            On demand mode where only context sync is needed
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Metadata.DependencyDepth.Offline">
            <summary>
            Offline mode where everything need to be synced
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Metadata.DependencyDepth.Mobile">
            <summary>
            Mobile mode
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Metadata.DependencyDepth.UserContext">
            <summary>
            User context mode, that
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.MetadataQuery">
            <summary>
            Client Metadata Query for getting dependencies 
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MetadataQuery.MetadataType">
            <summary>
            Gets or sets the metadata type that need to be synced
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MetadataQuery.MetadataSubtype">
            <summary>
            Gets or sets the metadata subtype that need to be synced
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MetadataQuery.EntityLogicalName">
            <summary>
            Gets or sets the entity logical name
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MetadataQuery.MetadataId">
            <summary>
            Gets or sets the metadata id that need to be synced
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MetadataQuery.MetadataName">
            <summary>
            Gets or sets tODO # 689663 remove it
            The metadataName that need to be synced
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MetadataQuery.MetadataNames">
            <summary>
            Gets or sets the metadatanames that need to be synced
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MetadataQuery.GetDefault">
            <summary>
            Gets or sets a value indicating whether the field indicates if default is needed or not
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MetadataQuery.DependencyDepth">
            <summary>
            Gets or sets the dependency depth for the sync
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MetadataQuery.ChangedAfter">
            <summary>
            Gets or sets tODO # 689663 remove it
            The changed after indicates sync after this time
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MetadataQuery.Exclude">
            <summary>
            Gets or sets the exclude indicates don't sync given ids
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MetadataQuery.AppId">
            <summary>
            Gets or sets the app id
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MetadataQuery.UserMetadataDelta">
            <summary>
            Gets or sets the Over all user metadata version
            We want to sync changes after this metadata version
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MetadataQuery.SystemMetadataDelta">
            <summary>
            Gets or sets the Over all System metadata version
            We want to sync changes after this metadata version
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.ConstantsBase`1.Value">
            <summary>
            Represents the current value out of the list of valid values
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.EntityAndAttribute">
            <summary>
            Entity And Attribute mapping for lookup
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.EntityAndAttribute.EntityName">
            <summary>
            Gets or sets the Entity Logical Name
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.EntityAndAttribute.AttributeName">
            <summary>
            Gets or sets the Lookup Attribute Logical Name
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.LookupEntityInfo">
            <summary>
            Client Look up query for getting data and metadata
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityInfo.EntityLogicalName">
            <summary>
            The entity logical name
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityInfo.ViewId">
            <summary>
            The View id
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityInfo.CustomFilter">
            <summary>
            The custom Filter
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityInfo.RelationshipName">
            <summary>
            The Relationship Name
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityInfo.FetchXml">
            <summary>
            The Fetch Xml
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityInfo.LayoutJson">
            <summary>
            The Layout Json
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityInfo.PagingInfo">
            <summary>
            The Paging Cookie
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.LookupEntityMetadata">
            <summary>
            Lookup Entity Metadata
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityMetadata.IsReadOnlyInMobileClient">
            <summary>
            Gets or sets a value indicating whether the Is Read Only In Mobile Client
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityMetadata.IsEnabledInUnifiedInterface">
            <summary>
            Gets or sets a value indicating whether the Is Enabled In Unified Interface
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityMetadata.DisplayName">
            <summary>
            Gets or sets the Display Name
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityMetadata.PrimaryNameAttribute">
            <summary>
            Gets or sets the Primary Name Attribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityMetadata.PrimaryIdAttribute">
            <summary>
            Gets or sets the Primary id/Key Attribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityMetadata.LogicalName">
            <summary>
            Gets or sets the Logical Name
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityMetadata.DisplayCollectionName">
            <summary>
            Gets or sets the Display Collection Name
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityMetadata.IconVectorName">
            <summary>
            Gets or sets the Icon Vector Name
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.LookupEntityResponse">
            <summary>
            Lookup Entity Response
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityResponse.Metadata">
            <summary>
            The Lookup Metadata
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityResponse.EntityLogicalName">
            <summary>
            The Entity Logical Name
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityResponse.Data">
            <summary>
            The Data
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityResponse.TotalRecordCount">
            <summary>
            The Total Record Count
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupEntityResponse.PagingCookie">
            <summary>
            The Entity Logical Name
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.LookupMetadata">
            <summary>
            Lookup Metadata
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupMetadata.Entity">
            <summary>
            The Lookup Entity Metadata
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupMetadata.View">
            <summary>
            The Lookup View
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.LookupDataRequest">
            <summary>
            Lookup Data Request
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupDataRequest.LookupEntityByName">
            <summary>
            The look up entity by name
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupDataRequest.EntityAndAttribute">
            <summary>
            The Entity And Attribute to get the realted entity for lookup search
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupDataRequest.AppId">
            <summary>
            The App module id
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupDataRequest.RelatedRecordId">
            <summary>
            The Related Record Id
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupDataRequest.QueryString">
            <summary>
            The Query String
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupDataRequest.ReturnMetadata">
            <summary>
            The Return Metadata
            If its passed as true we will return related metadata in the response also
            Otherwise only data will be returned
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.LookupDataResponse">
            <summary>
            Lookup Data Request
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupDataResponse.EntityResponses">
            <summary>
            The Entity Responses
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.LookupView">
            <summary>
            Lookup View
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupView.ViewId">
            <summary>
            The View Id
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupView.ViewName">
            <summary>
            The View Name
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupView.Columns">
            <summary>
            The View Columns
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupView.FetchXml">
            <summary>
            The Fetch Xml
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.LookupView.LayoutJson">
            <summary>
            The Layout Json
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.ViewColumn">
            <summary>
            View Column
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.ViewColumn.EntityLogicalName">
            <summary>
            The Entity Logical Name
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.ViewColumn.AttributeLogicalName">
            <summary>
            The Attribute Logical Name
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.ViewColumn.DataType">
            <summary>
            The Data Type
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.ViewColumn.Format">
            <summary>
            The Format
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.ViewColumn.Alias">
            <summary>
            The alias
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Metadata.AttributeMetadata.GetAttributeTypeDisplayName(Microsoft.Xrm.Sdk.Metadata.AttributeTypeCode)">
            <summary>
            Please make sure add any new AttributeTypes in here so a given AttributeType is translated to AttributeTypeDisplayName
            and also make sure add the new AttributeType to src\managedplatform\sdk\metadata\metadatacache\MetadataCacheEnums.cs
            </summary>
            <param name="attributeType"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.AttributeMetadata.AttributeTypeName">
            <summary>
            AttributeTypeName will eventually replace AttributeTypeCode enum as enum is not WCF backcompat friendly for adding new
            attribute types. All new AttributeTypes added should use the AttributeTypeName instead.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.AttributeMetadata.Description">
            <summary>
            Gets or sets the description for the attribute.  This is valid for both the Create and Update of an attribute.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.AttributeMetadata.DeprecatedVersion">
            <summary>
            Gets the version an attribute was deprecated in.
            This is the assembly version of the product's release
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.AttributeMetadata.IntroducedVersion">
            <summary>
            Gets the version an attribute was introduced in.
            This is the assembly version of the product's release
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.AttributeMetadata.SourceType">
            <summary>
            Indicates how the field's data is stored (0:Persist; 1:Calculated; ...)
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Metadata.AttributeTypeDisplayName.MultiSelectPicklistType">
            <summary>
            Attribute type to allow multiple values to be selected on Picklist
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Metadata.AttributeTypeDisplayName.op_Implicit(System.String)~Microsoft.Xrm.Sdk.Metadata.AttributeTypeDisplayName">
            <summary>
            Implicity converts a string to AttributeTypeDisplayName
            </summary>
            <param name="formatName">String value to convert</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Metadata.DateTimeBehavior.op_Implicit(System.String)~Microsoft.Xrm.Sdk.Metadata.DateTimeBehavior">
            <summary>
            Implicity converts a string to Behavior
            </summary>
            <param name="behavior">String value to convert</param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.BigIntAttributeMetadata.MaxValue">
            <summary>
            Required for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.BigIntAttributeMetadata.MinValue">
            <summary>
            Required for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.BooleanAttributeMetadata.FormulaDefinition">
            <summary>
            String representing the formula of a calculated field.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.BooleanAttributeMetadata.SourceTypeMask">
            <summary>
             Indicates the type of attributes present in the Calculated Field (i.e. persistent, logical, related, calculated, invalid or any combination of these types)
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.CascadeSPGenerationRequest.IsBackedByXdb">
            <summary>
            Gets or sets if org is backed by Xdb.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.CascadeSPGenerationRequest.CascadingChangeList">
            <summary>
            Gets or sets the cascading changing list.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.CascadeSPGenerationRequest.CascadeRollupEntities">
            <summary>
            Gets or sets list of cascade rollup entities.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.CascadeSPGenerationRequest.EntitiesBeingChanged">
            <summary>
            Gets or sets the list which contains object type code of the entities for which cascade SP has to be generated.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.CascadeSPGenerationRequest.RequestCorrelationId">
            <summary>
            Gets or sets the request correlation id.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.CreateReserveEntityRequest">
            <summary>
            Contract class for Creating reserve entity request
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.CreateReserveEntityRequest.RequestCorrelationId">
            <summary>
            Gets or sets the request correlation id.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.CreateTDSViewAsyncRequest.EntityLogicalName">
            <summary>
            Gets or sets the Entity logical name.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.CreateTDSViewAsyncRequest.CallerId">
            <summary>
            Gets or sets the Caller id.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.CreateTDSViewAsyncRequest.RequestCorrelationId">
            <summary>
            Gets or sets the request correlation id.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.DateTimeAttributeMetadata.Format">
            <summary>
            Required for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.DateTimeAttributeMetadata.ImeMode">
            <summary>
            Valid for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.DateTimeAttributeMetadata.SourceTypeMask">
            <summary>
             Indicates the type of attributes present in the Calculated Field (i.e. persistent, logical, related, calculated, invalid or any combination of these types)
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.DateTimeAttributeMetadata.FormulaDefinition">
            <summary>
            String representing the formula of a calculated field.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.DateTimeAttributeMetadata.DateTimeBehavior">
            <summary>
            Valid for CreateAttribute.
            Valid for UpdateAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.DecimalAttributeMetadata.FormulaDefinition">
            <summary>
            String representing the formula of a calculated field.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.DecimalAttributeMetadata.SourceTypeMask">
            <summary>
             Indicates the type of attributes present in the Calculated Field (i.e. persistent, logical, related, calculated, invalid or any combination of these types)
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.DoubleAttributeMetadata.ImeMode">
            <summary>
            Valid for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.DoubleAttributeMetadata.MaxValue">
            <summary>
            Required for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.DoubleAttributeMetadata.MinValue">
            <summary>
            Required for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.DoubleAttributeMetadata.Precision">
            <summary>
            Required for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.EntityMetadata.IntroducedVersion">
            <summary>
            Gets the version an entity was introduced in.
            This is the assembly version of the product's release
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.EntityMetadataCollection">
            <summary>
            Represents a collection of entity metadata
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.EntityUIProcessRequest.EntityLogicalName">
            <summary>
            Gets or sets the Entity logical name.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.EntityUIProcessRequest.CallerId">
            <summary>
            Gets or sets the Caller id.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.EntityUIProcessRequest.RequestCorrelationId">
            <summary>
            Gets or sets the request correlation id.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.FileAttributeMetadata">
            <summary>
            Introduces FileAttribute metadata to Xrm SDK
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.FileAttributeMetadata.MaxSizeInKB">
            <summary>
            Maximum file size(in KBs) allowed for this attribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.ImageAttributeMetadata.MaxSizeInKB">
            <summary>
            Maximum file size(in KBs) allowed for this attribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.ImageAttributeMetadata.CanStoreFullImage">
            <summary>
            Indicates whether this Image attribute can store full image
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.IntegerAttributeMetadata.Format">
            <summary>
            Required for CreateAttribute
            Ignored for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.IntegerAttributeMetadata.MaxValue">
            <summary>
            Required for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.IntegerAttributeMetadata.MinValue">
            <summary>
            Required for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.IntegerAttributeMetadata.FormulaDefinition">
            <summary>
            String representing the formula of a calculated field.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.IntegerAttributeMetadata.SourceTypeMask">
            <summary>
             Indicates the type of attributes present in the Calculated Field (i.e. persistent, logical, related, calculated, invalid or any combination of these types)
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MemoAttributeMetadata.Format">
            <summary>
            Valid for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MemoAttributeMetadata.FormatName">
            <summary>
            Valid for UpdateAttribute.
            Valid for UpdateAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MemoAttributeMetadata.ImeMode">
            <summary>
            Valid for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MemoAttributeMetadata.MaxLength">
            <summary>
            Required on non-email Memo attributes for CreateAttribute
            Valid on UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MemoAttributeMetadata.IsLocalizable">
            <summary>
            Valid on RetrieveEntityRequest
            Valid on RetrieveAllEntitiesRequest
            Valid on RetrieveAttributeRequest
            Valid on RetrieveMetadataChanges
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Metadata.MemoFormatName.op_Implicit(System.String)~Microsoft.Xrm.Sdk.Metadata.MemoFormatName">
            <summary>
            Implicity converts a string to MemoFormatName
            </summary>
            <param name="formatName">String value to convert</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.AlternatekeyAttribute">
            <summary>
            This custom attribute classifies a property as alternate key
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MoneyAttributeMetadata.ImeMode">
            <summary>
            Valid for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MoneyAttributeMetadata.MaxValue">
            <summary>
            Required for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MoneyAttributeMetadata.MinValue">
            <summary>
            Required for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MoneyAttributeMetadata.Precision">
            <summary>
            Required for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MoneyAttributeMetadata.PrecisionSource">
            <summary>
            Required for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MoneyAttributeMetadata.FormulaDefinition">
            <summary>
            String representing the formula of a calculated field.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MoneyAttributeMetadata.SourceTypeMask">
            <summary>
             Indicates the type of attributes present in the Calculated Field (i.e. persistent, logical, related, calculated, invalid or any combination of these types)
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MultiSelectPicklistAttributeMetadata.FormulaDefinition">
            <summary>
            String representing the formula of a calculated field.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MultiSelectPicklistAttributeMetadata.SourceTypeMask">
            <summary>
             Indicates the type of attributes present in the Calculated Field (i.e. persistent, logical, related, calculated, invalid 
             or any combination of these types)
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MultiSelectPicklistAttributeMetadata.ParentPicklistLogicalName">
            <summary>
            Gets or sets parent picklist attribute's logical name.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.MultiSelectPicklistAttributeMetadata.ChildPicklistLogicalNames">
            <summary>
            Gets list (logical names) of a picklist attribute's child attributes
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.PicklistAttributeMetadata.FormulaDefinition">
            <summary>
            String representing the formula of a calculated field.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.PicklistAttributeMetadata.SourceTypeMask">
            <summary>
             Indicates the type of attributes present in the Calculated Field (i.e. persistent, logical, related, calculated, invalid or any combination of these types)
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.PicklistAttributeMetadata.ParentPicklistLogicalName">
            <summary>
            Gets or sets parent picklist attribute's logical name.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.PicklistAttributeMetadata.ChildPicklistLogicalNames">
            <summary>
            Gets list (logical names) of a picklist attribute's child attributes
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.PrivilegeRoleAssignmentRequest.PrivilegeRoleMappings">
            <summary>
            Gets or sets the Privilege roles mappings.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.PrivilegeRoleAssignmentRequest.RequestCorrelationId">
            <summary>
            Gets or sets the request correlation id.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.PrivilegeRoleMapping.Privileges">
            <summary>
            Gets or sets the privileges info.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.PrivilegeRoleMapping.RoleIds">
            <summary>
            Gets or sets the Role ids.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.PrivilegeRoleMapping.IsRole">
            <summary>
            Gets or sets if RoleId is a Role (As opposed to RoleTemplate.) Default - false (Role Template)
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.PrivilegeInfo.PrivilegeId">
            <summary>
            Gets or sets the privilege id.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.PrivilegeInfo.Depth">
            <summary>
            Gets or sets the privilege depth
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.Query.AttributeQueryExpression">
            <summary>
            Class used to specify a query for attribute metadata
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.Query.DeletedMetadataCollection">
            <summary>
            Data collection of metadata that has been deleted from the server since the client last synced
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.Query.DeletedMetadataFilters">
            <summary>
            Enumeration that lists the types of deleted metadata
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.Query.EntityKeyQueryExpression">
            <summary>
            Class used to specify a query for entity key metadata
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.Query.EntityQueryExpression">
            <summary>
            Class used to specify a query for entity metadata
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.EntityQueryExpression.LabelQuery">
            <summary>
            Specifies the filter criteria for any labels selected by this query
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.EntityQueryExpression.AttributeQuery">
            <summary>
            Specifies the filter criteria for any attributes selected by this query
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.EntityQueryExpression.RelationshipQuery">
            <summary>
            Specifies the filter criteria for any relationships selected by this query
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.EntityQueryExpression.KeyQuery">
            <summary>
            Specifies the filter criteria for any entity keys selected by this query
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.Query.LabelQueryExpression">
            <summary>
            Class used to specify a query for labels
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.LabelQueryExpression.FilterLanguages">
            <summary>
            List of languages on which labels are filtered
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.LabelQueryExpression.MissingLabelBehavior">
            <summary>
            How should we default labels that don't exist in the requested languages
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.Query.MetadataConditionExpression">
            <summary>
            Class that is used to specify a condition for a metadata type
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.MetadataConditionExpression.PropertyName">
            <summary>
            Name of the property the condition is applied on
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.MetadataConditionExpression.ConditionOperator">
            <summary>
            Operator for the condition expression
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.MetadataConditionExpression.Value">
            <summary>
            Object that the property is compared against
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.Query.MetadataFilterExpression">
            <summary>
            Class that is used to group a set of query conditions
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.MetadataFilterExpression.Conditions">
            <summary>
            Set of conditions that are part of this filter
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.MetadataFilterExpression.FilterOperator">
            <summary>
            Logical operator applied between conditions and child filters
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.MetadataFilterExpression.Filters">
            <summary>
            Collection of child filters
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.Query.MetadataPropertiesExpression">
            <summary>
            Class used to specify the properties of a metadata type that need to be retrieved
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.MetadataPropertiesExpression.AllProperties">
            <summary>
            Specifies whether all properties need to be retrieved
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.MetadataPropertiesExpression.PropertyNames">
            <summary>
            Specifies the names of the properties that need to be retrieved
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.Query.MetadataQueryBase">
            <summary>
            Abstract base class for metadata query expressions
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.MetadataQueryExpression.Criteria">
            <summary>
            Defines the criteria used to query the metadata
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.Query.MetadataQueryExpression.Properties">
            <summary>
            Defines the set of metadata properties that need to be retrieved
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.Query.RelationshipQueryExpression">
            <summary>
            Class used to specify a query for relationship metadata
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.ReserveEntityForSolutionRequest.RequestCorrelationId">
            <summary>
            Gets or sets the request correlation id.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Metadata.StateOptionMetadata">
            <summary>
            StateOption represents the name/value combination that can
            be used for state values. In addition to the base Option
            info, StateOption has an extra field indicating what the default
            status for this state is.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.StringAttributeMetadata.Format">
            <summary>
            Gets or sets required for CreateAttribute.  
            Ignored for UpdateAttribute.
            Use FormatName instead of Format
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.StringAttributeMetadata.FormatName">
            <summary>
            Gets or sets valid for CreateAttribute.
            Valid for UpdateAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.StringAttributeMetadata.ImeMode">
            <summary>
            Gets or sets valid for CreateAttribute.
            Valid for UpdateAttribute.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.StringAttributeMetadata.MaxLength">
            <summary>
            Gets or sets required for CreateAttribu;te
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.StringAttributeMetadata.YomiOf">
            <summary>
            Gets or sets required for CreateAttribute
            Valid for UpdateAttribute
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.StringAttributeMetadata.IsLocalizable">
            <summary>
            Gets valid on RetrieveEntityRequest
            Valid on RetrieveAllEntitiesRequest
            Valid on RetrieveAttributeRequest
            Valid on RetrieveMetadataChanges
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.StringAttributeMetadata.FormulaDefinition">
            <summary>
            Gets or sets string representing the formula of a calculated field.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Metadata.StringAttributeMetadata.SourceTypeMask">
            <summary>
             Gets indicates the type of attributes present in the Calculated Field (i.e. persistent, logical, related, calculated, invalid or any combination of these types)
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Metadata.StringFormatName.op_Implicit(System.String)~Microsoft.Xrm.Sdk.Metadata.StringFormatName">
            <summary>
            Implicity converts a string to StringFormatName
            </summary>
            <param name="formatName">String value to convert</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.BusinessEntityChanges">
            <summary>
            BusinessEntityChanges that holds the Token and the Collection of Entities or EntityReferances
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.BusinessEntityChanges.MoreRecords">
            <summary>
            Gets or sets a value indicating whether flag to indicate whether there are more records at server side
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.BusinessEntityChanges.DataToken">
            <summary>
            Gets or sets last version synced by client
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.BusinessEntityChanges.Changes">
            <summary>
            Gets or sets holds the collection of Entities and EntityReferences that are INSERTED/UPDATED or DELETED at the server side
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.BusinessEntityChanges.GlobalMetadataVersion">
            <summary>
            Gets or sets global metadata version
            This value is same as MetadataCache.Timestamp
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.IChangedItem">
            <summary>
            Interface implimented by the NewOrUpdatedItem or RemovedOrDeletedItem classes for supporting SubscriptionEntityQueryMappingService API
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.NewOrUpdatedItem">
            <summary>
            Holds the Change Type and Entities that are INSERTED or UPDATED at the server since the last sync
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.NewOrUpdatedItem.Type">
            <summary>
            Gets or sets changeType that indicates the type of the change
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.NewOrUpdatedItem.NewOrUpdatedEntity">
            <summary>
            Gets or sets entity of the INSERTED/UPDATED business entity record.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.RemovedOrDeletedItem">
            <summary>
            Holds the Change Type and EntityReference of the records that are REMOVED or DELETED at the server since the last sync
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.RemovedOrDeletedItem.Type">
            <summary>
            Gets or sets changeType that indicates the type of the change
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.RemovedOrDeletedItem.RemovedItem">
            <summary>
            Gets or sets entity of the REMOVED/DELETED business entity record.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.ChangeType">
            <summary>
            Holds enumeration for the items
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.ClientExceptionHelper.ThrowIfNegative(System.Int32,System.String)">
            <summary>
            Throw an CrmArgumentOutOfRangeException if the specified integer is less than zero.
            </summary>
            <param name="value">Integer value.</param>
            <param name="parameterName">Name of the parameter.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.ClientExceptionHelper.ThrowIfNull(System.Object,System.String)">
            <summary>
            Throw an XrmArgumentNullException if the specified parameter is null.
            </summary>
            <param name="parameter">Parameter value.</param>
            <param name="name">Parameter name.</param>
            
        </member>
        <member name="M:Microsoft.Xrm.Sdk.ClientExceptionHelper.ThrowIfNullOrEmpty(System.String,System.String)">
            <summary>
            Throw an XrmArgumentNullException if the specified parameter is null.
            </summary>
            <param name="parameter">Parameter value.</param>
            <param name="name">Parameter name.</param>
            
        </member>
        <member name="M:Microsoft.Xrm.Sdk.ClientExceptionHelper.ThrowIfGuidEmpty(System.Guid,System.String)">
            <summary>
            Throw an XrmArgumentException if the specified parameter is Guid.Empty.
            </summary>
            <param name="parameter">Parameter value.</param>
            <param name="name">Parameter name.</param>
            
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Client.SdkHeaders.IsOfflinePlayback">
            <summary>
            Header set by offline client when playing back requests while going online
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Client.SdkHeaders.CallerId">
            <summary>
            Supports alternate user context in SDK. Type:Guid
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Client.SdkHeaders.CallerRegardingObjectId">
            <summary>
            Alternate user regarding object context in SDK. Type:Guid
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Client.SdkHeaders.UserType">
            <summary>
            Defines user type in SDK.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Client.SdkHeaders.LanguageCodeOverride">
            <summary>
            Defines a language code override for an SDK call
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Client.SdkHeaders.OutlookSyncOperationType">
            <summary>
            Necessary for throttling Outlook sync operations.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Client.SdkHeaders.ClientAppName">
            <summary>
            Used to inform the server about the calling client app
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Client.SdkHeaders.ClientAppVersion">
            <summary>
            Used to inform the server about the calling client app version
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Client.SdkHeaders.SdkClientVersion">
            <summary>
            Used to inform the server about the calling client sdk client version.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.Utilites.GetXrmSdkAssemblyFileVersion">
            <summary>
            Get's the file version of the Xrm Sdk assembly that is loaded in the current client domain.
            For Sdk clients called via the OrganizationServiceProxy this is the version of the local Microsoft.Xrm.Sdk dll used by the Client App.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.EntityLogicalNameAttribute.LogicalName">
            <summary>
            Gets the CRM logical name of the entity.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.MergeOption">
            <summary>
            Determines the synchronization option for sending or receiving entities to or from a data service. 
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.SaveChangesOptions">
            <summary>
            Indicates change options when <see cref="M:Microsoft.Xrm.Sdk.Client.OrganizationServiceContext.SaveChanges(Microsoft.Xrm.Sdk.Client.SaveChangesOptions)"/> is called. 
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.OrganizationServiceContext">
            <summary>
            The runtime context of the data service.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.OrganizationServiceContext.CreateQuery``1">
            <summary>
            Binds to the set of entities of a specified type.
            </summary>
            <typeparam name="TEntity"></typeparam>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.OrganizationServiceContext.CreateQuery(System.String)">
            <summary>
            Binds to the set of dynamic entities.
            </summary>
            <param name="entityLogicalName"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.OrganizationServiceContext.LoadProperty(Microsoft.Xrm.Sdk.Entity,System.String)">
            <summary>
            Loads the related entity collection for the specified relationship.
            </summary>
            <param name="entity"></param>
            <param name="propertyName"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.OrganizationServiceContext.LoadProperty(Microsoft.Xrm.Sdk.Entity,Microsoft.Xrm.Sdk.Relationship)">
            <summary>
            Loads the related entity collection for the specified relationship.
            </summary>
            <param name="entity"></param>
            <param name="relationship"></param>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.ProxyTypesAssemblyAttribute.ContainsSharedContracts">
            <summary>
            indicates whether the assembly contains data contracts known to the SDK host
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.DiscoveryServiceContextInitializer">
            <summary>
            Manages context for sdk calls
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.DiscoveryServiceProxy">
            <summary>
            helper class that manages a ChannelFactory and serves up channels for sdk client use
            <remarks>For internal use only</remarks>
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.OrganizationServiceContextInitializer">
            <summary>
            Manages context for sdk calls
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.OrganizationServiceProxy">
            <summary>
            helper class that manages a ChannelFactory and serves up channels for sdk client use
            <remarks>For internal use only</remarks>
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.OrganizationServiceProxy.EnableProxyTypes">
            <summary>
            This method will enable support for the default strong proxy types. 
            
            If you are using a shared Service Configuration instance, you must be careful if using 
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.OrganizationServiceProxy.EnableProxyTypes(System.Reflection.Assembly)">
            <summary>
            This method will enable support for the strong proxy types exposed in the passed assembly.
            <param name="assembly">The assembly that will provide support for the desired strong types in the proxy.</param>
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.OrganizationServiceProxy.GetXrmSdkAssemblyFileVersion">
            <summary>
            Get's the file version of the Xrm Sdk assembly that is loaded in the current client domain.
            For Sdk clients called via the OrganizationServiceProxy this is the version of the local Microsoft.Xrm.Sdk dll used by the Client App.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.UserType">
            <summary>
            Enumeration of the different type of user.  This can be a crm user or 
            an ExternalParty user calling the sdk.  The type is used to help
            identify application calls for impersonations.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.ServiceChannel`1">
            <summary>
            	Channel class to manage connections to the SDK endpoints
            </summary>
            <typeparam name = "TChannel"></typeparam>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.ServiceChannel`1.Channel">
            <summary>
            	Gets a channel, creates new one if current one is not present or faulted
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.ServiceChannel`1.Factory">
            <summary>
            	Returns the Channel factory used
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceChannel`1.CreateChannel">
            <summary>
            	Create a new channel
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceChannel`1.Channel_Faulted(System.Object,System.EventArgs)">
            <summary>
            	Attaches to Faulted event for cleanup
            </summary>
            <param name = "sender"></param>
            <param name = "e"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceContextInitializer`1.#ctor(Microsoft.Xrm.Sdk.Client.ServiceProxy{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Client.ServiceContextInitializer`1"/> class.
            Constructs a context initializer
            </summary>
            <param name="proxy">sdk proxy</param>
            <param name="serviceProxyConfiguration"></param>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.ServiceFederatedChannel`1">
            <summary>
            Class used to manage channels for SDK when claims enabled.
            </summary>
            <typeparam name="TChannel"></typeparam>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceProxy`1.#ctor(Microsoft.Xrm.Sdk.Client.IServiceManagement{`0},Microsoft.Xrm.Sdk.Client.SecurityTokenResponse)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Client.ServiceProxy`1"/> class.
            	This constructor is used when the service management interface and security token have been acquired externally.
            
            	This version can not be used for non-claims authentication.
            
            	Also, with this constructor, the Authenticate call should not be attempted.
            </summary>
            <param name = "serviceManagement"></param>
            <param name = "securityTokenResponse"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceProxy`1.#ctor(Microsoft.Xrm.Sdk.Client.IServiceConfiguration{`0},Microsoft.Xrm.Sdk.Client.SecurityTokenResponse)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Client.ServiceProxy`1"/> class.
            	This constructor is used when the service configuration and security token have been acquired externally.
            
            	This version can not be used for non-claims authentication.
            
            	Also, with this constructor, the Authenticate call should not be attempted.
            </summary>
            <param name = "serviceConfiguration"></param>
            <param name = "securityTokenResponse"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceProxy`1.#ctor(Microsoft.Xrm.Sdk.Client.IServiceManagement{`0},System.ServiceModel.Description.ClientCredentials)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Client.ServiceProxy`1"/> class.
            	This constructor is used when the service configuration has been acquired externally, and Windows-based auth is known to be used already.
            
            	Also, with this constructor, the Authenticate call should not be attempted.
            </summary>
            <param name = "serviceManagement"></param>
            <param name = "clientCredentials"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceProxy`1.#ctor(Microsoft.Xrm.Sdk.Client.IServiceConfiguration{`0},System.ServiceModel.Description.ClientCredentials)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Client.ServiceProxy`1"/> class.
            	This constructor is used when the service configuration has been acquired externally, and Windows-based auth is known to be used already.
            
            	Also, with this constructor, the Authenticate call should not be attempted.
            </summary>
            <param name = "serviceConfiguration"></param>
            <param name = "clientCredentials"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceProxy`1.Authenticate">
            <summary>
            	Calling Authenticate will cause the following things to happen:
            	1.  If the ServiceChannel has already been opened, it will be closed and set to null.
            	2.  The ChannelFactory will be closed and set to null.
            	3.  AuthenticateCore will be called.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.ServiceProxy`1.IsAuthenticated">
            <summary>
            	Determines whether the Authenticate call will be made during the ValidateChannel call.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.ServiceProxy`1.AutoCloseChannel">
            <summary>
            Determines if the proxy object should close the channel automatically on successful 
            completion of requests. This is set to false when the same proxy object is being 
            shared between multiple threads.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceProxy`1.AuthenticateCrossRealmCore">
            <summary>
            	Provided to authenticate a user in a realm other than the realm that CRM Server is located.  This call will attempt to authenticate
            	in the home realm, then use the corresponding token to authenticate to the CRM Server realm STS.  In addition to the normal client credential 
            	requirements, the HomeRealmUri is expected to be set in the call to the constructor.
            
            	The resulting SecurityTokenResponse should be the security token that can be used to authenticate against the CRM Server realm STS.
            
            	This method may be overridden in the case that home realm authenticate requires multiple hops, which is not supported by this call.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceProxy`1.ValidateAuthentication">
            <summary>
            	Checks the IsAuthenticated flag, and if false, calls Authenticate;
            
            	Then checks the service channel, and if null, calls CreateNewChannel;
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceProxy`1.AuthenticateDeviceCore">
            <summary>
            	Allows the client to authenticate the device for LiveId use
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceProxy`1.AuthenticateCore">
            <summary>
            	if the server is in Claims mode (including Live), will attempt to request a new token, otherwise will just mark the proxy as authenticated,
            	so that the default security conversation will take place.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceProxy`1.CloseChannel(System.Boolean)">
            <summary>
            	If there is a valid channel and it needs to be closed either forcefully or automatically, this will close it, otherwise, it will take no action.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceProxy`1.ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint,Microsoft.Xrm.Sdk.Client.ServiceProxy{`0})">
            <summary>
            	Updates the endpoint to have the correct quotas
            </summary>
            <param name = "endpoint">Endpoint that needs to be updated</param>
            <param name = "serviceProxy">The service proxy instance</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceProxy`1.CreateNewServiceChannel">
            <summary>
            	Creates a new Service Channel using either the current credentials or security token.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.AuthenticationHelpers">
            <summary>
            Provides helper methods for generalized authentication context querying
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.AuthenticationHelpers.ShouldAuthenticateWithLiveId``1(Microsoft.Xrm.Sdk.Client.IServiceManagement{``0},System.ServiceModel.Description.ClientCredentials)">
            <summary>
            /// This method is deprecated and will always return false.
            </summary>
            <typeparam name="TService">The service contract type</typeparam>
            <param name="serviceConfiguration">The serviceConfiguration interface instance</param>
            <param name="clientCredentials">The client credentials passed to the method</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.AuthenticationHelpers.ShouldAuthenticateWithLiveId``1(Microsoft.Xrm.Sdk.Client.IServiceConfiguration{``0},System.ServiceModel.Description.ClientCredentials)">
            <summary>
            This method is deprecated and will always return false.
            </summary>
            <typeparam name="TService">The service contract type</typeparam>
            <param name="serviceConfiguration">The serviceConfiguration interface instance</param>
            <param name="clientCredentials">The client credentials passed to the method</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.AuthenticationHelpers.ShouldAuthenticateWithLiveId``1(Microsoft.Xrm.Sdk.Client.ServiceConfiguration{``0},System.ServiceModel.Description.ClientCredentials)">
            <summary>
            This method is deprecated and will always return false.
            </summary>
            <typeparam name="TService">The service contract type</typeparam>
            <param name="serviceConfiguration">The serviceConfiguration interface instance</param>
            <param name="clientCredentials">The client credentials passed to the method</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.AuthenticationPolicyImporter.ImportSecurityPolicy(System.ServiceModel.Description.MetadataImporter,System.ServiceModel.Description.PolicyConversionContext)">
            <summary>
            This methods imports a security policy. 
            For OnlineFederation and LiveId auth type, it updates the metadataImporter to use a custom MetadataExchangeClient which does not automatically resolve references.
            This reduces the number of calls to ACS MEX service that we did with the previous implementation.
            </summary>
            <param name="metadataImporter">The MetadataImporter object</param>
            <param name="context">The PolicyConversionContext object</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.AuthenticationPolicyImporter.ImportSecurityPolicyWithoutMetadata(System.ServiceModel.Description.MetadataImporter,System.ServiceModel.Description.PolicyConversionContext)">
            <summary>
            This methods imports a security policy by using a custom MetadataExchangeClient object which has ResolveMetadataReferences propery set to false
            This method was added to reduce the number of calls done to ACS MEX service for OnlineFederation authentication.
            </summary>
            <param name="metadataImporter">The MetadataImporter object</param>
            <param name="context">The PolicyConversionContext object</param>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.EndpointSwitchEventArgs">
            <summary>
            The arguments for endpoint switched events.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IdentityProviderLookup.GetIdentityProvider(System.Uri,System.Uri,System.String)">
            <summary>
            This lookup returns the service root url for the identity provider associated to the user principal name.
            </summary>
            <param name="host">The OrgID host to query</param>
            <param name="orgIdServiceRoot">The OrgID service root for non ADFS requests</param>
            <param name="userPrincipalName">The domainSuffix to look for.  May be empty.</param>
            <returns>The service root of the corresponding online provider, or the local ADFS WS-Trust metadata endpoint.</returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.IEndpointSwitch">
            <summary>
            This interface provides support for failover event handling.  This is only enabled in on-line scenarios.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.CurrentIssuer">
            <summary>
            	If there is a CurrentServiceEndpoint and the Service has been configured for claims (Federation,) then this
            	is the endpoint used by the Secure Token Service (STS) to issue the trusted token.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.CurrentServiceEndpoint">
            <summary>
            	This defaults to the first avaialble endpoint in the ServiceEndpoints dictionary if it has not been set.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.AuthenticationType">
            <summary>
            	Identifies whether the constructed service is using Claims (Federation) authentication or AD.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.ServiceEndpoints">
            <summary>
            	Contains the list of urls and binding information required in order to make a call to a WCF service.  If the service is configured
            	for On-Premise use only, then the endpoint(s) contained within will NOT require the use of an Issuer Endpoint on the binding.
            
            	If the service is configured to use Claims (Federation,) then the binding on the service endpoint MUST be configured to use
            	the appropriate Issuer Endpoint, i.e., UserNamePassword, Kerberos, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.IssuerEndpoints">
            <summary>
            	The following property contains the urls and binding information required to use a configured Secure Token Service (STS)
            	for issuing a trusted token that the service endpoint will trust for authentication.
            
            	The available endpoints can vary, depending on how the administrator of the STS has configured the server, but may include 
            	the following authentication methods:
            
            	1.  UserName and Password
            	2.  Kerberos
            	3.  Certificate
            	4.  Asymmetric Token
            	5.  Symmetric Token
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.CrossRealmIssuerEndpoints">
            <summary>
            	Contains the STS IssuerEndpoints as determined dynamically by calls to AuthenticateCrossRealm.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.CreateChannelFactory">
            <summary>
            	Creates a client factory that uses the default Kerberos credentials (i.e., clientAuthenticationType = ClientAuthenticationType.Kerberos.)
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.CreateChannelFactory(Microsoft.Xrm.Sdk.Client.ClientAuthenticationType)">
            <summary>
            	Creates a channel factory that uses the default Kerberos credentials (clientAuthenticationType = ClientAuthenticationType.Kerberos, )
            	<example>
            		var channel = channelFactory.CreateChannel();
            	</example>
            	or allows the usage of a Security Token during channel creation later (clientAuthenticationType = ClientAuthenticationType.SecurityToken )
            	<example>
            		var channel = channelFactory.CreateChannelWithIssuedToken(SecurityToken);
            	</example>
            </summary>
            <param name = "clientAuthenticationType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.CreateChannelFactory(Microsoft.Xrm.Sdk.Client.TokenServiceCredentialType)">
            <summary>
            	Creates a channel factory that uses the credential type used when getting the Token Response
            </summary>
            <param name = "clientCredentials"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.CreateChannelFactory(System.ServiceModel.Description.ClientCredentials)">
            <summary>
            	Creates a channel factory that supports passing the client credentials, regardless of whether in Federation Authentication mode or not.
            
            	Windows and UserName are the currently supported settings.
            </summary>
            <param name = "clientCredentials"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.Authenticate(System.ServiceModel.Description.ClientCredentials)">
            <summary>
            	Authenticates against the Trusted CRM Secure Token Service using client credentials supporting both
            	Username and Windows (when in Federation mode) and returns a wrapper with both the RequestSecurityTokenResponse and the SecurityToken.
            
            	Will return null when in ActiveDirectory mode.
            </summary>
            <param name = "clientCredentials">A ClientCredentialsinstance with the Windows credentials or the UserName/Password</param>
            <returns>SecurityTokenResponse that contains both the RTSR and the SecurityToken</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.Authenticate(System.IdentityModel.Tokens.SecurityToken)">
            <summary>
            	Authenticates against the Trusted CRM Secure Token Service using a Security Token retrieved from 
            	an Identity Provider other than the Trusted CRM Secure Token Service (when in Federation mode) and 
            	returns a wrapper with both the RequestSecurityTokenResponse and the SecurityToken.
            </summary>
            <param name = "securityToken">A Security Token issued from an Identity Provider.</param>
            <returns>SecurityTokenResponse that contains both the RTSR and the SecurityToken</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.AuthenticateCrossRealm(System.ServiceModel.Description.ClientCredentials,System.String,System.Uri)">
            <summary>
            	Authenticates against a remote Secure Token Service using client credentials supporting both
            	Username and Windows (when in Federation mode) and returns a wrapper with both the RequestSecurityTokenResponse and the SecurityToken.
            
            	Will throw a NotSupportedException when in ActiveDirectory mode.
            </summary>
            <param name = "clientCredentials">A ClientCredentials instance with the Windows credentials or the UserName/Password</param>
            <param name = "appliesTo">The identifier of the STS to authenticate on behalf of</param>
            <param name = "crossRealmSts">The uri of the cross realm STS metadata endpoint</param>
            <returns>SecurityTokenResponse that contains both the RTSR and the SecurityToken</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.AuthenticateCrossRealm(System.IdentityModel.Tokens.SecurityToken,System.String,System.Uri)">
            <summary>
            	Authenticates against a remote Secure Token Service using a Security Token retrieved from 
            	an Identity Provider (when in Federation mode) and 
            	returns a wrapper with both the RequestSecurityTokenResponse and the SecurityToken.
            
            	Will throw a NotSupportedException when in ActiveDirectory mode.
            </summary>
            <param name = "securityToken">A Security Token issued from an Identity Provider.</param>
            ///
            <param name = "appliesTo">The identifier of the STS to authenticate on behalf of</param>
            <param name = "crossRealmSts">The uri of the cross realm STS metadata endpoint</param>
            <returns>SecurityTokenResponse that contains both the RTSR and the SecurityToken</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.Authenticate(System.ServiceModel.Description.ClientCredentials,Microsoft.Xrm.Sdk.Client.SecurityTokenResponse)">
            <summary>
            	Authenticates a user against Windows Live ID.  The UserName.UserName and UserName.Password MUST be populated
            	with the user name and password, and the SecurityToken must be set to a valid device security token.
            </summary>
            <param name = "clientCredentials">A ClintCredentials instance with the UserName.UserName and UserName.Password set.</param>
            <param name = "deviceSecurityToken">A Security Token response received from authenticating the user's device with Windows Live ID.</param>
            <returns>SecurityTokenResponse that contains both the RTSR and the SecurityToken</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IServiceConfiguration`1.AuthenticateDevice(System.ServiceModel.Description.ClientCredentials)">
            <summary>
            	Authenticates a registered device against Windows Live ID.  The UserName.UserName and UserName.Password MUST be populated
            	with the device name and password.
            </summary>
            <param name = "clientCredentials">A ClintCredentials instance with the UserName.UserName and UserName.Password set.</param>
            <returns>SecurityTokenResponse that contains both the RTSR and the SecurityToken</returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.IServiceManagement`1">
            <summary>
            This interface does NOT provide support for LiveID based authentication.
            </summary>
            <typeparam name="TService"></typeparam>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.IServiceManagement`1.CurrentServiceEndpoint">
            <summary>
            	This defaults to the first avaialble endpoint in the ServiceEndpoints dictionary if it has not been set.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.IServiceManagement`1.AuthenticationType">
            <summary>
            	Identifies whether the constructed service is using Claims (Federation) authentication or AD.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.IServiceManagement`1.IssuerEndpoints">
            <summary>
            	The following property contains the urls and binding information required to use a configured Secure Token Service (STS)
            	for issuing a trusted token that the service endpoint will trust for authentication.
            
            	The available endpoints can vary, depending on how the administrator of the STS has configured the server, but may include 
            	the following authentication methods:
            
            	1.  UserName and Password
            	2.  Kerberos
            	3.  Certificate
            	4.  Asymmetric Token
            	5.  Symmetric Token
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.IServiceManagement`1.CrossRealmIssuerEndpoints">
            <summary>
            	Contains the STS IssuerEndpoints as determined dynamically by calls to AuthenticateCrossRealm.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IServiceManagement`1.CreateChannelFactory">
            <summary>
            	Creates a client factory that uses the default Kerberos credentials (i.e., clientAuthenticationType = ClientAuthenticationType.Kerberos.)
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IServiceManagement`1.CreateChannelFactory(Microsoft.Xrm.Sdk.Client.ClientAuthenticationType)">
            <summary>
            	Creates a channel factory that uses the default Kerberos credentials (clientAuthenticationType = ClientAuthenticationType.Kerberos, )
            	<example>
            		var channel = channelFactory.CreateChannel();
            	</example>
            	or allows the usage of a Security Token during channel creation later (clientAuthenticationType = ClientAuthenticationType.SecurityToken )
            	<example>
            		var channel = channelFactory.CreateChannelWithIssuedToken(SecurityToken);
            	</example>
            </summary>
            <param name = "clientAuthenticationType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IServiceManagement`1.CreateChannelFactory(Microsoft.Xrm.Sdk.Client.TokenServiceCredentialType)">
            <summary>
            	Creates a channel factory that uses the credential type used when getting the Token Response
            </summary>
            <param name = "clientCredentials"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IServiceManagement`1.CreateChannelFactory(System.ServiceModel.Description.ClientCredentials)">
            <summary>
            	Creates a channel factory that supports passing the client credentials, regardless of whether in Federation Authentication mode or not.
            
            	Windows and UserName are the currently supported settings.
            </summary>
            <param name = "clientCredentials"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.IServiceManagement`1.Authenticate(Microsoft.Xrm.Sdk.Client.AuthenticationCredentials)">
            <summary>
            	
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.OnlineFederationPolicyConfiguration">
            <summary>
            Used to manage the LiveFederation configuration.  In this configuration, we will have a true STS, but must be able to talk to 
            MFG for initial authentication when not using HomeRealmUrl.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.OrganizationServiceConfiguration.EnableProxyTypes">
            <summary>
            This method will enable support for the default strong proxy types. 
            
            If you are using a shared Service Configuration instance, you must be careful if using 
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.OrganizationServiceConfiguration.EnableProxyTypes(System.Reflection.Assembly)">
            <summary>
            This method will enable support for the strong proxy types exposed in the passed assembly.
            <param name="assembly">The assembly that will provide support for the desired strong types in the proxy.</param>
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.RealmInfo">
            <summary>
            Must be public for de-serialization
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.AuthenticateOnlineFederationInternal(Microsoft.Xrm.Sdk.Client.AuthenticationCredentials)">
            <summary>
            Supported matrix:
            1.  Security Token Response populated: We will submit the token to Org ID to exchange for a CRM token.
            2.  Credentials passed.  
            		a.  The UserPrincipalName MUST be populated if the Username/Windows username is empty AND the Home Realm Uri is null.
            		a.  If the Home Realm 
            </summary>
            <param name="authenticationCredentials"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.AuthenticateFederatedTokenWithOrgIdForCRM(Microsoft.Xrm.Sdk.Client.AuthenticationCredentials)">
            <summary>
            Authenticates a federated token with OrgID to retrieve a token for CRM
            </summary>
            <param name="authenticationCredentials"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.AuthenticateWithADFSForOrgId(Microsoft.Xrm.Sdk.Client.AuthenticationCredentials,System.Uri)">
            <summary>
            Authenticates with ADFS to retrieve a federated token to exchange with OrgId for CRM
            </summary>
            <param name="authenticationCredentials"></param>
            <param name="identifier"></param>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.ClaimsEnabledService">
            <summary>
            Returns true if the AuthenticationType == Federation or LiveFederation
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.SetAuthenticationConfiguration">
            <summary>
            If there is no binding, there is nothing to do.  Otherwise, import the XRM Policy elements and set the issuers if claims.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.CurrentServiceEndpoint">
            <summary>
            This defaults to the first avaialble endpoint in the ServiceEndpoints dictionary if it has not been set.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.CurrentIssuer">
            <summary>
            If there is a CurrentServiceEndpoint and the Service has been configured for claims (Federation,) then this
            is the endpoint used by the Secure Token Service (STS) to issue the trusted token.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.AuthenticationType">
            <summary>
            Identifies whether the constructed service is using Claims (Federation) authentication or legacy AD/RPS.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.ServiceEndpoints">
            <summary>
            Contains the list of urls and binding information required in order to make a call to a WCF service.  If the service is configured
            for On-Premise use only, then the endpoint(s) contained within will NOT require the use of an Issuer Endpoint on the binding.
            
            If the service is configured to use Claims (Federation,) then the binding on the service endpoint MUST be configured to use
            the appropriate Issuer Endpoint, i.e., UserNamePassword, Kerberos, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.IssuerEndpoints">
            <summary>
            The following property contains the urls and binding information required to use a configured Secure Token Service (STS)
            for issuing a trusted token that the service endpoint will trust for authentication.
            
            The available endpoints can vary, depending on how the administrator of the STS has configured the server, but may include 
            the following authentication methods:
            
            1.  UserName and Password
            2.  Kerberos
            3.  Certificate
            4.  Asymmetric Token
            5.  Symmetric Token
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.CrossRealmIssuerEndpoints">
            <summary>
            Contains the STS IssuerEndpoints as determined dynamically by calls to AuthenticateCrossRealm.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.AuthenticateCrossRealm(System.ServiceModel.Description.ClientCredentials,System.String,System.Uri)">
            <summary>
            Authenticates based on the client credentials passed in.
            </summary>
            <param name="clientCredentials">The standard ClientCredentials</param>
            <param name="appliesTo"></param>
            <param name="crossRealmSts"></param>
            <returns>RequestSecurityTokenResponse</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.AuthenticateCrossRealm(System.IdentityModel.Tokens.SecurityToken,System.String,System.Uri)">
            <summary>
            Authenticates based on the security token passed in.
            </summary>
            <param name="securityToken"></param>
            <param name="appliesTo"></param>
            <param name="crossRealmSts"></param>
            <returns>RequestSecurityTokenResponse</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.Authenticate(System.ServiceModel.Description.ClientCredentials)">
            <summary>
            Authenticates based on the client credentials passed in.
            </summary>
            <param name="clientCredentials">The standard ClientCredentials</param>
            <returns>RequestSecurityTokenResponse</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.Authenticate(System.ServiceModel.Description.ClientCredentials,System.Uri,System.String)">
            <summary>
            Authenticates based on the client credentials passed in.
            </summary>
            <param name="clientCredentials"></param>
            <param name="uri"></param>
            <param name="keyType">Optional.  Can be set to Bearer if bearer token required</param>
            <returns>RequestSecurityTokenResponse</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.Authenticate(System.IdentityModel.Tokens.SecurityToken)">
            <summary>
            Authenticates based on the security token passed in.
            </summary>
            <param name="securityToken"></param>
            <returns>RequestSecurityTokenResponse</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.Authenticate(System.IdentityModel.Tokens.SecurityToken,System.Uri,System.String)">
            <summary>
            Authenticates based on the security token passed in.
            </summary>
            <param name="securityToken"></param>
            <param name="uri"></param>
            <param name="keyType">Optional.  Can be set to Bearer if bearer token required</param>
            <returns>RequestSecurityTokenResponse</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.AuthenticateDevice(System.ServiceModel.Description.ClientCredentials)">
            <summary>
            This will default to LiveID auth when on-line.
            </summary>
            <param name="clientCredentials"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.Authenticate(System.ServiceModel.Description.ClientCredentials,Microsoft.Xrm.Sdk.Client.SecurityTokenResponse)">
            <summary>
            This will default to LiveID auth when on-line.
            </summary>
            <param name="clientCredentials"></param>
            <param name="deviceTokenResponse"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.Authenticate(System.ServiceModel.Description.ClientCredentials,Microsoft.Xrm.Sdk.Client.SecurityTokenResponse,System.String)">
            <summary>
            This will default to LiveID auth when on-line.
            </summary>
            <param name="clientCredentials"></param>
            <param name="deviceTokenResponse"></param>
            <param name="keyType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceConfiguration`1.Issue(Microsoft.Xrm.Sdk.Client.AuthenticationCredentials)">
            <summary>
            This is the method that actually creates the trust channel factory and issues the request for the token.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.ServiceMetadataUtility">
            <summary>
            	Handles retrieving/making use of service metadata information.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceMetadataUtility.GetSDKVersionNumberFromAssembly">
            <summary>
            Gets version of current assembly which is the version of the SDK
            </summary>
            <returns>The version number</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Client.ServiceMetadataUtility.AddSecurityBindingToPolicyImporter(System.ServiceModel.Description.WsdlImporter)">
            <summary>
            Returns a list of policy import extensions in the importer parameter and adds a SecurityBindingElementImporter if not already present in the list.
            </summary>
            <param name="importer">The WsdlImporter object</param>
            <returns>The list of PolicyImportExtension objects</returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Client.ServiceUrls">
            <summary>
            Contains the available urls for a service endpoint, and whether they were generated from querying the alternate endpoint.ye
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.DataCollection`1">
            <summary>
            XRM Collection object
            </summary>
            <typeparam name="T">The type of elements in the collection</typeparam>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DataCollection`1.AddRange(`0[])">
            <summary>
            Add items to the collection
            </summary>
            <param name="items"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DataCollection`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Add items from another collection
            </summary>
            <param name="items"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DataCollection`1.ToArray">
            <summary>
            Converts the collection into an array
            </summary>
            <returns>Array for the collection</returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.DataCollection`2">
            <summary>
            Base class for Dictionary types in XRM SDK
            </summary>
            <typeparam name="TKey"></typeparam>
            <typeparam name="TValue"></typeparam>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DataCollection`2.Add(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
            Adds an item to the collection
            </summary>
            <param name="item">Item to be added</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DataCollection`2.AddRange(System.Collections.Generic.KeyValuePair{`0,`1}[])">
            <summary>
            Adds the given items to the collection
            </summary>
            <param name="items"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DataCollection`2.AddRange(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{`0,`1}})">
            <summary>
            Adds the given items to the collection
            </summary>
            <param name="items"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DataCollection`2.Add(`0,`1)">
            <summary>
            Adds an item to the collection
            </summary>
            <param name="key">Key of the item</param>
            <param name="value">Value to be added</param>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.DataCollection`2.Item(`0)">
            <summary>
            Property accessor value
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DataCollection`2.Clear">
            <summary>
            Clears the items from the collection
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DataCollection`2.Contains(`0)">
            <summary>
            Checks if an item exists in the collection
            </summary>
            <param name="key">Key for the item</param>
            <returns>True if the item is contained in the collection</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DataCollection`2.Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
            Determines whether the collection contains a specific value.
            </summary>
            <param name="item">The object to locate in the collection</param>
            <returns>True if item is found in the collection; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DataCollection`2.TryGetValue(`0,`1@)">
            <summary>
            Gets the value associated with the specified key.
            </summary>
            <param name="key">Key for the item</param>
            <param name="value">When this method returns, the value associated with the specified key, if the key is found; otherwise, the default value for the type of the value parameter. This parameter is passed uninitialized.</param>
            <returns>True if the DataCollection contains an element with the specified key; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DataCollection`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
            <summary>
            Copies the elements of the collection to an Array, starting at a particular Array index.
            </summary>
            <param name="array">The one-dimensional Array that is the destination of the elements copied from the collection. The Array must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in array at which copying begins.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DataCollection`2.ContainsKey(`0)">
            <summary>
            Checks if an item exists in the dictionary
            </summary>
            <param name="key">Key for the item</param>
            <returns>True if the item is contained in the dictionary</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DataCollection`2.Remove(`0)">
            <summary>
            Removes an item from the collection
            </summary>
            <param name="key"></param>
            <returns>True if the item was removed</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DataCollection`2.Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary>
            Removes the first occurrence of a specific object from the collection.
            </summary>
            <param name="item">The object to remove from the collection.</param>
            <returns>True if item was successfully removed from the collection; otherwise, false. This method also returns false if item is not found in the original collection.</returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.DataCollection`2.Count">
            <summary>
            Number of elements in the collection
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "P:Microsoft.Xrm.Sdk.DataCollection`2.Keys" -->
        <!-- Badly formed XML comment ignored for member "P:Microsoft.Xrm.Sdk.DataCollection`2.Values" -->
        <member name="P:Microsoft.Xrm.Sdk.DataCollection`2.IsReadOnly">
            <summary>
            Gets a value indicating whether the collection is read-only.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.AttributeCollection">
            <summary>
            Represents collection of attribute values
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.FormattedValueCollection">
            <summary>
            Represents collection of attribute values formatted using user settings
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.ErrorDetailCollection">
            <summary>
            collection to provide error details
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.ParameterCollection">
            <summary>
            collection of fields
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.ParameterCollection.TryGetValue``1(System.String,``0@)">
            <summary>
            Gets the value associated with the specified key
            </summary>
            <typeparam name="T"></typeparam>
            <param name="key">The key of the value to get.</param>
            <param name="value">When this method returns, contains the value associated with the specified key, if the key is found and value is of type T; otherwise, the default value for the type of T.</param>
            <returns>true if the ParameterCollection contains an element with the specified key and is of type T; otherwise, false.</returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.EntityImageCollection">
            <summary>
            Collection of related entity ids
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.OptionSetValueCollection">
            <summary>
            collection for OptionSetValue
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.BaseServiceFault">
            <summary>
            Fault contract for IOrganizationService service contract.
            Use the OrganizationServiceFault contract to handle CRM specific errors in SDK client.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.BaseServiceFault.Message">
            <summary>
            Gets or sets contains the error message included by CRM platform. This string is always in English.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.BaseServiceFault.ErrorCode">
            <summary>
            Gets or sets predefined error code returned by CRM. Refer to SDK helpers and documentation for list of codes.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.BaseServiceFault.HelpLink">
            <summary>
            Gets or sets url that points to a knowledgebase article or similar content to help with the error raised.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.BaseServiceFault.Timestamp">
            <summary>
            Gets or sets server time in UTC when the error occured.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.BaseServiceFault.ActivityId">
            <summary>
            Gets or sets telemetry ActivityId when the error occurred
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.BaseServiceFault.ErrorDetails">
            <summary>
            Gets or sets additional data returned from CRM. The contents of this property is error specific.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.DiscoveryServiceFault">
            <summary>
            Fault contract for IDiscoveryService service contract.
            Use the DiscoveryServiceFault contract to handle CRM specific errors in SDK client.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.DiscoveryServiceFault.InnerFault">
            <summary>
            Gets or sets get the inner exception information that caused the fault
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.OrganizationServiceFault">
            <summary>
            Fault contract for IOrganizationService service contract.
            Use the OrganizationServiceFault contract to handle CRM specific errors in SDK client.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.OrganizationServiceFault.TraceText">
            <summary>
            Gets or sets diagnostic information added by custom plugins registered for the organization.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.OrganizationServiceFault.InnerFault">
            <summary>
            Gets or sets get the inner exception information that caused the fault
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.OrganizationServiceFault.OriginalException">
            <summary>
            Gets or sets original Exception that triggered this FaultException.
            </summary> 
        </member>
        <member name="P:Microsoft.Xrm.Sdk.OrganizationServiceFault.ExceptionSource">
            <summary>
            Gets or sets exception source, to map where original exception happened. This can be used
            to track where a chain of remote exception start failing.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.OrganizationServiceFault.ExceptionRetriable">
            <summary>
            Gets or sets a value indicating whether indicates that the exception is safe to retry
            </summary> 
        </member>
        <member name="T:Microsoft.Xrm.Sdk.ExecuteTransactionFault">
            <summary>
            Fault contract for IOrganizationService service contract.
            Use the ExecuteTransactionFault contract to handle ExecuteTransaction Sdk specific errors in SDK client.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.ExecuteTransactionFault.FaultedRequestIndex">
            <summary>
            Gets or sets the zero-based index of the request that failed during execution.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.OrganizationRequest">
            <summary>
            Makes a request to XRM services to perform a specific action.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.OrganizationRequest.RequestName">
            <summary>
            Gets or sets name of the message represented by the request.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.OrganizationRequest.Item(System.String)">
            <summary>
            Indexer property for the Parameters collection
            </summary>
            <param name="parameterName">Name of the parameter</param>
            <returns>Parameter stored in the Parameters collection with the given parameter name</returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.OrganizationRequest.Parameters">
            <summary>
            Gets or sets parameters for the request.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.OrganizationRequest.RequestId">
            <summary>
            Gets or sets provides tracking mechanism for actions resulting from a request.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.OrganizationResponse">
            <summary>
            Returns results from performing an action using a Request object.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.OrganizationResponse.ResponseName">
            <summary>
            Gets or sets name of the message represented by the request that was processed.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.OrganizationResponse.Item(System.String)">
            <summary>
            Indexer property for the Results collection
            </summary>
            <param name="parameterName">Name of the parameter</param>
            <returns>Parameter stored in the Results collection with the given parameter name</returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.OrganizationResponse.Results">
            <summary>
            Gets or sets results from processing a request.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.TriggerDefinitions">
            <summary>
            Data contract for the Reconciled Entity File pointers response
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.TriggerDefinitions.Definitions">
            <summary>
            List of Definitions
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.TriggerDefinition">
            <summary>
            Data contract for the Reconciled Entity File pointers response
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.TriggerDefinition.DisplayName">
            <summary>
            Display name of the trigger definition
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.TriggerDefinition.PrimaryEntityLogicalName">
            <summary>
            primary entity logical name of the trigger definition
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.TriggerDefinition.PrimaryEntityDisplayName">
            <summary>
            primary entity displayname of the trigger definition
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.TriggerDefinition.SdkMessageId">
            <summary>
            Sdk message id of the trigger definition
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.TriggerDefinition.SdkMessageName">
            <summary>
            sdk message name of the trigger definition 
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.TriggerDefinition.CatalogUniqueName">
            <summary>
            catalog unique name of the trigger definition 
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.TriggerDefinition.CatalogDisplayName">
            <summary>
            catalog display name of the trigger definition
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.TriggerDefinition.SubCatalogUniqueName">
            <summary>
            sub-catalog unique name of the trigger definition 
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.TriggerDefinition.SubCatalogDisplayName">
            <summary>
            sub-catalog unique name of the trigger definition 
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.TriggerDefinition.EventType">
            <summary>
            event type of the trigger definition 
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.TriggerDefinition.PrimaryEntityId">
            <summary>
            Primary entity id 
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.TriggerDefinition.CatalogId">
            <summary>
            Catalog Id
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.TriggerDefinition.SubCatalogId">
            <summary>
            Sub catalog Id
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.DateTimeBehaviorConversionRule">
            <summary>
            Class to verify and translate ConversionRule param value from ConvertDateAndTimeBehaviorRequest to the correct type
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.DateTimeBehaviorConversionRule.op_Implicit(System.String)~Microsoft.Xrm.Sdk.DateTimeBehaviorConversionRule">
            <summary>
            Implicity converts a string to value
            </summary>
            <param name="conversionRule">String value to convert</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.DependencySummary">
            <summary>
            Dependency Summary
            Contains selected field from dependency entity with additional joined data
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.EntityStates">
            <summary>
            Represents the enumeration that identifies the state of an entity being tracked by the <see cref="!:OrganizationServiceContext"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.EntityDescriptor">
            <summary>
            A container of entity changes performed by the <see cref="!:OrganizationServiceContext"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.LinkDescriptor">
            <summary>
            A container of relationship changes performed by the <see cref="!:OrganizationServiceContext"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.RetrieveUserIdByExternalIdRequest.ExternalId">
            <summary>
            The external id that is associated to a crmuserid
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.RetrieveUserIdByExternalIdRequest.OrganizationName">
            <summary>
            The organization name to retrieve the CrmUserId for
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Discovery.RetrieveUserIdByExternalIdResponse">
            <summary>
            Response to the RetrieveCrmUserIdByExternalIdRequest which returns the CrmUserId of external id provided.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.RetrieveUserIdByExternalIdResponse.UserId">
            <summary>
            The User Id for the provided external id.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Discovery.OrganizationDetailCollection">
            <summary>
            Contains the result returned by Validate() method of an IInputValidator object.
            TODO - consider renaming this class.
            These types should match the types in src/SDK/Core/OrganizationDetail.cs.
            The converters in src/SDK/Core/OrganizationDetail.cs should be updated if the classes are changed in either file.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Discovery.ClientPatchInfo">
            <summary>
            A strongly typed property bag that gives details about each patch that needs to be installed
            on the client.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.ClientPatchInfo.PatchId">
            <summary>
            The PatchId of the patch that needs to be installed.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.ClientPatchInfo.Title">
            <summary>
            The Title of the Patch (eg: KB 1234)
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.ClientPatchInfo.Description">
            <summary>
            A brief description of the patch (and what problem it resolves).
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.ClientPatchInfo.IsMandatory">
            <summary>
            A bit to indicate if the patch is mandatory.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.ClientPatchInfo.Depth">
            <summary>
            The depth order in which this patch needs to be installed. Lower depth patches should be installed
            first.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.ClientPatchInfo.LinkId">
            <summary>
            The link Identifier that is appended to the base URL when download patches.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Discovery.EndUserNotificationClient">
            <summary>
            Notifications clients
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Discovery.ClientInfo">
            <summary>
            A property bag encapsulating all the properties of the client that is requesting patches.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.ClientInfo.PatchIds">
            <summary>
            List of patches that the client already has installed.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.ClientInfo.ClientType">
            <summary>
            The type of client which is querying for patches. (Desktop\Laptop\DM Client).
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.ClientInfo.UserId">
            <summary>
            The CRM user id of the client.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.ClientInfo.OrganizationId">
            <summary>
            The Organization Id for which the client has been configured.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.ClientInfo.LanguageCode">
            <summary>
            The language code for which the client has been configured.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.ClientInfo.OfficeVersion">
            <summary>
            The Office Version installed on the client machine.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.ClientInfo.OSVersion">
            <summary>
            The operating system version that the client is running.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Discovery.ClientInfo.CrmVersion">
            <summary>
            The CRM version that the client is running.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Discovery.SdkUtilities.SecureStringToString(System.Security.SecureString)">
            <summary>
            Converts a secure string to string.
            </summary>
            <param name="value">Secure string</param>
            <returns>String</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Discovery.SdkUtilities.StringToSecureString(System.String)">
            <summary>
            Converts a string to secure string.
            </summary>
            <param name="value">String</param>
            <returns>Read-only Secure string</returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.EmailEngagementAggregate">
            <summary>
            EmailEngagementAggregate holds the counts for email opens, links clicks, attachment opens, replies
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.EmailEngagementAggregate.TotalEmails">
            <summary>
            Total Email for the entity
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.EmailEngagementAggregate.TotalFollowedEmails">
            <summary>
            Total followed Emails for the entity
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.EmailEngagementAggregate.TotalEmailOpens">
            <summary>
            Total Email Opens for the entity
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.EmailEngagementAggregate.TotalEmailReplies">
            <summary>
            Total Email Replies for the Entity
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.EmailEngagementAggregate.TotalAttachmentOpens">
            <summary>
            Total Attachment OPens for the entity
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.EmailEngagementAggregate.TotalLinkClicks">
            <summary>
            Total Links clicked for the entity
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Entity">
            <summary>
            Represents an instance of an entity.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Entity.LogicalName">
            <summary>
            Specifies the name of the entity.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Entity.Id">
            <summary>
            Specifies the unique id of the entity;
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Entity.Attributes">
            <summary>
            Specifies a collection of attribute name/value pairs for the entity.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Entity.KeyAttributes">
            <summary>
            Specifies a collection of KeyAttribute name/value pairs for the alternate key.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Entity.HasLazyFileAttribute">
            <summary>
            Gets and sets a flag for a lazy file attribute value. This flag is used
            when large files are not routed to plugins.
            </summary>
            <returns>True if the entity has a lazy file attribute, otherwise false.</returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Entity.LazyFileAttributeKey">
            <summary>
            Gets and sets the lazy file attribute name;
            </summary>
            <returns>The lazy file attribute key.</returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Entity.LazyFileAttributeValue">
            <summary>
            Gets and sets the lazy file attribute value;
            </summary>
            <returns>A lazy file attribute value.</returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Entity.LazyFileSizeAttributeKey">
            <summary>
            Gets and sets the lazy file size attribute name;
            </summary>
            <returns>The lazy file size attribute key.</returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Entity.LazyFileSizeAttributeValue">
            <summary>
            Gets and sets the lazy file size attribute value;
            </summary>
            <returns>The lazy file size attribute value.</returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Entity.Item(System.String)">
            <summary>
            Gets or sets the value of an attribute in an entity
            </summary>
            <param name="attributeName">logical name of attribute</param>
            <returns>value of the attribute</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Entity.Contains(System.String)">
            <summary>
            Checks if value for an attribute is specified
            </summary>
            <param name="attributeName">logical name of attribute</param>
            <returns>true if the attribute has value, otherwise false</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Entity.ToEntity``1">
            <summary>
            Creates an early bound instance of current entity, and copies members to it
            If T = Entity, the instance will be copied to a late bound instance
            </summary>
            <typeparam name="T">Early bound type</typeparam>
            <returns>Early bound instance of entity</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Entity.ToEntityReference">
            <summary>
            Creates a reference for the current entity instance.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Entity.ShallowCopyTo(Microsoft.Xrm.Sdk.Entity)">
            <summary>
            Performs a shallow copy of members
            </summary>
            <param name="target"></param>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Entity.IsReadOnly">
            <summary>
            Indicates that the entity is attached to an <see cref="!:OrganizationServiceContext"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.EntityReference">
            <summary>
            Pointer to an entity instance
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.EntityRole">
            <summary>
            Enumeration representing the role of an entity in a relationship
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.ExecuteMultipleResponseItem">
            <summary>
            Wrapper class for the result of each individual Organization Request processed.
            
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.ExecuteMultipleResponseItem.RequestIndex">
            <summary>
            Gets or sets the index of the original Organization Request that was processed.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.ExecuteMultipleResponseItem.Response">
            <summary>
            Gets or sets the OrganizationResponse for the Organization Request if ReturnResponses is true.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.ExecuteMultipleResponseItem.Fault">
            <summary>
            Gets or sets if the Organization Request generates a fault, it will be returned here.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.ExecuteMultipleSettings">
            <summary>
            Provides support for passing in settings that control how an ExecuteMultiple message is processed.
            
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.ExecuteMultipleSettings.ContinueOnError">
            <summary>
            When true, then if an individual Organization Request generates an exception, the processor will add the exception to the response and continue processing.  Otherwise, any exception from the
            Organization Request will stop processing further requests.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.ExecuteMultipleSettings.ReturnResponses">
            <summary>
            When true, the response generated by an individual Organization Request will be returned, otherwise the response will be discarded.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Extensions.ContextExtensions">
            <summary>
            Provides a set of static methods for querying the <see cref="T:Microsoft.Xrm.Sdk.IPluginExecutionContext"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Extensions.ContextExtensions.InputParameterOrDefault``1(Microsoft.Xrm.Sdk.IPluginExecutionContext,System.String)">
            <summary>
            Retrieve the value of the specified input parameter, or the object type's default value.
            </summary>
            <typeparam name="T">The type of the input parameter.</typeparam>
            <param name="context">The <see cref="T:Microsoft.Xrm.Sdk.IPluginExecutionContext"/> from which to extract the input parameter.</param>
            <param name="parameterName">The name of the input parameter.</param>
            <returns>The specified input parameter cast to T, or default T if the parameter does not exist or cannot be cast to T.</returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.EntityFilePointersRequest">
            <summary>
            Data contract for the Entity File pointers to Reconcile
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.EntityFilePointersRequest.FilePointers">
            <summary>
            List of file pointers to reconcile
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.EntityFilePointersRequest.OrganizationId">
            <summary>
            Gets or sets the organization id.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.EntityFilePointersRequest.ScaleGroupId">
            <summary>
            Gets or sets the scale group id.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.EntityFilePointersResponse">
            <summary>
            Data contract for the Reconciled Entity File pointers response
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.EntityFilePointersResponse.UnReferencedFiles">
            <summary>
            List of file pointers which are not in the Org DB
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.FileSasUrlResponse.FileName">
            <summary>
            Gets or sets the file name.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.FileSasUrlResponse.FileSizeInBytes">
            <summary>
            Gets or sets the file size.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.FileSasUrlResponse.MimeType">
            <summary>
            Gets or sets the mime type.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.FileSasUrlResponse.SasUrl">
            <summary>
            Gets or sets the SAS url.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.UpdatePointersRequest">
            <summary>
            Data contract for the update pointers request.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.UpdatePointersRequest.Prefix">
            <summary>
            Gets or sets the prefix.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.UpdatePointersRequest.StoragePointer">
            <summary>
            Gets or sets the storage pointer.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.UpdatePointersRequest.FilePointer">
            <summary>
            Gets or sets the file pointer.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.UpdatePointersRequest.FileSize">
            <summary>
            Gets or sets the file size.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.UpdatePointersRequest.Otc">
            <summary>
            Gets or sets the object type code.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.UpdatePointersRequest.TargetObjectId">
            <summary>
            Gets or sets the id of the target attachment or annotation.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.UpdatePointersRequest.SetBodyToNull">
            <summary>
            Gets or sets a value indicating whether whether to set the body or documentbody column to null in the target annotation or attachment.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.UpdatePointersRequest.OrganizationId">
            <summary>
            Gets or sets the organization id.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.UpdatePointersRequest.ScaleGroupId">
            <summary>
            Gets or sets the scale group id.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.UpdatePointersRequest.ExtensionData">
            <summary>
            Gets or sets the extension data.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.AuthenticationType">
            <summary>
            Types of authentication supported by our library.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IAssemblyAuthenticationContext.AcquireToken(System.String,System.String,Microsoft.Xrm.Sdk.AuthenticationType)">
            <summary>
            Acquires security token from the authority.
            </summary>
            <param name="authority">Authority to use on AuthenticationContext</param>
            <param name="resource">Identifier of the target resource that is the recipient of the requested token.</param>
            <param name="authenticationType">Authentication type to use</param>
            <returns>
            AccessToken
            </returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.IAssemblyAuthenticationContext2">
            <summary>
            Extension to the IAssemblyAuthenticationContext to add Resolve Authority and resource for a given challenge URI
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IAssemblyAuthenticationContext2.ResolveAuthorityAndResourceFromChallengeUri(System.Uri,System.String@,System.String@)">
            <summary>
            Retrieves Authority and Resource from AAD challenge URI.
            The challenge URI is used to post a challenge to an authenticated endpoint, expecting a 401 response which includes the "www-authenticate" header containing the authority and resource to use when calling acquire token.
            </summary>
            <param name="aadChallengeUri">URI of the target service that will respond to an AAD challenge response</param>
            <param name="authority">If found, the authority to use when calling AcquireToken, else returns empty string</param>
            <param name="resource">If found, the resource to use when calling AcquireToken, else returns empty string</param>
            <returns>True if successfully populated, false if unable to retrieve</returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.IEnvironmentService">
            <summary>
            Interface for plug-ins to get environment specific information.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IEnvironmentService.GetAzureAuthorityHost">
            <summary>
            Gets the Azure authority host of current environment
            </summary>
            <returns>Azure authority host of current environment</returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.Mode">
            <summary>
            Gets mode of the processing.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.IsolationMode">
            <summary>
            Gets indicates the plug-in's isolation mode.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.MessageName">
            <summary>
            Gets name of the message being processed.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.PrimaryEntityName">
            <summary>
            Gets name of the primary entity that is associated with the message.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.RequestId">
            <summary>
            Gets requestId if specified by SDK client
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.SecondaryEntityName">
            <summary>
            Gets name of the secondary entity that is associated with the message.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.InputParameters">
            <summary>
            Gets input parameters from the caller.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.OutputParameters">
            <summary>
            Gets output parameters to be returned back to caller.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.SharedVariables">
            <summary>
            Gets temporary parameters passing between plug-ins.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.UserId">
            <summary>
            Gets id of user that plug-in is executing as.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.InitiatingUserId">
            <summary>
            Gets id of user that initiates web service.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.BusinessUnitId">
            <summary>
            Gets id of business unit that user that plug-in is executing as.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.OrganizationId">
            <summary>
            Gets id of the organization that the plug-in is executing in.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.OrganizationName">
            <summary>
            Gets unique name of organization that plug-in is executing in.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.PrimaryEntityId">
            <summary>
            Gets id of the Entity that trigged the extension.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.PreEntityImages">
            <summary>
            Gets contains a copy of the Entity prior to the platform taking action on it.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.PostEntityImages">
            <summary>
            Gets contains a copy of the Entity after the platform has taken action on it.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.OwningExtension">
            <summary>
            Gets either the Workflow record representing the Activation or the SdkMessageProcessingStep the is currently executing
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.CorrelationId">
            <summary>
            Gets id to correlate between multiple requests.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.IsExecutingOffline">
            <summary>
            Gets a value indicating whether indicates whether the plugin is running in offline mode
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.IsOfflinePlayback">
            <summary>
            Gets a value indicating whether indicates whether the plugin is running during offline playback
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.IsInTransaction">
            <summary> Gets a value indicating whether 
            Indicates whether the context is executing in transaction or not
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.OperationId">
            <summary>
            Gets the AsyncOperationId that is executing the current extension.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IExecutionContext.OperationCreatedOn">
            <summary>
            Gets the time that the AsyncOperation was created on.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IPluginExecutionContext.Stage">
            <summary>
            Gets stage of the processing.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.IPluginExecutionContext2">
            <summary>
            Extension of IPluginExecutionContext adding support for azure AAD properties
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IPluginExecutionContext2.UserAzureActiveDirectoryObjectId">
            <summary>
            Gets azure active directory object Id of user.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IPluginExecutionContext2.InitiatingUserAzureActiveDirectoryObjectId">
            <summary>
            Gets azure active directory object Id of user that initiates web service.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IPluginExecutionContext2.InitiatingUserApplicationId">
            <summary>
            Gets application Id of user that initiates the plugin (for NON-app user .. it is Guid.Empty)
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IPluginExecutionContext2.PortalsContactId">
            <summary>
            Gets contactId that got passed for the calls that come from portals client to web service (for NON-portal/Anonymous call, it is guid.Empty)
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IPluginExecutionContext2.IsPortalsClientCall">
            <summary>
            Gets a value indicating whether 'True' if the call is originated from Portals client
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.IFeatureControlService">
            <summary>
            Interface for plug-ins to retrieve FeatureControlSettings
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.IInternalEnvironmentService">
            <summary>
            Interface for plug-ins to get environment specific information. For internal use only.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Microsoft.Xrm.Sdk.IInternalEnvironmentService.GetIslandResourceName(System.String,System.String)" -->
        <!-- Badly formed XML comment ignored for member "M:Microsoft.Xrm.Sdk.IInternalEnvironmentService.GetIslandEndpointName(System.String,System.String,System.String)" -->
        <!-- Badly formed XML comment ignored for member "M:Microsoft.Xrm.Sdk.IInternalEnvironmentService.GetIslandGeoEndpointName(System.String,System.String,System.String)" -->
        <member name="T:Microsoft.Xrm.Sdk.IKeyVaultClient">
            <summary>
            KeyVault Wrapper to KeyVault API
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IKeyVaultClient.PreferredAuthType">
            <summary>
            Plugins can change the Preferred auth method used by KeyVault.
            
            By default KeyVaultProvider tries to use ClientCredential.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IKeyVaultClient.GetSecret(System.String,System.String)">
            <summary>
             Gets a secret.
            </summary>
            <param name="vaultAddress">The URL for the vault containing the secrets.</param>
            <param name="secretName">The name of the secret in the given vault.</param>
            <returns>The secret</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IKeyVaultClient.GetSecret(System.String)">
            <summary>
             Gets a secret with the default vault.
            </summary>
            <param name="secretName">The name of the secret in the default vault.</param>
            <returns>The secret</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IKeyVaultClient.SetSecret(System.String,System.String,System.String)">
            <summary>
            Sets a secret in the specified vault.
            </summary>
            <param name="vaultAddress">The URL for the vault containing the secrets.</param>
            <param name="secretName">The name of the secret in the given vault.</param>
            <param name="value">The value of the secret.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IKeyVaultClient.SetSecret(System.String,System.String)">
            <summary>
            Sets a secret in the default vault.
            </summary>
            <param name="secretName">The name of the secret in the default vault.</param>
            <param name="value">The value of the secret.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IKeyVaultClient.DeleteSecret(System.String,System.String)">
            <summary>
             Deletes a secret.
            </summary>
            <param name="vaultAddress">The URL for the vault containing the secrets.</param>
            <param name="secretName">The name of the secret in the given vault.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IKeyVaultClient.DeleteSecret(System.String)">
            <summary>
             Deletes a secret with the default vault.
            </summary>
            <param name="secretName">The name of the secret in the default vault.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IKeyVaultClient.Encrypt(System.String,System.String,System.Byte[])">
            <summary>
            Encrypts a single block of data. The amount of data that may be encrypted
            is determined by the target key type and the encryption algorithm.
            </summary>
            <param name="keyIdentifier">Full URL for the key.</param>
            <param name="algorithm">
            The algorithm. For more information on possible algorithm types, see JsonWebKeyEncryptionAlgorithm.
            For convenience the possible values are Wrapped under KeyVaultAlgorithm.
            </param>
            <param name="rawData">The raw data</param>
            <returns>Encrypted data</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IKeyVaultClient.Encrypt(System.String,System.String,System.String,System.Byte[])">
            <summary>
            Encrypts a single block of data. The amount of data that may be encrypted
            is determined by the target key type and the encryption algorithm.
            </summary>
            <param name="keyName">The name of the key.</param>
            <param name="keyVersion">The version of the key (optional, use empty string for no version).</param>
            <param name="algorithm">
            The algorithm. For more information on possible algorithm types, see JsonWebKeyEncryptionAlgorithm.
            For convenience the possible values are Wrapped under KeyVaultAlgorithm.
            </param>
            <param name="rawData">The raw data</param>
            <returns>Encrypted data</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IKeyVaultClient.Decrypt(System.String,System.String,System.Byte[])">
            <summary>
            Decrypts a single block of encrypted data
            </summary>
            <param name="keyIdentifier">Full URL for the key.</param>
            <param name="algorithm">
            The algorithm. For more information on possible algorithm types, see JsonWebKeyEncryptionAlgorithm.
            For convenience the possible values are Wrapped under KeyVaultAlgorithm.
            </param>
            <param name="encryptedData">Encrypted data</param>
            <returns>Decrypted raw data</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IKeyVaultClient.Decrypt(System.String,System.String,System.String,System.Byte[])">
            <summary>
            Decrypts a single block of encrypted data
            </summary>
            <param name="keyName">The name of the key.</param>
            <param name="keyVersion">The version of the key (optional, use empty string for no version).</param>
            <param name="algorithm">
            The algorithm. For more information on possible algorithm types, see JsonWebKeyEncryptionAlgorithm.
            For convenience the possible values are Wrapped under KeyVaultAlgorithm.
            </param>
            <param name="encryptedData">Encrypted data</param>
            <returns>Decrypted raw data</returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.ILocalConfigStore">
            <summary>
            Access for Secure data storage for CRM
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.ILocalConfigStore.SetData(System.String,System.Object)">
            <summary>
            Sets Secure data into the local secure data store.
            </summary>
            <param name="keyName">Key to use the set the data. </param>
            <param name="data">Object data that is being stored</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.ILocalConfigStore.SetData(System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Sets Secure data into the local secure data store.
            </summary>
            <param name="keyData">Key value pair collection of secure data. </param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.ILocalConfigStore.GetData``1(System.String)">
            <summary>
            Returns Data from the Secure Store
            </summary>
            <typeparam name="T">Object type of the returned data</typeparam>
            <param name="keyName">Key to use to retrieve the data</param>
            <returns>Resulting data</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.ILocalConfigStore.GetAllData">
            <summary>
            Returns a collection of key value pairs of all data that the caller can access.
            </summary>
            <returns>Key value pair collection of secure data. </returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.ILocalConfigStore.GetDataByKeyNames(System.Collections.Generic.List{System.String})">
            <summary>
            Returns a collection of key value pairs based on the collection of keys that the caller passes in.
            </summary>
            <param name="keyNames">List of keys that a caller is interested in data for</param>
            <returns>Key value pair collection of secure data. </returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.IManagedIdentityService">
            <summary>
            Interface for plug-ins to obtain access token from managed identity.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IManagedIdentityService.AcquireToken(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Acquire the access token for specified scopes.
            </summary>
            <param name="scopes">Scopes of the token</param>
            <returns>Access token for the scopes</returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.OperationStatus">
            <summary>
            Status of workflow instance after executing StopWorkflow activity.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.PluginHttpStatusCode">
            <summary>
            HTTP status codes supported to be returned due to an InvalidPluginExecutionException.
            The default HTTP status code for plugin exceptions is 400 Bad Request.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.InvalidPluginExecutionException">
            <summary>
            Defines an exception to be thrown when plug-in encounter business logic exception.
            This exception will be bubbled up to client.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.InvalidPluginExecutionException.#ctor(System.String,Microsoft.Xrm.Sdk.PluginHttpStatusCode)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.InvalidPluginExecutionException"/> class.
            Constructor allowing a specific HTTP status code to be returned when InvalidPluginExecutionException is thrown.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.InvalidPluginExecutionException.#ctor(Microsoft.Xrm.Sdk.OperationStatus,System.Int32,System.String,Microsoft.Xrm.Sdk.PluginHttpStatusCode)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.InvalidPluginExecutionException"/> class.
            Constructor allowing a specific HTTP status code to be returned when InvalidPluginExecutionException is thrown.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.InvalidPluginExecutionException.Status">
            <summary>
            Gets status of workflow instance after executing StopWorkflow activity.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.InvalidPluginExecutionException.ErrorCode">
            <summary>
            Get the error code associated with the exception
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.IOnBehalfOfTokenService">
            <summary>
            Interface for plug-ins to obtain on-behalf-of token. For internal use only.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IOnBehalfOfTokenService.AcquireToken(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Acquire the on-behalf-of token for specified scopes.
            </summary>
            <param name="scopes">Scopes of the token</param>
            <returns>On-behalf-of token for the scopes</returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.IOrganizationServiceFactory">
            <summary>
            Interface to allow plug-ins to obtain IOrganizationService.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IOrganizationServiceFactory.CreateOrganizationService(System.Nullable{System.Guid})">
            <summary>
            Creates an instance of IOrganizationService initialized with a given user id.  Null will use the 
            current user id.
            </summary>
            <param name="userId">Id of user.</param>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.IPlugin">
            <summary>
            Defines interface to be implemented by plug-ins.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IPlugin.Execute(System.IServiceProvider)">
            <summary>
            Request the plug-in to Execute business logic.
            </summary>
            <param name="serviceProvider">Interface to the Service Provider.</param>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.IPluginExceptionProvider">
            <summary>
            Interface for plug-ins to get the exception type
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.IProxyPlugin">
            <summary>
            Plugins that proxy calls to other types internally such as Sandbox plugins and InternalOperationPlugin
            need to implement this interface. This will make them have better telemetry out of the box.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.ServiceEndpointFault">
            <summary>
            Fault contract for ITwoWayServiceEndpointPlugin, IWebHttpServiceEndpointPlugin service contract.
            Use the ServiceEndpointFault contract to return specific errors back to CRM.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.ServiceEndpointFault.Message">
            <summary>
            Gets or sets contains the error message included by CRM platform. This string is always in English.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.ServiceEndpointFault.ErrorDetails">
            <summary>
            Gets or sets additional data returned from CRM. The contents of this property is error specific.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.ITokenService">
            <summary>
            Interface for plug-ins to get Token.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.ITracingService">
            <summary>
            Interface for plug-ins to provide trace information.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.IVariantConfigEnvironmentConstraintProvider">
            <summary>
              Provider of environment constraint for variant config
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IVariantConfigEnvironmentConstraintProvider.BuildConstraints">
            <summary>
            Build the environment constraints
            </summary>
            <returns>Environment constraints</returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.JsonConverters.DictionaryConverter`2">
            <summary>
            Converts Dictionary from JSON
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.JsonConverters.DictionaryConverter`2.Read(System.Text.Json.Utf8JsonReader@,System.Type,System.Text.Json.JsonSerializerOptions)">
            <summary>
            <see cref="!:JsonConverter.Read"/>
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.JsonConverters.DictionaryConverter`2.ReadArray(System.Text.Json.Utf8JsonReader@)">
            <summary>
            Reads string array from Json reader
            </summary>
            <param name="reader"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.JsonConverters.EndpointCollectionConverter">
            <summary>
            Converts EndpointCollection from JSON
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.JsonConverters.EndpointCollectionConverter.Read(System.Text.Json.Utf8JsonReader@,System.Type,System.Text.Json.JsonSerializerOptions)">
            <summary>
            <see cref="!:JsonConverter.Read"/>
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.KnownTypesFactory">
            <summary>
            Factory class used to create concrete instances of known types
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.KnownTypesFactory.Create(System.String,System.String)">
            <summary>
            Creates instance of typeName and initializes properties from propertiesJson
            </summary>
            <param name="typeName"></param>
            <param name="propertiesJson"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.KnownTypesFactory.TryCreate(System.String,System.Object@,System.String)">
            <summary>
            Creates instance of typeName and initializes properties from propertiesJson
            </summary>
            <param name="typeName"></param>
            <param name="instance"></param>
            <param name="propertiesJson"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.KnownTypesFactory.Create(System.Type,System.String)">
            <summary>
            Creates instance of type and initializes properties from propertiesJson
            </summary>
            <param name="type"></param>
            <param name="propertiesJson"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.KnownTypesFactory.TryGetPropertyValue(System.Text.Json.JsonElement,System.Reflection.PropertyInfo,System.Object@)">
            <summary>
            Try getting property value from jsonElement
            </summary>
            <param name="jsonElement"></param>
            <param name="property"></param>
            <param name="propertyValue"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.KnownTypesProvider">
            <summary>
            Class to centralize known types for the SDK contracts
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.KnownTypesProvider.RegisterAssembly(System.Reflection.Assembly)">
            <summary>
            Registers the assembly if it contain the ProxyTypesAssemblyAttribute
            </summary>
            <param name="assembly"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.KnownTypesProvider.GetProxyTypesAttribute(System.Reflection.Assembly)">
            <summary>
            Extract the ProxyTypesAssemblyAttribute on the assembly
            </summary>
            <param name="assembly"></param>
            <returns>attribute if it is defined. null otherwise</returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.KnownTypesProvider.KnownOrganizationRequestResponseTypes">
            <summary>
            Dictionary of known organization Request/Response types by qualified name
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.KnownTypesProvider.KnownOrganizationRequestResponseTypesByTypeName">
            <summary>
            Dictionary of known organization Request/Response types by type name
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.KnownTypesProvider.LoadKnownOrganizationRequestResponseTypes">
            <summary>
            Reflect and loads the knownassemblies if the knowncustomvaluetypes have not been initialized (or) if the knownassemblies list has been updated.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.KnownTypesResolver">
            <summary>
            Class to resolve known organization request/response types for the SDK contracts.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.ExpressionExtensions.ForSubtreePreorder(System.Linq.Expressions.Expression,Microsoft.Xrm.Sdk.Linq.ExpressionExtensions.ExpressionAction)">
            <summary>
            Preorder traversal of the expression.
            </summary>
            <param name="exp"></param>
            <param name="action"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.ExpressionExtensions.ForSubtreePreorder(System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,Microsoft.Xrm.Sdk.Linq.ExpressionExtensions.ExpressionAction)">
            <summary>
            Preorder traversal of the expression.
            </summary>
            <param name="exp"></param>
            <param name="parent"></param>
            <param name="action"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.ExpressionExtensions.GetSubtreePreorder(System.Linq.Expressions.Expression)">
            <summary>
            Postorder traversal of the expression.
            </summary>
            <param name="exp"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.ExpressionExtensions.FindPreorder(System.Linq.Expressions.Expression,System.Predicate{System.Linq.Expressions.Expression})">
            <summary>
            Returns the first matching expression performing a preorder traversal of the expression.
            </summary>
            <param name="exp"></param>
            <param name="match"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.ExpressionExtensions.GetMethodsPreorder(System.Linq.Expressions.Expression)">
            <summary>
            Traverses a chain of method calls in order of invocation.
            </summary>
            <param name="exp"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.ExpressionExtensions.GetMethodsPostorder(System.Linq.Expressions.Expression)">
            <summary>
            Traverses a chain of method calls in reverse order of invocation.
            </summary>
            <param name="exp"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.MemberInfoExtensions.GetCustomAttributes``1(System.Reflection.MemberInfo)">
            <summary>
            Returns a collection of custom attributes for a given member.
            </summary>
            <param name="info"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.MemberInfoExtensions.GetFirstOrDefaultCustomAttribute``1(System.Reflection.MemberInfo)">
            <summary>
            Returns the first custom attribute of a given member.
            </summary>
            <param name="info"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.MemberInfoExtensions.GetLogicalName(System.Reflection.MemberInfo)">
            <summary>
            Retrieves the attribute logical name mapped to the member.
            </summary>
            <param name="property"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Linq.PagingCookieHelper">
            <summary>
            Paging cookie helper class.
            </summary>
            <remarks> Samples of paging cookie.
            paging-cookie="<cookie page="2"><name last="C" first="C" /><accountid last="{C8CD170E-40B5-DF11-8E7B-00155D352900}" first="{C8CD170E-40B5-DF11-8E7B-00155D352900}" /></cookie>">
            paging-cookie="<cookie page="2" parentEntityId="{C8CD170E-40B5-DF11-8E7B-00155D352900}" parentAttributeName="accountid" parentEntityObjectTypeCode="1"><name last="C" first="C" /><accountid last="{C8CD170E-40B5-DF11-8E7B-00155D352900}" first="{C8CD170E-40B5-DF11-8E7B-00155D352900}" /></cookie>">
            </remarks>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.PagingCookieHelper.CreateXmlWriter(System.IO.TextWriter)">
            <summary>
            Creates an XmlWriter object with secure default property values.
            </summary>
            <param name="textWriter">Text writer</param>
            <returns>New XmlWriter object</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.PagingCookieHelper.CreateXmlReader(System.String)">
            <summary>
            Creates an XmlReader object with secure default property values.
            </summary>
            <param name="xml">The string to get the data from.</param>
            <returns>the new XmlReader object</returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Linq.IEntityQuery">
            <summary>
            Represents <see cref="T:System.Linq.IQueryable"/> queries that can hold the entity logical name it is bound to.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Linq.QueryProvider">
            <summary>
            Represents the main entry point for LINQ based CRM queries.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.QueryProvider.CreateQuery(System.Type)">
            <summary>
            Binds to the set of entities of a specified type.
            </summary>
            <param name="entityType"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Linq.QueryProvider.FilterExpressionWrapper">
            <summary>
            Class to Track the filter expression that is being build and the alias used for it.
            In LINQ layer, alias is alwasy null.
            In ODATA layer, the alias is null for qe.Criteria filter and is set to the expanded property name for conditions on expanded property.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.QueryProvider.ResetPagingNumber(System.String,System.Int32)">
            <summary>
            Reset the page number with the new value.
            </summary>
            <param name="pagingCookie"></param>
            <param name="newPage"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.QueryProvider.ResetPagingInfo(Microsoft.Xrm.Sdk.Query.PagingInfo,System.String,System.Int32)">
            <summary>
            Reset the paging info.
            </summary>
            <param name="pagingInfo">PagingInfo object to reset.</param>
            <param name="pagingCookie">Paging cookie to use, if available.</param>
            <param name="skipValue">Records to skip.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.QueryProvider.AdjustPagingInfo(Microsoft.Xrm.Sdk.OrganizationRequest,Microsoft.Xrm.Sdk.Query.QueryExpression,Microsoft.Xrm.Sdk.Linq.QueryProvider.NavigationSource,System.Boolean@)">
            <summary>
            Positions the paging cookie to start at the requested record.
            </summary>
            <param name="request"></param>
            <param name="pagingInfo"></param>
            <param name="source"></param>
            <param name="moreRecordAfterAdjust">False if no more records are found after skip.</param>
            <returns>Paging cookie strategy applied.</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.QueryProvider.AdjustEntityCollection(Microsoft.Xrm.Sdk.OrganizationRequest,Microsoft.Xrm.Sdk.Query.QueryExpression,Microsoft.Xrm.Sdk.Linq.QueryProvider.NavigationSource)">
            <summary>
            Strategy to be used only for calls with skip clause that do not return paging cookie.
            </summary>
            <param name="request"></param>
            <param name="qe"></param>
            <param name="source"></param>
            <returns>Skipped entity collection.</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.QueryProvider.Translate(System.Linq.Expressions.Expression)">
            <summary>
            Converts an expression to a <see cref="T:Microsoft.Xrm.Sdk.Query.QueryExpression"/> without executing the query.
            </summary>
            <param name="expression"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.QueryProvider.AddCondition(Microsoft.Xrm.Sdk.Linq.QueryProvider.FilterExpressionWrapper,Microsoft.Xrm.Sdk.Query.ConditionExpression,System.String)">
            <summary>
            Add condition to the filter.
            </summary>
            <param name="filter">Filter.</param>
            <param name="condition">Condition to add.</param>
            <param name="alias">Alias used to identity the condition.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.QueryProvider.GetLinkLookup(System.String,System.Collections.Generic.List{Microsoft.Xrm.Sdk.Linq.QueryProvider.LinkLookup})">
            <summary>
            Returns a delegate which is used to map where clause expression to a link entity
            </summary>
            <param name="parameterName">Link entity name prefix from join clause</param>
            <param name="linkLookups">Collection of link entities to search in</param>
            <returns>A reference to link entity if it is found, null otherwise</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Linq.QueryProvider.ThrowException(System.Exception)">
            <summary>
            Overridable Throw mehtod
            </summary>
            <param name="exception">Exception to throw</param>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.ProvisionLanguageForUserResult">
            <summary>
            EnableLanguageForTeamsResult
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.ManagedProperty`1">
            <summary>
            Represents an instance of an managed property.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.ManagedProperty`1.Value">
            <summary>
            Value of the attribute that this managed property refers.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.ManagedProperty`1.CanBeChanged">
            <summary>
            Whether it is valid to change the Value of this managed property.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.ManagedProperty`1.ManagedPropertyLogicalName">
            <summary>
            The logical name of the managed property definition.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Messages.RetrieveEntityChangesRequest.GetGlobalMetadataVersion">
            <summary>
            If GetGlobalMetadataVersion = true,
            global CRM metadata version is added 
            to RetrievEntityChanges API response
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Money">
            <summary>
            Money
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V6.Contracts">
            <summary>
            Defines the namespace for common data contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V6.Metadata">
            <summary>
            Defines namespace for Metadata contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V6.Workflow">
            <summary>
            Defines namespace for workflow interfaces and contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V6.Services">
            <summary>
            Defines namespace for services
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V6.Claims">
            <summary>
            Defines namespace for claims.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V6.OrganizationEndpoint">
            <summary>
            Organization SDK endpoint
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V7.Contracts">
            <summary>
            Defines the namespace for common data contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V7.Metadata">
            <summary>
            Defines namespace for Metadata contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V7.Workflow">
            <summary>
            Defines namespace for workflow interfaces and contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V7.Services">
            <summary>
            Defines namespace for services
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V7.Claims">
            <summary>
            Defines namespace for claims.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V7.OrganizationEndpoint">
            <summary>
            Organization SDK endpoint
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V8.SDKVersion">
            <summary>
            Defines version of the SDK
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V82.SDKVersion">
            <summary>
            Defines version of the SDK
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V9.Contracts">
            <summary>
            Defines the namespace for common data contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V9.Metadata">
            <summary>
            Defines namespace for Metadata contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V9.SDKVersion">
            <summary>
            Defines version of the SDK
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V71.Contracts">
            <summary>
            Defines the namespace for common data contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V71.Metadata">
            <summary>
            Defines namespace for Metadata contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V71.Workflow">
            <summary>
            Defines namespace for workflow interfaces and contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V71.Services">
            <summary>
            Defines namespace for services
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V71.Claims">
            <summary>
            Defines namespace for claims.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V71.MetadataEndpoint">
            <summary>
            Defines namespace for metadataendpoint.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V71.OrganizationEndpoint">
            <summary>
            Organization SDK endpoint
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V71.SDKVersion">
            <summary>
            Defines version of the SDK
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.R8.Contracts">
            <summary>
            Defines the namespace for common data contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.R8.Metadata">
            <summary>
            Defines namespace for Metadata contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.R8.Workflow">
            <summary>
            Defines namespace for workflow interfaces and contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.R8.Services">
            <summary>
            Defines namespace for services
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.R8.Claims">
            <summary>
            Defines namespace for claims.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.R8.OrganizationEndpoint">
            <summary>
            Organization SDK endpoint
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.XmlNamespaces.V5">
            <summary>
            Defines the xml namespaces for SDK contracts
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V5.Contracts">
            <summary>
            Defines the namespace for common data contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V5.Metadata">
            <summary>
            Defines namespace for Metadata contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V5.Workflow">
            <summary>
            Defines namespace for workflow interfaces and contracts.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V5.Services">
            <summary>
            Defines namespace for services
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V5.Claims">
            <summary>
            Defines namespace for claims.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.V5.OrganizationEndpoint">
            <summary>
            Organization SDK endpoint
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.XmlNamespaces.Api">
            <summary>
            Defines the xml namespaces for Api endpoint
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.XmlNamespaces.Api.DataEndpoint">
            <summary>
            Api Data endpoint
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.OptionSetValue">
            <summary>
            OptionSetValue
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Organization.OrganizationDetail">
            <summary>
            OrganizationDetail
            These types should match the types in src/SDK/Core/DiscoveryServiceProxy.cs.
            The converters should be updated if the classes are changed in either file.
            </summary>
            
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Organization.OrganizationType">
            <summary>
            An Enumeration represents Organization Type
            </summary>
            <remarks>
            Copied from src\Platform\Core\DataServices\Configuration\LocatorService\LocatorServiceInterface.cs
            </remarks>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Organization.OrganizationInfo">
            <summary>
            A class that represents organization information
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Organization.OrganizationInfo.InstanceType">
            <summary>
            Gets or sets the instance types.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Organization.OrganizationInfo.Solutions">
            <summary>
            Gets or sets the solutions.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Organization.OrganizationInfo.ExtensionData">
            <summary>
            Gets or sets the structure that contains extra data.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Organization.Solution">
            <summary>
            A class that represents solution information
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Organization.Solution.Id">
            <summary>
            Gets or sets the solution identifier.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Organization.Solution.VersionNumber">
            <summary>
            Gets or sets the version number of the solution.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Organization.Solution.SolutionUniqueName">
            <summary>
            Gets or sets the unique name of the solution.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Organization.Solution.FriendlyName">
            <summary>
            Gets or sets the friendly name of the solution.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Organization.Solution.PublisherId">
            <summary>
            Gets or sets the publisherId of the solution.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Organization.Solution.PublisherIdName">
            <summary>
            Gets or sets the publisherId name of the solution.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Organization.Solution.PublisherUniqueName">
            <summary>
            Gets or sets the publisher unique name of the solution.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Organization.Solution.ExtensionData">
            <summary>
            Gets or sets the structure that contains extra data.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.PluginTelemetry.EventId">
            <summary>
            Defines eventId
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.EventId.#ctor(System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.PluginTelemetry.EventId"/> class.
            Constructor
            </summary>
            <param name="id"></param>
            <param name="name"></param>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.PluginTelemetry.EventId.Id">
            <summary>
            Id of event
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.PluginTelemetry.EventId.Name">
            <summary>
            Name of event
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.BeginScope``1(``0)">
            <summary>
            Begins a logical operation scope.
            </summary>
            <typeparam name="TState">The identifier for the scope.</typeparam>
            <param name="state">The type of the state to begin scope for.</param>
            <returns>An IDisposable that ends the logical operation scope on dispose.</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.BeginScope(System.String,System.Object[])">
            <summary>
            Formats the message and creates a scope.
            </summary>
            <param name="messageFormat">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
            <returns>An IDisposable that ends the logical operation scope on dispose.</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.IsEnabled(Microsoft.Xrm.Sdk.PluginTelemetry.LogLevel)">
            <summary>
            Checks if the given <paramref name="logLevel" /> is enabled.
            </summary>
            <param name="logLevel">level to be checked.</param>
            <returns><c>true</c> if enabled.</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.Log``1(Microsoft.Xrm.Sdk.PluginTelemetry.LogLevel,Microsoft.Xrm.Sdk.PluginTelemetry.EventId,``0,System.Exception,System.Func{``0,System.Exception,System.String})">
            <summary>
            Writes a log entry.
            </summary>
            <typeparam name="TState"></typeparam>
            <param name="logLevel">Entry will be written on this level.</param>
            <param name="eventId">Id of the event.</param>
            <param name="state">The entry to be written. Can be also an object.</param>
            <param name="exception">The exception related to this entry.</param>
            <param name="formatter">Function to create a <c>string</c> message of the state and exception.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.Log(Microsoft.Xrm.Sdk.PluginTelemetry.LogLevel,Microsoft.Xrm.Sdk.PluginTelemetry.EventId,System.Exception,System.String,System.Object[])">
            <summary>
            Formats and Writes a log message.
            </summary>
            <param name="logLevel">Entry will be written on this level.</param>
            <param name="eventId">Id of the event.</param>
            <param name="exception">The exception related to this entry.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.Log(Microsoft.Xrm.Sdk.PluginTelemetry.LogLevel,Microsoft.Xrm.Sdk.PluginTelemetry.EventId,System.String,System.Object[])">
            <summary>
            Formats and Writes a log message.
            </summary>
            <param name="logLevel">Entry will be written on this level.</param>
            <param name="eventId">Id of the event.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.Log(Microsoft.Xrm.Sdk.PluginTelemetry.LogLevel,System.Exception,System.String,System.Object[])">
            <summary>
            Formats and Writes a log message.
            </summary>
            <param name="logLevel">Entry will be written on this level.</param>
            <param name="exception">The exception related to this entry.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.Log(Microsoft.Xrm.Sdk.PluginTelemetry.LogLevel,System.String,System.Object[])">
            <summary>
            Formats and Writes a log message.
            </summary>
            <param name="logLevel">Entry will be written on this level.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogCritical(Microsoft.Xrm.Sdk.PluginTelemetry.EventId,System.Exception,System.String,System.Object[])">
            <summary>
            Formats and Writes a critical log message.
            </summary>
            <param name="eventId">Id of the event.</param>
            <param name="exception">The exception related to this entry.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogCritical(Microsoft.Xrm.Sdk.PluginTelemetry.EventId,System.String,System.Object[])">
            <summary>
            Formats and Writes a critical log message.
            </summary>
            <param name="eventId">Id of the event.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogCritical(System.Exception,System.String,System.Object[])">
            <summary>
            Formats and Writes a critical log message.
            </summary>
            <param name="exception">The exception related to this entry.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogCritical(System.String,System.Object[])">
            <summary>
            Formats and Writes a critical log message.
            </summary>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogDebug(Microsoft.Xrm.Sdk.PluginTelemetry.EventId,System.Exception,System.String,System.Object[])">
            <summary>
            Formats and Writes a debug log message.
            </summary>
            <param name="eventId">Id of the event.</param>
            <param name="exception">The exception related to this entry.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogDebug(Microsoft.Xrm.Sdk.PluginTelemetry.EventId,System.String,System.Object[])">
            <summary>
            Formats and Writes a debug log message.
            </summary>
            <param name="eventId">Id of the event.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogDebug(System.Exception,System.String,System.Object[])">
            <summary>
            Formats and Writes a debug log message.
            </summary>
            <param name="exception">The exception related to this entry.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogDebug(System.String,System.Object[])">
            <summary>
            Formats and Writes a debug log message.
            </summary>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogError(Microsoft.Xrm.Sdk.PluginTelemetry.EventId,System.Exception,System.String,System.Object[])">
            <summary>
            Formats and Writes an error log message.
            </summary>
            <param name="eventId">Id of the event.</param>
            <param name="exception">The exception related to this entry.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogError(Microsoft.Xrm.Sdk.PluginTelemetry.EventId,System.String,System.Object[])">
            <summary>
            Formats and Writes an error log message.
            </summary>
            <param name="eventId">Id of the event.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogError(System.Exception,System.String,System.Object[])">
            <summary>
            Formats and Writes an error log message.
            </summary>
            <param name="exception">The exception related to this entry.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogError(System.String,System.Object[])">
            <summary>
            Formats and Writes an error log message.
            </summary>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogInformation(Microsoft.Xrm.Sdk.PluginTelemetry.EventId,System.Exception,System.String,System.Object[])">
            <summary>
            Formats and Writes an information log message.
            </summary>
            <param name="eventId">Id of the event.</param>
            <param name="exception">The exception related to this entry.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogInformation(Microsoft.Xrm.Sdk.PluginTelemetry.EventId,System.String,System.Object[])">
            <summary>
            Formats and Writes an information log message.
            </summary>
            <param name="eventId">Id of the event.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogInformation(System.Exception,System.String,System.Object[])">
            <summary>
            Formats and Writes an information log message.
            </summary>
            <param name="exception">The exception related to this entry.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogInformation(System.String,System.Object[])">
            <summary>
            Formats and Writes an information log message.
            </summary>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogTrace(Microsoft.Xrm.Sdk.PluginTelemetry.EventId,System.Exception,System.String,System.Object[])">
            <summary>
            Formats and Writes a trace log message.
            </summary>
            <param name="eventId">Id of the event.</param>
            <param name="exception">The exception related to this entry.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogTrace(Microsoft.Xrm.Sdk.PluginTelemetry.EventId,System.String,System.Object[])">
            <summary>
            Formats and Writes a trace log message.
            </summary>
            <param name="eventId">Id of the event.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogTrace(System.Exception,System.String,System.Object[])">
            <summary>
            Formats and Writes a trace log message.
            </summary>
            <param name="exception">The exception related to this entry.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogTrace(System.String,System.Object[])">
            <summary>
            Formats and Writes a trace log message.
            </summary>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogWarning(Microsoft.Xrm.Sdk.PluginTelemetry.EventId,System.Exception,System.String,System.Object[])">
            <summary>
            Formats and Writes a warning log message.
            </summary>
            <param name="eventId">Id of the event.</param>
            <param name="exception">The exception related to this entry.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogWarning(Microsoft.Xrm.Sdk.PluginTelemetry.EventId,System.String,System.Object[])">
            <summary>
            Formats and Writes a warning log message.
            </summary>
            <param name="eventId">Id of the event.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogWarning(System.Exception,System.String,System.Object[])">
            <summary>
            Formats and Writes a warning log message.
            </summary>
            <param name="exception">The exception related to this entry.</param>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogWarning(System.String,System.Object[])">
            <summary>
            Formats and Writes a warning log message.
            </summary>
            <param name="message">Format string of the scope message.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogMetric(System.String,System.Int64)">
            <summary>
            Publish hot path metric.
            </summary>
            <param name="metricName">name of the metric</param>
            <param name="value">value that needs to be published</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.LogMetric(System.String,System.Collections.Generic.IDictionary{System.String,System.String},System.Int64)">
            <summary>
            Publish hot path metric nd add custom dimensions.
            </summary>
            <param name="metricName">name of the metric</param>
            <param name="metricDimensions">custom properties that needs to be published</param>
            <param name="value">value that needs to be published</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.AddCustomProperty(System.String,System.String)">
            <summary>
            Add property to customdimensions.
            </summary>
            <param name="propertyName">Name of the property.</param>
            <param name="propertyValue">Value of the property.</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.Execute(System.String,System.Action,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Executes an action and wrap it inside provided activityName
            </summary>
            <param name="activityName">Name of the activity that gets logged in telemetry</param>
            <param name="action">Action to be performed</param>
            <param name="additionalCustomProperties">Additional proeprties to be logged</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.PluginTelemetry.ILogger.ExecuteAsync(System.String,System.Func{System.Threading.Tasks.Task},System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Executes an action and wrap it inside provided activityName
            </summary>
            <param name="activityName">Name of the activity that gets logged in telemetry</param>
            <param name="action">Action to be performed</param>
            <param name="additionalCustomProperties">Additional proeprties to be logged</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.PluginTelemetry.LogLevel">
            <summary>
            Defines logging severity levels.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.PluginTelemetry.LogLevel.Trace">
            <summary>
            Logs that contain the most detailed messages.These messages may contain sensitive
                application data. These messages are disabled by default and should never be
                enabled in a production environment.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.PluginTelemetry.LogLevel.Debug">
            <summary>
            Logs that are used for interactive investigation during development. These logs
                should primarily contain information useful for debugging and have no long-term
                value.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.PluginTelemetry.LogLevel.Information">
            <summary>
            Logs that track the general flow of the application. These logs should have long-term
                value.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.PluginTelemetry.LogLevel.Warning">
            <summary>
            Logs that highlight an abnormal or unexpected event in the application flow,
                but do not otherwise cause the application execution to stop.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.PluginTelemetry.LogLevel.Error">
            <summary>
             Logs that highlight when the current flow of execution is stopped due to a failure.
                These should indicate a failure in the current activity, not an application-wide
                failure.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.PluginTelemetry.LogLevel.Critical">
            <summary>
            Logs that describe an unrecoverable application or system crash, or a catastrophic
                failure that requires immediate attention.
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.PluginTelemetry.LogLevel.None">
            <summary>
             Not used for writing log messages. Specifies that a logging category should not
                write any messages.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.IPSqlTdsResponsePipe">
            <summary>
            IPSqlTdsResponsePipe Class.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.IPSqlTdsResponsePipe.TdsEncodedDataStream">
            <summary>
            Tds Encoded Data Stream.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IPSqlTdsResponsePipe.StartAsync">
            <summary>
            Start fill in the stream async.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SqlNameMappingOptions">
            <summary>
            Indicates whether the table names and column names in the sql command text are expressed in logical name or display name.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Query.ColumnSet">
            <summary>
            Allow specifying selected columns
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.ColumnSet.HasLazyFileAttribute">
            <summary>
            Gets and sets a flag for a lazy file attribute value. This flag is used
            when large files are not routed to plugins.
            </summary>
            <returns>True if the entity has a lazy file attribute, otherwise false.</returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.ColumnSet.LazyFileAttributeEntityName">
            <summary>
            Gets and sets the lazy file attribute's entity name..
            </summary>
            <returns>True if the entity has a lazy file attribute, otherwise false.</returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.ColumnSet.LazyFileAttributeKey">
            <summary>
            Gets and sets the lazy file attribute name;
            </summary>
            <returns>The lazy file attribute key.</returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.ColumnSet.LazyFileAttributeValue">
            <summary>
            Gets and sets the lazy file attribute value;
            </summary>
            <returns>A lazy file attribute value.</returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.ColumnSet.LazyFileAttributeSizeLimit">
            <summary>
            Gets and sets the lazy file attribute value;
            </summary>
            <returns>A lazy file attribute value.</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.ConditionExpression.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Query.ConditionExpression"/> class.
            Condition Expression constructor.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.ConditionExpression.#ctor(System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Query.ConditionExpression"/> class.
            Condition Expression constructor.
            </summary>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
            <param name="values">list of values to be compared with</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.ConditionExpression.#ctor(System.String,System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Query.ConditionExpression"/> class.
            Condition Expression constructor.
            </summary>
            <param name="entityName">Name of the entity of attribute on which condition is to be defined on</param>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
            <param name="values">list of values to be compared with</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.ConditionExpression.#ctor(System.String,System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator,System.Boolean,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Query.ConditionExpression"/> class.
            Condition Expression constructor.
            </summary>
            <param name="entityName">Name of the entity of attribute on which condition is to be defined on</param>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
            <param name="compareColumns">Boolean flag to define condition on attributes instead of condition on constant value(s)</param>
            <param name="values">list of values or attributes(if compareColumns is true) to be compared with</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.ConditionExpression.#ctor(System.String,System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator,System.Boolean,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Query.ConditionExpression"/> class.
            Condition Expression constructor.
            </summary>
            <param name="entityName">Name of the entity of attribute on which condition is to be defined on</param>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
            <param name="compareColumns">Boolean flag to define condition on attributes instead of condition on constant value(s)</param>
            <param name="value">value or attributes(if compareColumns is true) to be compared with</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.ConditionExpression.#ctor(System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator,System.Boolean,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Query.ConditionExpression"/> class.
            Condition Expression constructor.
            </summary>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
            <param name="compareColumns">Boolean flag to define condition on attributes instead of condition on constant value(s)</param>
            <param name="value">value or attributes(if compareColumns is true) to be compared with</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.ConditionExpression.#ctor(System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator,System.Boolean,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Query.ConditionExpression"/> class.
            Condition Expression constructor.
            </summary>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
            <param name="compareColumns">Boolean flag to define condition on attributes instead of condition on constant value(s)</param>
            <param name="values">list of values or attributes(if compareColumns is true) to be compared with</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.ConditionExpression.#ctor(System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Query.ConditionExpression"/> class.
            Condition Expression constructor.
            </summary>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
            <param name="value">value to be compared with</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.ConditionExpression.#ctor(System.String,System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Query.ConditionExpression"/> class.
            Condition Expression constructor.
            </summary>
            <param name="entityName">Name of the entity of attribute on which condition is to be defined on</param>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
            <param name="value">value to be compared with</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.ConditionExpression.#ctor(System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Query.ConditionExpression"/> class.
            Condition Expression constructor.
            </summary>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.ConditionExpression.#ctor(System.String,System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Query.ConditionExpression"/> class.
            Condition Expression constructor.
            </summary>
            <param name="entityName">Name of the entity of attribute on which condition is to be defined on</param>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.ConditionExpression.#ctor(System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator,System.Collections.ICollection)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Query.ConditionExpression"/> class.
            Condition Expression constructor.
            </summary>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
            <param name="values">list of values to be compared with</param>
            <remarks>Need to handle collections differently. esp. Guid arrays.</remarks>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.ConditionExpression.EntityName">
            <summary>
            Name or alias of LinkEntity to which this condition refers to
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.ConditionExpression.CompareColumns">
            <summary>
            Bool flag to distinguish between column comparison condition vs condition on constant value(s)
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.ConditionExpression.AttributeName">
            <summary>
            Name of the attribute on which the condition is defined on
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.ConditionExpression.Operator">
            <summary>
            Condition Operator to be applied on condition
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.ConditionExpression.Values">
            <summary>
            List of values to be compared to in the condition
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.FilterExpression.FilterHint">
            <summary>
            Filter hint
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.FilterExpression.AddCondition(System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator,System.Object[])">
            <summary>
            Add multiple value condition to the filter
            </summary>
            <param name="attributeName">Name of the attribute that the condition is on</param>
            <param name="conditionOperator">condition operator to be applied</param>
            <param name="values">list of values to be compared with</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.FilterExpression.AddCondition(System.String,System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator,System.Object[])">
            <summary>
            Add multiple value condition to the filter
            </summary>
            <param name="entityName">Name of the entity of attribute on which condition is to be defined on</param>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
            <param name="values">list of values to be compared with</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.FilterExpression.AddCondition(System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator,System.Boolean,System.Object[])">
            <summary>
            Add multiple value condition to the filter
            </summary>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
            <param name="compareColumns">Boolean flag to define condition on attributes instead of condition on constant value(s)</param>
            <param name="values">list of values or attributes(if compareColumns is true) to be compared with</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.FilterExpression.AddCondition(System.String,System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator,System.Boolean,System.Object[])">
            <summary>
            Add multiple value condition to the filter
            </summary>
            <param name="entityName">Name of the entity of attribute on which condition is to be defined on</param>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
            <param name="compareColumns">Boolean flag to define condition on attributes instead of condition on constant value(s)</param>
            <param name="values">list of values or attributes(if compareColumns is true) to be compared with</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.FilterExpression.AddCondition(System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator,System.Boolean,System.Object)">
            <summary>
            Add multiple value condition to the filter
            </summary>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
            <param name="compareColumns">Boolean flag to define condition on attributes instead of condition on constant value(s)</param>
            <param name="value">value or attribute(if compareColumns is true) to be compared with</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.FilterExpression.AddCondition(System.String,System.String,Microsoft.Xrm.Sdk.Query.ConditionOperator,System.Boolean,System.Object)">
            <summary>
            Add multiple value condition to the filter
            </summary>
            <param name="entityName">Name of the entity of attribute on which condition is to be defined on</param>
            <param name="attributeName">Name of the attribute on which condition is to be defined on</param>
            <param name="conditionOperator">operator that has to be applied</param>
            <param name="compareColumns">Boolean flag to define condition on attributes instead of condition on constant value(s)</param>
            <param name="value">value or attribute(if compareColumns is true) to be compared with</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.FilterExpression.AddCondition(Microsoft.Xrm.Sdk.Query.ConditionExpression)">
            <summary>
            Add condition to the filter
            </summary>
            <param name="condition">condition to be added</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.FilterExpression.AddFilter(Microsoft.Xrm.Sdk.Query.LogicalOperator)">
            <summary>
            add filter to current filter
            </summary>
            <param name="logicalOperator"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.FilterExpression.AddFilter(Microsoft.Xrm.Sdk.Query.FilterExpression)">
            <summary>
            add filter to current filter
            </summary>
            <param name="childFilter">Filter to be added.</param>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Query.IQueryExpressionVisitor">
            <summary>
            Provides a mechanism for visiting or rewriting query expression trees.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.IQueryExpressionVisitor.Visit(Microsoft.Xrm.Sdk.Query.QueryExpression)">
            <summary>
            Visits the QueryExpression.
            </summary>
            <param name="query">The query expression to visit.</param>
            <returns>The modified expression, if it or any subexpression was modified; otherwise, returns the original expression.</returns>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.PagingInfo.PageNumber">
            <summary>
            The page to be returned.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.PagingInfo.Count">
            <summary>
            number of rows per page.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.PagingInfo.ReturnTotalRecordCount">
            <summary>
            If total record count across all pages is required
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.PagingInfo.PagingCookie">
            <summary>
            Page Cookie information, null indicates old paging to be used.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Query.QueryBase">
            <summary>
            Query class similary to V4
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Query.QueryExpressionVisitorBase">
            <summary>
            Represents a visitor or rewriter for query expression trees.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.QueryExpressionVisitorBase.Visit(Microsoft.Xrm.Sdk.Query.QueryExpression)">
            <summary>
            Visits top level properties of QueryExpression and dispatches each subexpression to one of the more specialized visit methods in this class.
            </summary>
            <param name="query">The query expression to visit.</param>
            <returns>The modified expression, if it or any subexpression was modified; otherwise, returns the original expression.</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.QueryExpressionVisitorBase.VisitPagingInfo(Microsoft.Xrm.Sdk.Query.PagingInfo)">
            <summary>
            Visits the PagingInfo.
            </summary>
            <param name="pageInfo">The PagingInfo to visit.</param>
            <returns>The modified PagingInfo, if it was modified; otherwise, returns the original PagingInfo.</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.QueryExpressionVisitorBase.VisitLinkEntity(Microsoft.Xrm.Sdk.Query.LinkEntity)">
            <summary>
            Visits the LinkEntity by dispatching each subexpression to one of the more specialized visit methods in this class.
            </summary>
            <param name="linkEntity">The LinkEntity to visit.</param>
            <returns>The modified LinkEntity, if it or any subexpression was modified; otherwise, returns the original LinkEntity.</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.QueryExpressionVisitorBase.VisitConditionExpression(Microsoft.Xrm.Sdk.Query.ConditionExpression)">
            <summary>
            Visits the ConditionExpression.
            </summary>
            <param name="condition">The ConditionExpression to visit.</param>
            <returns>The modified ConditionExpression, if it was modified; otherwise, returns the original ConditionExpression.</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.QueryExpressionVisitorBase.VisitFilterExpression(Microsoft.Xrm.Sdk.Query.FilterExpression)">
            <summary>
            Visits the FilterExpression by dispatching each subexpression to one of the more specialized visit methods in this class.
            </summary>
            <param name="filter">The LinkEntity to visit.</param>
            <returns>The modified FilterExpression, if it or any subexpression was modified; otherwise, returns the original FilterExpression.</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.QueryExpressionVisitorBase.VisitOrderExpression(Microsoft.Xrm.Sdk.Query.OrderExpression)">
            <summary>
            Visits the OrderExpression.
            </summary>
            <param name="order">The OrderExpression to visit.</param>
            <returns>The modified OrderExpression, if it was modified; otherwise, returns the original OrderExpression.</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Query.QueryExpressionVisitorBase.VisitColumnSet(Microsoft.Xrm.Sdk.Query.ColumnSet)">
            <summary>
            Visits the ColumnSet.
            </summary>
            <param name="columnSet">The ColumnSet to visit.</param>
            <returns>The modified ColumnSet, if it was modified; otherwise, returns the original ColumnSet.</returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Query.SeverityLevel">
            <summary>
            The impact of a given message where low is not that important and high / critical is blocking
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Query.SeverityLevel.Low">
            <summary>
            Low / Informational
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Query.SeverityLevel.Medium">
            <summary>
            Somewhat impactful issue (Warning)
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Query.SeverityLevel.High">
            <summary>
            Highly impacting issue (blocking)
            </summary>
        </member>
        <member name="F:Microsoft.Xrm.Sdk.Query.SeverityLevel.Critical">
            <summary>
            critically impacting issue (blocking)
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Query.InfoCode">
            <summary>
            Information code which uniquely identifies each message type
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Query.ValidatorIssue">
            <summary>
            A container which is used to record any issues that were found during validating process. 
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.ValidatorIssue.TypeCode">
            <summary>
            Unique errorcode which uniquely identifies a given issue type.
            This is losely typed to <see cref="T:Microsoft.Xrm.Sdk.Query.InfoCode"/>. Not declared as an enum to prevent tight coupling between server and caller
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.ValidatorIssue.Severity">
            <summary>
            How important is this issue in terms of needing to fix
            This is losely typed to <see cref="T:Microsoft.Xrm.Sdk.Query.SeverityLevel"/>. Not declared as an enum to prevent tight coupling between server and caller
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.ValidatorIssue.LocalizedMessageText">
            <summary>
            A plain text message that describes the issue.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.ValidatorIssue.OptionalPropertyBag">
            <summary>
            A prop bag which is used to pass along any contextual information for the issue (Entity info, condition, etc)
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Query.ValidateFetchXmlExpressionResult">
            <summary>
            Response container which given back to the caller of the ValidateFetchExpression api
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Query.ValidateFetchXmlExpressionResult.Messages">
            <summary>
            Messages found during the validation of a given fetchxml
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.QuickFindResultCollection">
            <summary>
            This collection holds all the entity search results for the entities involved in the multi entity search.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.QuickFindResult">
            <summary>
            Holds the Search Results per Entity which includes the Search Status (Ok, Error, Quick Find Limit Exceeded)
            an error message, and EntityCollection which has the data. This EntityCollection will always be present
            and will be empty only if the search returned no data for a given entity or is empty because of an error.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.QuickFindConfiguration">
            <summary>
            Entities participating in the Multi Entity Search in the Order in which the results should be returned
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.Relationship">
            <summary>
            Represents a relationship to use when associating, disassociating and retrieving related entities
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Relationship.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Relationship"/> class.
            Creates a Relationship object
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.Relationship.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.Relationship"/> class.
            Creates a Relationship object and initilizes SchemaName member
            </summary>
            <param name="schemaName">Schema name for the relationship.</param>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Relationship.SchemaName">
            <summary>
            Schema name for the relationship
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.Relationship.PrimaryEntityRole">
            <summary>
            Role of primary entity in the relationship.
            Required when using a reflexive relationship
            Not required for non-reflexive relationship, must match the entity's role if specified
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.RelationshipSchemaNameAttribute.SchemaName">
            <summary>
            Gets or sets the CRM schema name of the relationship.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.RelationshipSchemaNameAttribute.PrimaryEntityRole">
            <summary>
            Gets or sets the name of the <see cref="T:Microsoft.Xrm.Sdk.EntityRole"/> of the relationship.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.RelationshipSchemaNameAttribute.Relationship">
            <summary>
            Gets the <see cref="P:Microsoft.Xrm.Sdk.RelationshipSchemaNameAttribute.Relationship"/> of the attribute.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.FieldPermissionType.Validate(System.Int32)">
            <summary>
            Validate whether the provided value is one of the constants in the class
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">Throws ArgumentOutOfRangeException if the provided value is not 
            one of the constants specified in the class</exception>
            <param name="value">an integer to be validated as a FieldPermissionType</param>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SelectColumn">
            <summary>
            EntityCollection does not have a columnset schema header. So if the EntityCollection is empty then TDS endpoint will not know how to generate 
            the column header. With guidance from sdk team we decided not to modify the EntityCollection, but expose a seperate property that represents
            a columnset header for the entity collection.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.IOrganizationService">
            <summary>
            Interface containing methods provided by Organization Service.
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IOrganizationService.Create(Microsoft.Xrm.Sdk.Entity)">
            <summary>
            Create an entity and process any related entities
            </summary>
            <param name="entity">entity to create</param>
            <param name="relatedActions">related entity actions</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IOrganizationService.Retrieve(System.String,System.Guid,Microsoft.Xrm.Sdk.Query.ColumnSet)">
            <summary>
            Retrieves instance of an entity
            </summary>
            <param name="entityName">Logical name of entity</param>
            <param name="id">Id of entity</param>
            <returns>Entiy</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IOrganizationService.Update(Microsoft.Xrm.Sdk.Entity)">
            <summary>
            Updates an entity and process any related entities
            </summary>
            <param name="entity">entity to update</param>
            <param name="relatedActions">related entity actions</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IOrganizationService.Delete(System.String,System.Guid)">
            <summary>
            Delete instance of an entity
            </summary>
            <param name="entityName">Logical name of entity</param>
            <param name="id">Id of entity</param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IOrganizationService.Execute(Microsoft.Xrm.Sdk.OrganizationRequest)">
            <summary>
            Perform an action in an organization specified by the request.
            </summary>
            <param name="request">Refer to SDK documentation for list of messages that can be used.</param>
            <returns>Results from processing the request</returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IOrganizationService.Associate(System.String,System.Guid,Microsoft.Xrm.Sdk.Relationship,Microsoft.Xrm.Sdk.EntityReferenceCollection)">
            <summary>
            Associate an entity with a set of entities
            </summary>
            <param name="entityName"></param>
            <param name="entityId"></param>
            <param name="relationship"></param>
            <param name="relatedEntities"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IOrganizationService.Disassociate(System.String,System.Guid,Microsoft.Xrm.Sdk.Relationship,Microsoft.Xrm.Sdk.EntityReferenceCollection)">
            <summary>
            Disassociate an entity with a set of entities
            </summary>
            <param name="entityName"></param>
            <param name="entityId"></param>
            <param name="relationship"></param>
            <param name="relatedEntities"></param>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.IOrganizationService.RetrieveMultiple(Microsoft.Xrm.Sdk.Query.QueryBase)">
            <summary>
            Retrieves a collection of entities
            </summary>
            <param name="query"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.LayerDesiredOrder">
            <summary>
            Holds the hints for solution install
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.LayerDesiredOrderType">
            <summary>
            Supported types of hints
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SolutionInfo">
            <summary>
            Holds information about the details of the solution
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SolutionOperationMessageType">
            <summary>
            The violation type
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SolutionOperationResult">
            <summary>
            Stage solution results
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SolutionOperationStatus">
            <summary>
            The violation type
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SolutionOperationType">
            <summary>
            The violation type
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SolutionParameters">
            <summary>
            Holds the solution parameters for import
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SolutionValidationResult">
            <summary>
            A container which is used to record any issues that were found during validating process. 
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SolutionValidationResult.SolutionValidationResultType">
            <summary>
            The SolutionValidationResult Type
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SolutionValidationResult.Message">
            <summary>
            A plain text message that describes the issue.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SolutionValidationResult.ErrorCode">
            <summary>
            Errorcode
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SolutionValidationResult.AdditionalInfo">
            <summary>
            AdditionalInfo
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SolutionValidationResultType">
            <summary>
            The violation type
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.StageSolutionResults">
            <summary>
            Stage solution results
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.StageSolutionStatus">
            <summary>
            The violation type
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.DistinctMobileOfflineRelatedEntitiesResponse">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.DistinctMobileOfflineRelatedEntitiesResponse"/> class
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.DistinctMobileOfflineRelatedEntitiesResponse.DistinctRelatedEntities">
            <summary>
            Entities that are related
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.DistinctMobileOfflineRelatedEntitiesResponse.EntitiesNotInProfile">
            <summary>
            Entities that are not in profile
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.OfflineAppModuleProfileMap">
            <summary>
            Map from app modules to profileIds
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.OfflineAppModuleProfileMapping">
            <summary>
            Mapping from app module to profile id
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.OfflineAppModuleProfileMapping.AppModuleId">
            <summary>
            Gets or sets the app module id
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.OfflineAppModuleProfileMapping.ProfileId">
            <summary>
            Gets or sets the profile id
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SyncOfflineDataResponse">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.SyncOfflineDataResponse"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncOfflineDataResponse.Updates">
            <summary>
            Denotes updated entities
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncOfflineDataResponse.Deletes">
            <summary>
            Denotes deleted entities
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncOfflineDataResponse.CompletedEntities">
            <summary>
            Denotes sync completed entities
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncOfflineDataResponse.SyncToken">
            <summary>
            Denotes the sync token
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncOfflineDataResponse.IsFinalPage">
            <summary>
            Denotes if it is the final page
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.UpdatedData">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.UpdatedData"/> class.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.DeletedData">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.DeletedData"/> class.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.EntitiesWithRejectedBackgroundSync">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.EntitiesWithRejectedBackgroundSync"/> class.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SyncOfflineDataSchemaResponse">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.SyncOfflineDataSchemaResponse"/> class
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncOfflineDataSchemaResponse.Scripts">
            <summary>
            Entity metadata scripts
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncOfflineDataSchemaResponse.SyncToken">
            <summary>
            Sync token to mark the data which is getting sync'd
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncOfflineDataSchemaResponse.IsFinalPage">
            <summary>
            Gets or sets a value indicating whether or not this is the final page of data
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncOfflineDataSchemaResponse.MetadataVersion">
            <summary>
            Gets or sets a value indicating the metadata version
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncOfflineDataSchemaResponse.ProfileVersion">
            <summary>
            Gets or sets a value indicating the profile version
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.OfflineSchemaScript">
            <summary>
            Offline Metadata Schema script for an entity
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SyncSubscriptionDataResponse">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.SyncSubscriptionDataResponse"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncSubscriptionDataResponse.SyncToken">
            <summary>
            Sync token to mark the data which is getting sync'd
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncSubscriptionDataResponse.IsFinalPage">
            <summary>
            Flag to indicate if there is more data available to be sync'd
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SubscriptionDeletedEntitiesData">
            <summary>
            class to hold Deleted entities collection
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SubscriptionDeletedEntityData">
            <summary>
            class to hold deleted entity Ids
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SubscriptionUpdatedEntitiesData">
            <summary>
            class to hold Updated data entities collection
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SubscriptionUpdatedEntityData">
            <summary>
            class to hold updated entity data
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.SyncSubscriptionMetadataResponse">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.SyncSubscriptionMetadataResponse"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncSubscriptionMetadataResponse.Metadata">
            <summary>
            Enities metadata definition
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncSubscriptionMetadataResponse.SyncToken">
            <summary>
            Sync token to mark the data which is getting sync'd
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncSubscriptionMetadataResponse.IsFinalPage">
            <summary>
            Gets or sets a value indicating whether or not this is the final page of data
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncSubscriptionMetadataResponse.MetadataVersion">
            <summary>
            Gets or sets a value indicating the metadata version
            </summary>
        </member>
        <member name="P:Microsoft.Xrm.Sdk.SyncSubscriptionMetadataResponse.ProfileVersion">
            <summary>
            Gets or sets a value indicating the profile version
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.TypeExtensions">
            <summary>
            Type class related extension methods and utilities.
            </summary>
            <exclude/>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.TypeExtensions.GetUnderlyingType(System.Type)">
            <summary>
            Retrieves the underlying type if the type is nullable, otherwise returns the current type.
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.TypeExtensions.IsA``1(System.Type)">
            <summary>
            Determines if a generic type is assignable from this type.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.TypeExtensions.IsA(System.Type,System.Type)">
            <summary>
            Determines if the input reference type is assignable from this type.
            </summary>
            <param name="type"></param>
            <param name="referenceType"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.UserLicenseInfo">
            <summary>
            User licenses information
            In future, we can add more details which are returned by graph api.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.ServicePlan">
            <summary>
            Service plan ex:D365_ENTERPRISE_P1
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.WebServiceClient.DiscoveryWebProxyClientContextInitializer">
            <summary>
                Manages context for sdk calls
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.WebServiceClient.UserType">
            <summary>
            Enumeration of the different type of user.  This can be a crm user or 
            an ExternalParty user calling the sdk.  The type is used to help
            identify application calls for impersonations.
            </summary>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.WebServiceClient.OrganizationWebProxyClientContextInitializer">
            <summary>
                Manages context for sdk calls
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.WebServiceClient.WebProxyClient`1.GetXrmSdkAssemblyFileVersion">
            <summary>
                Get's the file version of the Xrm Sdk assembly that is loaded in the current client domain.
                For Sdk clients called via the OrganizationServiceProxy this is the version of the local Microsoft.Xrm.Sdk dll used
                by the Client App.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Xrm.Sdk.WebServiceClient.WebProxyClientContextInitializer`1">
            <summary>
                Manages context for sdk calls
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Sdk.WebServiceClient.WebProxyClientContextInitializer`1.#ctor(Microsoft.Xrm.Sdk.WebServiceClient.WebProxyClient{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xrm.Sdk.WebServiceClient.WebProxyClientContextInitializer`1"/> class.
                Constructs a context initializer
            </summary>
            <param name="proxy">sdk proxy</param>
        </member>
        <member name="T:Microsoft.Xrm.Kernel.Contracts.IPSqlAsyncPluginService">
            <summary>
            Interface to regenerate TDS views service
            </summary>
        </member>
        <member name="M:Microsoft.Xrm.Kernel.Contracts.IPSqlAsyncPluginService.RegenerateTDSViews(System.Guid)">
            <summary>
            Regenerates the TDS filtered views for all the relevant entities in the organization
            </summary>
            <param name="organizationId">organization for which the views need to be generated</param>
            <returns>true if the TDS view generation was successful, false otherwise</returns>
        </member>
        <member name="M:Microsoft.Xrm.Kernel.Contracts.IPSqlAsyncPluginService.RegenerateTDSViewForEntity(Microsoft.Xrm.Sdk.Metadata.CreateTDSViewAsyncRequest)">
            <summary>
            Regenerates the TDS filtered view for the entity specified
            </summary>
            <param name="createTDSViewAsyncRequest">Request object for which the view needs to be generated</param>
        </member>
        <member name="T:Microsoft.Crm.Protocols.WSTrust.Bindings.IssuedTokenWSTrustBinding">
            <summary>
            This class has been in-place ported from WIF 3.5 for legacy support.  It SHOULD NOT be used for any future development but deleted once WS-Trust support has been removed.
            </summary>
        </member>
        <member name="T:Microsoft.Crm.Protocols.WSTrust.Bindings.UserNameWSTrustBinding">
            <summary>
            This class has been in-place ported from WIF 3.5 for legacy support.  It SHOULD NOT be used for any future development but deleted once WS-Trust support has been removed.
            </summary>
        </member>
        <member name="T:Microsoft.Crm.Protocols.WSTrust.Bindings.WSTrustBindingBase">
            <summary>
            This class has been in-place ported from WIF 3.5 for legacy support.  It SHOULD NOT be used for any future development but deleted once WS-Trust support has been removed.
            </summary>
        </member>
    </members>
</doc>
