
using Abt.Epdt.WebApis.Model;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System;
using Abt.Epdt.WebApis.Command;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using System.Threading.Tasks;

namespace Abt.Epdt.WebApis.Controller
{


    [ApiController]
    [Route("api/employee")]
    public class EmployeeController : ControllerBase
    {

        private readonly IMemoryCache _memoryCache;
        private readonly EmployeeCommand com;

        public EmployeeController(IMemoryCache memoryCache)
        {
            _memoryCache = memoryCache;
            com = new EmployeeCommand(_memoryCache);
        }


        /// <summary>
        /// 获取员工主数据
        /// </summary>
        /// <param name="startmodifiedon">开始修改时间</param>
        /// <param name="endmodifiedon">结束修改时间</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [Authorize(AuthenticationSchemes = "JwtBearer")]
        [HttpGet]
        [Route("GetEmployees")]
        public ResModel GetEmployees(string startmodifiedon, string endmodifiedon)
        {
            return com.GetEmployees(startmodifiedon, endmodifiedon);
        }

        /// <summary>
        /// 企微-同步员工信息
        /// </summary>
        /// <param name="employee">员工信息</param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = "JwtBearer")]
        [HttpPost]
        [Route("WechatUser")]
        public async Task<ResModel> WechatUser([FromBody] EmployeeInfo employee)
        {
            return await com.WechatUser(employee);
        }
    }
}
