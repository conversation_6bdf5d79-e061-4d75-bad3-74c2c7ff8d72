﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Workflow;
using Newtonsoft.Json;
using System;
using System.Activities;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Abbott.Onepdt.CodeActivities.GetSFTPProductData;

namespace Abbott.Onepdt.CodeActivities
{
    public class SyncProduct : CodeActivity
    {
        [Input("Data")]
        public InArgument<string> DataArgument { get; set; }

        [Output("Result")]
        public OutArgument<string> ResultArgument { get; set; }
        protected override void Execute(CodeActivityContext context)
        {
            // 获取服务
            IWorkflowContext workcontext = context.GetExtension<IWorkflowContext>();
            IOrganizationServiceFactory factory = context.GetExtension<IOrganizationServiceFactory>();
            ITracingService tracingService = context.GetExtension<ITracingService>();
            IOrganizationService serviceAdmin = factory.CreateOrganizationService(workcontext.UserId);

            var Data = DataArgument.Get(context);
            var res = new ReturnModel();
            var failcount = 0;
            var error = new StringBuilder();
            var products = JsonConvert.DeserializeObject<List<ProductModel>>(Data);

            foreach (var item in products)
            {
                try
                {
                    var entity = new Entity("onepdt_t_sap_product_mdata");
                    entity["onepdt_equnr"] = item.EQUNR;
                    entity["onepdt_batch"] = item.BATCH;
                    entity["onepdt_gtin"] = item.GTIN;
                    entity["onepdt_sn"] = item.SN;
                    entity["onepdt_zmod"] = item.ZMOD;
                    if (!string.IsNullOrWhiteSpace(item.MANUFACTUREDATE) && DateTime.TryParse(item.MANUFACTUREDATE, out DateTime onepdt_manufacturedate))
                    {
                        entity["onepdt_manufacturedate"] = onepdt_manufacturedate;
                    }
                    if (!string.IsNullOrWhiteSpace(item.VALTODATE) && DateTime.TryParse(item.VALTODATE, out DateTime onepdt_valtodate))
                    {
                        entity["onepdt_valtodate"] = onepdt_valtodate;
                    }
                    serviceAdmin.Create(entity);
                }
                catch (Exception ex)
                {
                    failcount++;
                    error.AppendLine($"【{JsonConvert.SerializeObject(item)}】同步失败：{ex.Message}");
                    continue;
                }
            }
            res.data = $"本次总计：{products.Count},失败：{failcount}。{(!string.IsNullOrWhiteSpace(error.ToString()) ? $"详细失败原因：{error.ToString()}" : "")}";
            ResultArgument.Set(context, JsonConvert.SerializeObject(res));
        }
    }
}
