{"version": 2, "dgSpecHash": "1YqR1YSDgrE=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\Abt.Epdt.WebApis.csproj", "expectedPackageFiles": ["C:\\.nuget\\vc_packages\\azure.core\\1.44.1\\azure.core.1.44.1.nupkg.sha512", "C:\\.nuget\\vc_packages\\azure.identity\\1.13.1\\azure.identity.1.13.1.nupkg.sha512", "C:\\.nuget\\vc_packages\\azure.security.keyvault.secrets\\4.7.0\\azure.security.keyvault.secrets.4.7.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.aspnetcore.authentication.jwtbearer\\3.1.12\\microsoft.aspnetcore.authentication.jwtbearer.3.1.12.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.bcl.asyncinterfaces\\6.0.0\\microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.csharp\\4.0.1\\microsoft.csharp.4.0.1.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.extensions.apidescription.server\\6.0.5\\microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.extensions.caching.abstractions\\3.1.8\\microsoft.extensions.caching.abstractions.3.1.8.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.extensions.caching.memory\\3.1.8\\microsoft.extensions.caching.memory.3.1.8.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.extensions.configuration\\3.1.8\\microsoft.extensions.configuration.3.1.8.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.extensions.configuration.abstractions\\3.1.8\\microsoft.extensions.configuration.abstractions.3.1.8.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.extensions.configuration.binder\\3.1.8\\microsoft.extensions.configuration.binder.3.1.8.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.extensions.dependencyinjection\\3.1.8\\microsoft.extensions.dependencyinjection.3.1.8.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.extensions.dependencyinjection.abstractions\\3.1.8\\microsoft.extensions.dependencyinjection.abstractions.3.1.8.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.extensions.http\\3.1.8\\microsoft.extensions.http.3.1.8.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.extensions.logging\\3.1.8\\microsoft.extensions.logging.3.1.8.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.extensions.logging.abstractions\\3.1.8\\microsoft.extensions.logging.abstractions.3.1.8.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.extensions.objectpool\\6.0.16\\microsoft.extensions.objectpool.6.0.16.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.extensions.options\\3.1.8\\microsoft.extensions.options.3.1.8.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.extensions.primitives\\3.1.8\\microsoft.extensions.primitives.3.1.8.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.identity.client\\4.66.1\\microsoft.identity.client.4.66.1.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.identity.client.extensions.msal\\4.66.1\\microsoft.identity.client.extensions.msal.4.66.1.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.identitymodel.abstractions\\8.0.2\\microsoft.identitymodel.abstractions.8.0.2.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.identitymodel.jsonwebtokens\\8.0.2\\microsoft.identitymodel.jsonwebtokens.8.0.2.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.identitymodel.logging\\8.0.2\\microsoft.identitymodel.logging.8.0.2.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.identitymodel.protocols\\5.5.0\\microsoft.identitymodel.protocols.5.5.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.identitymodel.protocols.openidconnect\\5.5.0\\microsoft.identitymodel.protocols.openidconnect.5.5.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.identitymodel.tokens\\8.0.2\\microsoft.identitymodel.tokens.8.0.2.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.identitymodel.tokens.saml\\5.2.4\\microsoft.identitymodel.tokens.saml.5.2.4.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.identitymodel.xml\\5.2.4\\microsoft.identitymodel.xml.5.2.4.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.netcore.platforms\\1.1.0\\microsoft.netcore.platforms.1.1.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.openapi\\1.6.14\\microsoft.openapi.1.6.14.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.powerplatform.dataverse.client\\1.2.1\\microsoft.powerplatform.dataverse.client.1.2.1.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.powerplatform.dataverse.client.dynamics\\1.2.2\\microsoft.powerplatform.dataverse.client.dynamics.1.2.2.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.visualbasic\\10.3.0\\microsoft.visualbasic.10.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\microsoft.win32.systemevents\\6.0.0\\microsoft.win32.systemevents.6.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\netstandard.library\\1.6.1\\netstandard.library.1.6.1.nupkg.sha512", "C:\\.nuget\\vc_packages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "C:\\.nuget\\vc_packages\\renci.sshnet.async\\1.4.0\\renci.sshnet.async.1.4.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\sixlabors.imagesharp\\3.1.6\\sixlabors.imagesharp.3.1.6.nupkg.sha512", "C:\\.nuget\\vc_packages\\ssh.net\\2016.1.0\\ssh.net.2016.1.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\sshnet.security.cryptography\\1.2.0\\sshnet.security.cryptography.1.2.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\sustainsys.saml2\\2.10.0\\sustainsys.saml2.2.10.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\sustainsys.saml2.aspnetcore2\\2.10.0\\sustainsys.saml2.aspnetcore2.2.10.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\swashbuckle.aspnetcore\\6.7.3\\swashbuckle.aspnetcore.6.7.3.nupkg.sha512", "C:\\.nuget\\vc_packages\\swashbuckle.aspnetcore.swagger\\6.7.3\\swashbuckle.aspnetcore.swagger.6.7.3.nupkg.sha512", "C:\\.nuget\\vc_packages\\swashbuckle.aspnetcore.swaggergen\\6.7.3\\swashbuckle.aspnetcore.swaggergen.6.7.3.nupkg.sha512", "C:\\.nuget\\vc_packages\\swashbuckle.aspnetcore.swaggerui\\6.7.3\\swashbuckle.aspnetcore.swaggerui.6.7.3.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.appcontext\\4.3.0\\system.appcontext.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.buffers\\4.3.0\\system.buffers.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.clientmodel\\1.1.0\\system.clientmodel.1.1.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.configuration.configurationmanager\\6.0.0\\system.configuration.configurationmanager.6.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.console\\4.3.0\\system.console.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.diagnostics.diagnosticsource\\6.0.1\\system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.diagnostics.tools\\4.3.0\\system.diagnostics.tools.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.diagnostics.tracesource\\4.0.0\\system.diagnostics.tracesource.4.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.drawing.common\\6.0.0\\system.drawing.common.6.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.dynamic.runtime\\4.0.11\\system.dynamic.runtime.4.0.11.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.formats.asn1\\6.0.0\\system.formats.asn1.6.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.identitymodel.tokens.jwt\\8.0.2\\system.identitymodel.tokens.jwt.8.0.2.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.io.compression.zipfile\\4.3.0\\system.io.compression.zipfile.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.io.pipelines\\9.0.0\\system.io.pipelines.9.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.memory.data\\6.0.0\\system.memory.data.6.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.net.http\\4.3.0\\system.net.http.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.net.nameresolution\\4.0.0\\system.net.nameresolution.4.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.net.sockets\\4.3.0\\system.net.sockets.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.private.datacontractserialization\\4.3.0\\system.private.datacontractserialization.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.reflection.typeextensions\\4.7.0\\system.reflection.typeextensions.4.7.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.runtime.caching\\4.7.0\\system.runtime.caching.4.7.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.runtime.serialization.primitives\\4.3.0\\system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.runtime.serialization.xml\\4.3.0\\system.runtime.serialization.xml.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.security.claims\\4.0.1\\system.security.claims.4.0.1.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.security.cryptography.cng\\4.3.0\\system.security.cryptography.cng.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.security.cryptography.pkcs\\6.0.1\\system.security.cryptography.pkcs.6.0.1.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.security.cryptography.protecteddata\\6.0.0\\system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.security.cryptography.xml\\6.0.1\\system.security.cryptography.xml.6.0.1.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.security.permissions\\6.0.0\\system.security.permissions.6.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.security.principal\\4.0.1\\system.security.principal.4.0.1.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.security.principal.windows\\4.0.0\\system.security.principal.windows.4.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.servicemodel.http\\6.2.0\\system.servicemodel.http.6.2.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.servicemodel.primitives\\6.2.0\\system.servicemodel.primitives.6.2.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.text.encodings.web\\9.0.0\\system.text.encodings.web.9.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.text.json\\9.0.0\\system.text.json.9.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.text.regularexpressions\\4.3.0\\system.text.regularexpressions.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.threading.thread\\4.0.0\\system.threading.thread.4.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.threading.threadpool\\4.0.10\\system.threading.threadpool.4.0.10.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.threading.timer\\4.3.0\\system.threading.timer.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.windows.extensions\\6.0.0\\system.windows.extensions.6.0.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.xml.xdocument\\4.3.0\\system.xml.xdocument.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.xml.xmldocument\\4.3.0\\system.xml.xmldocument.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.xml.xmlserializer\\4.3.0\\system.xml.xmlserializer.4.3.0.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.xml.xpath\\4.0.1\\system.xml.xpath.4.0.1.nupkg.sha512", "C:\\.nuget\\vc_packages\\system.xml.xpath.xmldocument\\4.0.1\\system.xml.xpath.xmldocument.4.0.1.nupkg.sha512"], "logs": [{"code": "NU1603", "level": "Warning", "message": "Microsoft.PowerPlatform.Dataverse.Client.Dynamics 1.2.2 depends on Microsoft.PowerPlatform.Dataverse.Client (>= 1.2.0) but Microsoft.PowerPlatform.Dataverse.Client 1.2.0 was not found. Microsoft.PowerPlatform.Dataverse.Client 1.2.1 was resolved instead.", "projectPath": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\Abt.Epdt.WebApis.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\Abt.Epdt.WebApis.csproj", "libraryId": "Microsoft.PowerPlatform.Dataverse.Client", "targetGraphs": ["net8.0"]}, {"code": "NU1902", "level": "Warning", "message": "Package 'Microsoft.AspNetCore.Authentication.JwtBearer' 3.1.12 has a known moderate severity vulnerability, https://github.com/advisories/GHSA-q7cg-43mg-qp69", "projectPath": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\Abt.Epdt.WebApis.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\Abt.Epdt.WebApis.csproj", "libraryId": "Microsoft.AspNetCore.Authentication.JwtBearer", "targetGraphs": ["net8.0"]}, {"code": "NU1903", "level": "Warning", "message": "Package 'SixLabors.ImageSharp' 3.1.6 has a known high severity vulnerability, https://github.com/advisories/GHSA-2cmq-823j-5qj8", "projectPath": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\Abt.Epdt.WebApis.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\workspace\\code\\dotnet\\abt-pp-epdt\\Abt.Epdt.WebApi\\Abt.Epdt.WebApis\\Abt.Epdt.WebApis.csproj", "libraryId": "SixLabors.ImageSharp", "targetGraphs": ["net8.0"]}]}