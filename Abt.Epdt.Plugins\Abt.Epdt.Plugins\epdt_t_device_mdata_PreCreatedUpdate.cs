﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;

namespace Abt.Epdt.Plugins
{
    public class epdt_t_device_mdata_PreCreatedUpdate : IPlugin
    {
        public void Execute(IServiceProvider serviceProvider)
        {
            // 二期中已被禁用，请勿调用

            //IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            //IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            //IOrganizationService service = serviceFactory.CreateOrganizationService(null); // 权限
            //if (context.InputParameters.Contains("Target"))
            //{
            //    string msg = "";

            //    Entity entity = (Entity)context.InputParameters["Target"];

            //    if (entity.Contains("epdt_implant_hospital_code"))
            //    {
            //        string hoscode = entity.GetAttributeValue<string>("epdt_implant_hospital_code");

            //        string fetchxml = $@"<fetch xmlns:generator='MarkMpn.SQL4CDS' top='10' >
            //        <entity name='epdt_t_hospital_basic_mdata' >
            //          <attribute name='epdt_t_hospital_basic_mdataid' />
            //          <attribute name='epdt_name' />
            //          <filter>
            //            <condition attribute='epdt_hospital_code' operator='eq' value='{hoscode}' />
            //            <condition attribute='statecode' operator='eq' value='0' />
            //          </filter>
            //        </entity>
            //      </fetch>";
            //        EntityCollection entityCollection = service.RetrieveMultiple(new FetchExpression(fetchxml));
            //        if (entityCollection.Entities.Count == 0)
            //        {
            //            //查询此医院是否存在
            //            msg = msg + "医院编码不存在。\r\n";
            //        }
            //        else
            //        {
            //            entity.Attributes["epdt_implant_hospital_name"] = entityCollection.Entities[0].GetAttributeValue<string>("epdt_name");

            //        }
            //    }

            //    if (entity.Contains("epdt_device_status"))
            //    {
            //        string status = entity.GetAttributeValue<string>("epdt_device_status");
            //        if (status != "已审批" && status != "已打印" && status != "已回收" && status != "已冲销")
            //        {
            //            msg = msg + "设备识别卡状态应为【已审批、已打印、已回收、已冲销】。\r\n";
            //        }
            //    }

            //    //Tina Modified：校验 设备识别卡号必填
            //    if (!entity.Attributes.Contains("epdt_name") || entity.Attributes["epdt_name"] == null)
            //    {
            //        msg = "请填写设备识别卡号";
            //    }

            //    if (!string.IsNullOrWhiteSpace(msg))
            //    {
            //        throw new InvalidPluginExecutionException(msg);
            //    }

            //}

        }
    }
}
