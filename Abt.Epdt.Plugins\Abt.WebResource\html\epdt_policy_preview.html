
<html><head>
    <meta charset="utf-8">
    <title></title>
    <style>
        html,body{
            background:#eee;
            font-family:'Microsoft YaHei',微软雅黑,'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
        }
        #app {
            width: 350px;
            box-sizing: border-box;
            margin: 0 auto;
            background:#fff;
            height: 600px;
            padding:30px;
            overflow-y:"scroll";
            border-radius: 18px;
        }
    </style>
<meta></head>

<body>
    <div id="app"></div>

<script type="text/javascript">

    window.onload = function () {

        function addZero(num) {
            if (num < 10) {
                return '0' + num;
            }
            return '' + num;
        }

        Date.prototype.format = function (format) {
            return format.replace("yyyy", this.getFullYear()).replace("MM", addZero(this.getMonth() + 1)).replace("dd", addZero(this.getDate()));
        }

        const formContext = window.top.Xrm.Page.ui.formContext;

        const lettercontent = formContext.getAttribute('epdt_policy_term_content').getValue();

        let container = document.getElementById("app");

        container.innerHTML = `${lettercontent}`;
    }

</script>


</body></html>