﻿using System;
using Microsoft.Xrm.Sdk;
using Abbott.Onepdt.Plugins.Service;
using Abbott.Onepdt.Plugins.Models;
using Microsoft.Xrm.Sdk.Query;
using System.Linq;

namespace Abbott.Onepdt.Plugins.Actions
{
    public class DecrypImage : IPlugin
    {
        static readonly string SEED = "abbott@AES@key";

        static readonly string key = "X33WNf6BAQQ/Bf1uyJ/eKw==";

        public void Execute(IServiceProvider serviceProvider)
        {
            IPluginExecutionContext context = (IPluginExecutionContext)serviceProvider.GetService(typeof(IPluginExecutionContext));
            IOrganizationServiceFactory serviceFactory = (IOrganizationServiceFactory)serviceProvider.GetService(typeof(IOrganizationServiceFactory));
            IOrganizationService service = serviceFactory.CreateOrganizationService(null); // 权限

            ITracingService tracingService = (ITracingService)serviceProvider.GetService(typeof(ITracingService));
            try
            {
                string Image = (string)context.InputParameters["Image"];
                string OperationNumber = (string)context.InputParameters["OperationNumber"];
                string CreateOn = (string)context.InputParameters["createon"];
                string Name = (string)context.InputParameters["Name"];

                string base64String = DecrypImageService.Decrypt(Convert.FromBase64String(Image), key);


                QueryExpression OperationQuery = new QueryExpression("onepdt_t_operation")
                {
                    ColumnSet = new ColumnSet(true)
                };
                OperationQuery.Criteria.AddCondition("onepdt_name", ConditionOperator.Equal, OperationNumber);
                EntityCollection OperationEntites = service.RetrieveMultiple(OperationQuery);
                Entity OperationEntity = OperationEntites.Entities.FirstOrDefault();

                // create a record in onepdt_t_opreation_photo
                Entity photo = new Entity("onepdt_t_opreation_photo");

                photo["onepdt_operation"] = new EntityReference("onepdt_t_operation", OperationEntity.Id);

                photo["onepdt_name"] = Name;

                photo["onepdt_operation_number"] = OperationNumber;

                photo["onepdt_photo"] = Convert.FromBase64String(base64String);

                Guid photoId = service.Create(photo);

                context.OutputParameters["Result"] = photoId.ToString();
            }
            catch (Exception ex)
            {
                tracingService.Trace(ex.Message + ex.StackTrace);
                throw;
            }
        }

      


    }
}
