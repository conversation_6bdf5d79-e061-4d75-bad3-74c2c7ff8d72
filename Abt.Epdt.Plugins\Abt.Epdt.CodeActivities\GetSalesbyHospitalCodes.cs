﻿using Abt.Epdt.CodeActivities.Models;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Microsoft.Xrm.Sdk.Workflow;
using Newtonsoft.Json;
using System.Activities;
using System.Collections.Generic;
using System.Linq;

namespace Abt.Epdt.CodeActivities
{
    public class GetSalesbyHospitalCodes : CodeActivity
    {
        [Input("HospitalCodes")]
        public InArgument<string> HospitalCodesArgument { get; set; }

        [Output("Result")]
        public OutArgument<string> ResultArgument { get; set; }
        protected override void Execute(CodeActivityContext context)
        {
            ITracingService tracingService = context.GetExtension<ITracingService>();
            IWorkflowContext workflowExecutionContext = context.GetExtension<IWorkflowContext>();
            IOrganizationServiceFactory factory = context.GetExtension<IOrganizationServiceFactory>();
            IOrganizationService orgSvc = factory.CreateOrganizationService(workflowExecutionContext.UserId);

            string codes = HospitalCodesArgument.Get<string>(context);
            var codeArr = codes.Split(',');

            //查询销售代表组织架构关系
            var qe = new QueryExpression("epdt_t_sales_org_stru_mdata");
            qe.ColumnSet = new ColumnSet("epdt_psr_id", "epdt_psr_name");
            qe.Criteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            qe.Criteria.AddCondition("epdt_psr_id", ConditionOperator.NotNull);
            qe.Criteria.AddCondition("epdt_psr_name", ConditionOperator.NotNull);
            qe.Distinct = true;

            if (!string.IsNullOrWhiteSpace(codes) && codeArr != null && codeArr.Length > 0)
            {
                var link = qe.AddLink("epdt_t_hospital_sales_mdata", "epdt_psr_id", "epdt_user_code", JoinOperator.Inner);
                link.LinkCriteria.AddCondition("epdt_hospital_code3", ConditionOperator.In, codeArr);
                link.LinkCriteria.AddCondition("statecode", ConditionOperator.Equal, 0);
            }

            var ec = orgSvc.RetrieveMultiple(qe);
            var result = new List<HospitalSalesModel>();

            if (ec != null && ec.Entities.Count > 0)
            {
                foreach (var item in ec.Entities)
                {
                    var model = new HospitalSalesModel();
                    model.psr_id = item.GetAttributeValue<string>("epdt_psr_id");
                    model.psr_name = item.GetAttributeValue<string>("epdt_psr_name");
                    result.Add(model);
                }
            }

            result.Distinct().OrderBy(p => p.psr_name);

            ResultArgument.Set(context, JsonConvert.SerializeObject(result));
        }
    }
}
