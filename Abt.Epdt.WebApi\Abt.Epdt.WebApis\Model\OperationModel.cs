﻿using System.Collections.Generic;

namespace Abt.Epdt.WebApis.Model
{
    public class OperationModel
    {
        /// <summary>
        /// 数据id
        /// </summary>
        public string id { get; set; }
        /// <summary>
        /// 关联电子保卡
        /// </summary>
        public string onepdt_device_applicationname { get; set; }
        /// <summary>
        /// 数据实体名称
        /// </summary>
        public string entityname { get; set; }
        /// <summary>
        /// 手术编号
        /// </summary>
        public string onepdt_name { get; set; } = "";
        /// <summary>
        /// 医院名称
        /// </summary>
        public string onepdt_hospitalname { get; set; } = "";
        /// <summary>
        /// 医院编码
        /// </summary>
        public string onepdt_hospitalcode { get; set; } = "";
        /// <summary>
        /// 术者
        /// </summary>
        public string onepdt_hcp_text { get; set; } = "";
        /// <summary>
        /// 患者姓名
        /// </summary>
        public string onepdt_patient { get; set; } = "";
        /// <summary>
        /// 患者性别
        /// </summary>
        public string onepdt_gender { get; set; } = "";
        /// <summary>
        /// 提交状态 周报/跟台
        /// </summary>
        public string onepdt_submit_status_label { get; set; } = "";
        /// <summary>
        /// 植入日期
        /// </summary>
        public string onepdt_date { get; set; } = "";
        /// <summary>
        /// 填报人名称
        /// </summary>
        public string onepdt_submittername { get; set; }
        /// <summary>
        /// 填报人id
        /// </summary>
        public string onepdt_submitterid { get; set; }
        /// <summary>
        /// 审批状态
        /// </summary>
        public string onepdt_approval_status_label { get; set; } = "";
        /// <summary>
        /// 邮寄审批状态
        /// </summary>
        public string onepdt_address_approval_status_label { get; set; } = "";
        /// <summary>
        /// 审批人
        /// </summary>
        public string onepdt_approver_text { get; set; } = "";
        /// <summary>
        /// 主机型号
        /// </summary>
        public string onepdt_product_name { get; set; } = "";
        /// <summary>
        /// 主机产品序列号
        /// </summary>
        public string onepdt_product_sn { get; set; } = "";
        /// <summary>
        /// 物流单号
        /// </summary>
        public string onepdt_logistics_no { get; set; } = "";
        /// <summary>
        /// 归档状态
        /// </summary>
        public string onepdt_archival_statuslabel { get; set; } = "";
        /// <summary>
        /// 打印状态
        /// </summary>
        public string onepdt_print_statuslabel { get; set; } = "";
        /// <summary>
        /// 退回原因
        /// </summary>
        public string onepdt_comment { get; set; } = "";
    }

    public class OperationData
    {
        public int total { get; set; }
        public int operationtotal { get; set; }
        public List<OperationModel> datas { get; set; }
        public List<TypeInfo> types { get; set; }
    }

    public class TypeInfo
    {
        public string typename { get; set; }
        public bool ishowdot { get; set; }
    }

    public class OnepdtConfig
    {
        public string text { get; set; }
        public string value { get; set; }

        public string configValue { get; set; }
    }

    public class Dapplication
    {
        public string id { get; set; } = "";
        public string patientName { get; set; } = "";
        public string patientGender { get; set; } = "";
        public string implantDate { get; set; } = "";
        public string hospital { get; set; } = "";
        public string hospitalcode { get; set; } = "";
        public string hospitalid { get; set; } = "";
        public string recipientname { get; set; } = "";
        public string recipientphone { get; set; } = "";
        public string mailingaddress { get; set; } = "";
        public bool epdt_if_need_paper_card { get; set; }
        public string epdt_card_acquisition_method { get; set; }
    }

    public class HospitalInfo
    {
        public string id { get; set; } = "";
        public string name { get; set; } = "";
        public string code { get; set; } = "";
    }

    public class HCPInfo
    {
        public string id { get; set; } = "";
        public string name { get; set; } = "";
        public string department { get; set; } = "";
        public string hospital { get; set; } = "";
    }

    public class OperationEditData
    {
        public string email { get; set; }
        public string id { get; set; }
        /// <summary>
        /// 关联系统信息——保卡
        /// </summary>
        public string deviceapplicationid { get; set; }
        /// <summary>
        /// 患者姓名
        /// </summary>
        public string devpatientName { get; set; }
        /// <summary>
        /// 患者性别
        /// </summary>
        public string devpatientGender { get; set; }
        /// <summary>
        /// 植入医院名字
        /// </summary>
        public string devimplantHospital { get; set; }
        /// <summary>
        /// 医院代码
        /// </summary>
        public string devimplantHospitalCode { get; set; }
        /// <summary>
        /// 植入医院id
        /// </summary>
        public string devimplantHospitalId { get; set; }
        /// <summary>
        /// 植入日期
        /// </summary>
        public string devimplantDate { get; set; }
        /// <summary>
        /// 患者姓名
        /// </summary>
        public string patientName { get; set; }
        /// <summary>
        /// 患者性别
        /// </summary>
        public string patientGender { get; set; }
        /// <summary>
        /// 植入医院名字
        /// </summary>
        public string implantHospital { get; set; }
        /// <summary>
        /// 医院代码
        /// </summary>
        public string implantHospitalCode { get; set; }
        /// <summary>
        /// 植入医院id
        /// </summary>
        public string implantHospitalId { get; set; }
        /// <summary>
        /// 植入日期
        /// </summary>
        public string implantDate { get; set; }
        /// <summary>
        /// 第一术者名称
        /// </summary>
        public string firstSurgeon { get; set; }
        /// <summary>
        /// 第一术者id
        /// </summary>
        public string firstSurgeonid { get; set; }
        /// <summary>
        /// 第二术者名称
        /// </summary>
        public string secondSurgeon { get; set; }
        /// <summary>
        /// 第二术者id
        /// </summary>
        public string secondSurgeonid { get; set; }
        /// <summary>
        /// 第三术者名称
        /// </summary>
        public string thirdSurgeon { get; set; }
        /// <summary>
        /// 第三术者id
        /// </summary>
        public string thirdSurgeonid { get; set; }
        /// <summary>
        /// 跟台类型名称
        /// </summary>
        public string surgicalAssistantType { get; set; } = "";
        /// <summary>
        /// 跟台类型id
        /// </summary>
        public string surgicalAssistantTypeid { get; set; }
        /// <summary>
        /// 跟台姓名
        /// </summary>
        public string surgicalAssistant { get; set; }
        /// <summary>
        /// 跟台耗时
        /// </summary>
        public string surgeonDuration { get; set; } = "";
        /// <summary>
        /// 跟台耗时id
        /// </summary>
        public string surgeonDurationid { get; set; }
        /// <summary>
        /// 希浦系统起搏
        /// </summary>
        public string hisPurkinjeSystemPacing { get; set; } = "";
        /// <summary>
        /// 希浦系统起搏id
        /// </summary>
        public string hisPurkinjeSystemPacingid { get; set; }
        /// <summary>
        /// 是否尝试心房生理性起搏
        /// </summary>
        public string xfsystem { get; set; } = "";
        /// <summary>
        /// 是否尝试心房生理性起搏id
        /// </summary>
        public string xfsystemId { get; set; }
        /// <summary>
        /// 成功植入使用的工具
        /// </summary>
        public string success_tool_list { get; set; }
        /// <summary>
        /// 尝试工具
        /// </summary>
        public string failed_tool_list { get; set; }
        /// <summary>
        /// 尝试工具
        /// </summary>
        public List<OnepdtConfig> failtoolslist { get; set; }
        /// <summary>
        /// ICD手术预防等级
        /// </summary>
        public string icdSurgeryProphylacticLevel { get; set; } = "";
        /// <summary>
        /// ICD手术预防等级id
        /// </summary>
        public string icdSurgeryProphylacticLevelid { get; set; }
        /// <summary>
        /// ICD患者是否有冠脉病史
        /// </summary>
        public bool isIcdPatientWithCadHistory { get; set; }
        /// <summary>
        /// 植入类型id
        /// </summary>
        public string implantTypeid { get; set; }
        /// <summary>
        /// 植入类型
        /// </summary>
        public string implantType { get; set; } = "";
        /// <summary>
        /// 原主机类型id
        /// </summary>
        public string originaldeviceid { get; set; }
        /// <summary>
        /// 原主机类型
        /// </summary>
        public string originaldevice { get; set; }
        /// <summary>
        /// 现主机类型id
        /// </summary>
        public string replaceddeviceid { get; set; }
        /// <summary>
        /// 现主机类型
        /// </summary>
        public string replaceddevice { get; set; }
        /// <summary>
        /// 历史跟台
        /// </summary>
        public string relatedoperationid { get; set; }
        public string relatedoperationpatient { get; set; }
        public string relatedoperationgender { get; set; }
        public string relatedoperationdate { get; set; }
        public string relatedoperationhospitalname { get; set; }
        public string relatedoperationproduct_name { get; set; }
        public string relatedoperationproduct_sn { get; set; }
        /// <summary>
        /// 是否邮寄保卡
        /// </summary>
        public bool isWarrantyCardMailed { get; set; }
        /// <summary>
        /// 是否使用K鞘value
        /// </summary>
        public string ksheathvalue { get; set; }
        /// <summary>
        /// 是否使用K鞘
        /// </summary>
        public string ksheathlabel { get; set; }
        /// <summary>
        /// 收件人类型
        /// </summary>
        public string receType { get; set; }
        /// <summary>
        /// 邮寄地址
        /// </summary>
        public string address { get; set; }
        /// <summary>
        /// 收件人姓名
        /// </summary>
        public string rece { get; set; }
        /// <summary>
        /// 联系电话
        /// </summary>
        public string tel { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string messageNote { get; set; }
        /// <summary>
        /// 提交状态
        /// </summary>
        public string onepdt_submit_status_label { get; set; }
        /// <summary>
        /// 退回原因
        /// </summary>
        public string rejectreason { get; set; }
        /// <summary>
        /// 业务审批备注
        /// </summary>
        public string onepdt_approval_comment { get; set; }
        /// <summary>
        /// 业务审批标签
        /// </summary>
        public string onepdt_approval_tag { get; set; }
        /// <summary>
        /// 邮寄审批备注
        /// </summary>
        public string onepdt_address_approval_comment { get; set; }
        /// <summary>
        /// 邮寄审批标签
        /// </summary>
        public string onepdt_address_approval_tag { get; set; }
        /// <summary>
        /// 业务审批状态value
        /// </summary>
        public int? onepdt_approval_status { get; set; }
        /// <summary>
        /// 业务审批状态label
        /// </summary>
        public string onepdt_approval_statuslabel { get; set; }
        /// <summary>
        /// 邮寄审批状态value
        /// </summary>
        public int? onepdt_address_approval_status { get; set; }
        /// <summary>
        /// 邮寄审批状态label
        /// </summary>
        public string onepdt_address_approval_statuslabel { get; set; }
        /// <summary>
        /// 是否退回
        /// </summary>
        public bool onepdt_is_returned { get; set; }
        /// <summary>
        /// 授权书
        /// </summary>
        public List<OpreationPhoto> authorizationLetter { get; set; }
        /// <summary>
        /// 回执单
        /// </summary>
        public List<OpreationPhoto> receiptForm { get; set; }
        /// <summary>
        /// 跟台植入产品明细
        /// </summary>
        public List<OperationProductDetail> details { get; set; }

        /// <summary>
        /// 删除跟台产品
        /// </summary>
        public bool deletePreviousProduct { get; set; }
        /// <summary>
        /// 是否可编辑
        /// </summary>
        public bool isedit { get; set; } = true;
        public string submitteremail { get; set; }
    }

    public class OperationProductDetail
    {
        public string id { get; set; }
        public string serialNumber { get; set; }
        public string productid { get; set; }
        public string model { get; set; }
        public string bigType { get; set; }
        public string category { get; set; }
        /// <summary>
        /// 是否非临床试验
        /// </summary>
        public bool isNonClinicalTrial { get; set; }
        /// <summary>
        /// 是否损耗
        /// </summary>
        public bool isWearAndTear { get; set; }
        /// <summary>
        /// 是否非大陆产品
        /// </summary>
        public bool isNonMainlandProduct { get; set; }
        /// <summary>
        /// SAP产品
        /// </summary>
        public List<SAPProduct> SAPProduct { get; set; }
        /// <summary>
        /// 是否需要删除
        /// </summary>
        public bool deflag { get; set; }

    }

    public class OpreationPhoto
    {
        public string id { get; set; }
        public string content { get; set; }
        public FileModel file { get; set; }
    }

    public class FileModel
    {
        public string name { get; set; }
        public int size { get; set; }
        public string type { get; set; }
    }

    public class OnePDTProduct
    {
        public string id { get; set; }
        /// <summary>
        /// 产品型号
        /// </summary>
        public string onepdt_name { get; set; }
        /// <summary>
        /// 产品类别
        /// </summary>
        public string onepdt_type { get; set; }
        /// <summary>
        /// 产品分类
        /// </summary>
        public string onepdt_classification { get; set; }
        /// <summary>
        /// 产品大类
        /// </summary>
        public string onepdt_category { get; set; }
    }

    public class SAPProduct
    {
        public string id { get; set; }
        public string zmod { get; set; }
    }

    public class aa
    {
        public string code { get; set; }
        public string message { get; set; }
        public CardTypeModel data { get; set; }
    }

    public class CardTypeModel
    {
        /// <summary>
        /// 产品植入ID
        /// </summary>
        public string onepdt_t_operation_implantid { get; set; }
        /// <summary>
        /// 患者姓名
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 患者性别
        /// </summary>
        public string Gender { get; set; }
        /// <summary>
        /// 植入日期
        /// </summary>
        public string ImpDate { get; set; }
        /// <summary>
        /// 植入医生1
        /// </summary>
        public string Physician1 { get; set; }
        /// <summary>
        /// 植入医生2
        /// </summary>
        public string Physician2 { get; set; }
        /// <summary>
        /// 医院
        /// </summary>
        public string Hospital { get; set; }
        /// <summary>
        /// 医院地址
        /// </summary>
        public string HospitalAdd { get; set; }
        /// <summary>
        /// 医院电话
        /// </summary>
        public string HospitalTel { get; set; }
        /// <summary>
        /// 卡类型（A/B/C）
        /// </summary>
        public string CardType { get; set; }
        /// <summary>
        /// 卡编号
        /// </summary>
        public string CardNo { get; set; } = "";
        /// <summary>
        /// 产品型号
        /// </summary>
        public string ModelNo { get; set; }
        /// <summary>
        /// 产品序列号
        /// </summary>
        public string SN { get; set; }
        /// <summary>
        /// 担保日期
        /// </summary>
        public string WarrantyPeriod { get; set; }
        /// <summary>
        /// 制造商
        /// </summary>
        public string Manufacturer { get; set; }
        /// <summary>
        /// 所选起搏模式
        /// </summary>
        public string SelectedMode { get; set; }
        /// <summary>
        /// 主要起搏模式
        /// </summary>
        public string MainMode { get; set; }
        /// <summary>
        /// 扫描强度
        /// </summary>
        public string ScanIntensity { get; set; }
        /// <summary>
        /// 配件列表
        /// </summary>
        public List<Accessory> Accessories { get; set; }

        public class Accessory
        {
            /// <summary>
            /// 产品型号
            /// </summary>
            public string ModelNo { get; set; }
            /// <summary>
            /// 产品序列号
            /// </summary>
            public string SN { get; set; }
            /// <summary>
            /// 植入日期
            /// </summary>
            public string ImpDate { get; set; }
            /// <summary>
            /// 制造商
            /// </summary>
            public string Manufacturer { get; set; }
        }

        public class BatchDeleteModel
        {
            public string entityname { get; set; }
            public List<string> entityids { get; set; }
        }

        public class VerificationReq
        {
            public string MainId { get; set; }
            public string Id { get; set; }
            public string SN { get; set; }
            public string Model { get; set; }
            public string ImplantDate { get; set; }
            public string Nonmainland { get; set; } = "N";
        }

        public class VerificationResult
        {
            public string ResultType { get; set; }
            public string SN { get; set; }
            public string model { get; set; }
            public string Message { get; set; }
            public bool IsExpired { get; set; }
        }

        public class QueryModel
        {
            public string id { get; set; }
            public string text { get; set; }
            public string text1 { get; set; }
            public string text2 { get; set; }
        }
    }

}
