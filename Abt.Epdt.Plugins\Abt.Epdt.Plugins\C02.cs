﻿using Microsoft.Xrm.Sdk;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abt.Epdt.Plugins
{
    public class C02_Register
    {
        public string RegisterName { get; set; }
        public string epdt_register_phonenumber1 { get; set; }
        /// <summary>
        /// 脱敏后
        /// </summary>
        public string epdt_register_phonenumber1_ { get; set; }
        public string epdt_verification_code { get; set; }
        public string epdt_register_id_no1 { get; set; }
        /// <summary>
        /// 脱敏后
        /// </summary>
        public string epdt_register_id_no1_ { get; set; }
    }

    public class C02_PatientInfo
    {
        public string epdt_patient_name { get; set; }
        public string epdt_patient_gender { get; set; }
        public string epdt_patient_id { get; set; }
        /// <summary>
        /// 脱敏后
        /// </summary>
        public string epdt_patient_id_ { get; set; }
        public string epdt_pitient_phone_no1 { get; set; }
        /// <summary>
        /// 脱敏后
        /// </summary>
        public string epdt_pitient_phone_no1_ { get; set; }
        public string epdt_verification_code { get; set; }
        public int epdt_idtype { get; set; }
        public bool? epdt_if_need_paper_card { get; set; }
        public bool? epdt_card_acquisition_method { get; set; }
        public string epdt_mailing_address { get; set; }
        public string epdt_recipient_name { get; set; }
        public string epdt_recipient_phone { get; set; }
        public bool? epdt_if_need_follow_up_reminder_service { get; set; }

    }

    public class C02_RelationInfo
    {
        public string epdt_relationship_with_patient { get; set; }
        public string epdt_other_relationship_with_patient { get; set; }
        public string epdt_if_default_display_patient { get; set; }
    }

    public class C02_RelationRelation
    {
        public string epdt_relationship_with_patient { get; set; }
        public string epdt_other_relationship_with_patient { get; set; }
        public string epdt_if_default_display_patient { get; set; }
    }


    public class C02_DeviceInfo
    {
        public string epdt_t_device_applicationid { get; set; }
        public string Hospitalcode { get; set; }
        public string implant_date { get; set; }
    }


    public class C02_PolicyInfo
    {
        public string policy_term_id { get; set; }
        public string informed_concent_term_id { get; set; }
        public string privacy_auth_page1 { get; set; }
    }
}
