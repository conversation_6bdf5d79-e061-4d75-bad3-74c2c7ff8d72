﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Microsoft.Xrm.Sdk.Workflow;
using Newtonsoft.Json;
using System;
using System.Activities;
using System.Collections.Generic;
using System.Linq;

namespace Abt.Epdt.CodeActivities
{
    /// <summary>
    /// 电子保卡批量销售复核操作
    /// </summary>
    public class DeviceApplicationSalesReview : CodeActivity
    {
        [Input("DeviceApplicationIds")]
        public InArgument<string> DeviceApplicationIdsArgument { get; set; }

        [Output("Result")]
        public OutArgument<string> ResultArgument { get; set; }

        protected override void Execute(CodeActivityContext context)
        {
            ITracingService tracingService = context.GetExtension<ITracingService>();
            IWorkflowContext workflowExecutionContext = context.GetExtension<IWorkflowContext>();
            IOrganizationServiceFactory factory = context.GetExtension<IOrganizationServiceFactory>();
            IOrganizationService orgSvc = factory.CreateOrganizationService(workflowExecutionContext.UserId);

            string ids = DeviceApplicationIdsArgument.Get<string>(context);
            var idArr = ids.ToLower().Split(',');

            //查询选中的【EPDT电子植入设备识别卡发放申请】数据
            var qe = new QueryExpression("epdt_t_device_application")
            {
                ColumnSet = new ColumnSet("epdt_name", "epdt_t_device_applicationid")
            };
            qe.Criteria.AddCondition("epdt_check_status", ConditionOperator.Equal, "匹配失败");
            qe.Criteria.AddCondition("epdt_if_delete_device_application", ConditionOperator.Equal, false); //是否删除设备申请为否
            qe.Criteria.AddCondition("epdt_sales_approve_status", ConditionOperator.NotEqual, 0); //复核状态不为待复核
            qe.Criteria.AddCondition("epdt_review_results", ConditionOperator.NotEqual, "无此植入");
            qe.Criteria.AddCondition("epdt_t_device_applicationid", ConditionOperator.In, idArr);
            qe.Distinct = true;

            var link = qe.AddLink("epdt_t_hospital_basic_mdata", "epdt_hospital_code", "epdt_t_hospital_basic_mdataid", JoinOperator.Inner);
            link.EntityAlias = "hmd";
            var link2 = link.AddLink("epdt_t_hospital_sales_mdata", "epdt_hospital_code", "epdt_hospital_code3", JoinOperator.Inner);
            link2.EntityAlias = "hsmd";
            link2.LinkCriteria.AddCondition("epdt_user_code", ConditionOperator.NotNull);

            var salesReviewCard = ""; //记录销售复核成功卡号
            var salesReviewExeFailedCard = ""; //记录更新报错保卡号及报错原因
            var ec = orgSvc.RetrieveMultiple(qe);
            if (ec != null || ec.Entities.Count > 0)
            {
                //获取存在销售代表的数据ID
                var hasSalesList = (from id in idArr
                                    from p in ec.Entities.AsEnumerable()
                                    where id == p.Id.ToString().ToLower()
                                    select p).ToList();

                if (hasSalesList != null && hasSalesList.Count > 0)
                {
                    foreach (var item in hasSalesList)
                    {
                        try
                        {
                            var ent = new Entity("epdt_t_device_application")
                            {
                                Id = item.Id
                            };
                            ent["epdt_sales_approve_status"] = new OptionSetValue(0); //待复核
                            ent["epdt_transfer_to_approve_date"] = DateTime.UtcNow; //提交复核时间
                            ent["epdt_operation"] = "销售复核"; // 操作

                            orgSvc.Update(ent);

                            salesReviewCard += item.GetAttributeValue<string>("epdt_name") + "，";
                        }
                        catch (Exception ex)
                        {
                            salesReviewExeFailedCard += item.GetAttributeValue<string>("epdt_name") + "，报错信息：" + ex.Message + "\n";
                        }
                    }
                }
            }

            //获取没有销售代表的数据
            var hasSalesIdList = ec.Entities.AsEnumerable().Select(p => p.Id.ToString().ToLower()).ToArray();
            var noSalesList = idArr.Except(hasSalesIdList).ToArray();

            var noSalesCard = ""; //记录未找到销售代表保卡号
            if (noSalesList != null && noSalesList.Count() > 0)
            {
                qe = new QueryExpression("epdt_t_device_application")
                {
                    ColumnSet = new ColumnSet("epdt_name")
                };
                qe.Criteria.AddCondition("epdt_t_device_applicationid", ConditionOperator.In, noSalesList.ToArray());

                ec = orgSvc.RetrieveMultiple(qe);
                if (ec != null && ec.Entities.Count > 0)
                {
                    foreach (var item in ec.Entities)
                    {
                        noSalesCard += item.GetAttributeValue<string>("epdt_name") + "，";
                    }
                }
            }

            //如果有执行错误，统一抛错。整个程序动作会回滚
            if (!string.IsNullOrWhiteSpace(salesReviewExeFailedCard))
            {
                throw new Exception("销售复核出错: " + salesReviewExeFailedCard);
            }

            ResultArgument.Set(context, JsonConvert.SerializeObject(
                new
                {
                    salesReviewCard = !string.IsNullOrWhiteSpace(salesReviewCard) ? salesReviewCard.Remove(salesReviewCard.Length - 1) : "",
                    noSalesCard = !string.IsNullOrWhiteSpace(noSalesCard) ? noSalesCard.Remove(noSalesCard.Length - 1) : ""
                }));
        }

    }
}
