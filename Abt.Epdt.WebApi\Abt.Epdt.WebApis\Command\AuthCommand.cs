﻿using Abt.Epdt.WebApis.Util;
using System.Collections.Generic;
using System.IdentityModel.Tokens;
using System.Security.Claims;
using System.Text;
using System;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.Xrm.Sdk;
using Microsoft.IdentityModel.Tokens;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Threading.Tasks;
using Microsoft.Xrm.Sdk.Query;
using Microsoft.Extensions.Caching.Memory;

namespace Abt.Epdt.WebApis.Command
{
    public class AuthCommand : OrgService
    {
        public AuthCommand(IMemoryCache memoryCache) : base(memoryCache)
        {
        }

        /// <summary>
        /// 检验appid
        /// </summary>
        /// <param name="appid">Appid</param>
        /// <exception cref="Exception"></exception>
        public void Checkappid(string appid)
        {
            var qe = new QueryExpression("environmentvariablevalue");
            qe.ColumnSet.AllColumns = true;
            qe.Criteria.AddCondition("value", ConditionOperator.Equal, appid);
            var ec = OrganizationServiceAdmin.RetrieveMultiple(qe);
            if (ec == null || ec.Entities.Count == 0)
            {
                throw new Exception("appid参数不在配置范围");
            }
        }
        
    }

    public class ApiModel
    {
        public string jjmc_name { get; set; }
    }

    public class LoginViewModel
    {
        public string key { get; set; }
    }
}
