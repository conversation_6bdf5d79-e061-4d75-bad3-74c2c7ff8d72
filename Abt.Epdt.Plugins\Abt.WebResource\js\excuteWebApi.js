//var BaseURL = "";
//var AccessToken = "";

function excuteApi(apiName) {
    var confirmOptions = { height: 200, width: 300 };
    var confirmStrings = { title: "确认框", text: "确认同步？" };
    // 1. 定义操作名称
    var actionName = "onepdt_SyncEmployee"; // 自定义操作名称

    var req = {
        API: apiName
    };
    req.getMetadata = function () {
        return {
            boundParameter: null,
            parameterTypes: {
                "API": {
                    "typeName": "Edm.String",
                    "structuralProperty": 1
                },
            },
            operationType: 0,
            operationName: actionName
        };
    };

    Xrm.Navigation.openConfirmDialog(confirmStrings, confirmOptions).then(function (success) {
        if (success.confirmed) {
            // 4. 通过 Xrm.WebApi.online.execute() 调用操作（异步执行）
            Xrm.WebApi.online.execute(req).then(
                function (response) {
                    if (response.ok) {
                    }
                    Xrm.Utility.alertDialog("同步操作已提交，请耐心等待执行完成！");
                },
                function (error) {
                    Xrm.Utility.alertDialog(error.message);
                }
            );
        }
    })
    
}


//function excuteApi(api) {
//    getURL();
//    createToken();

//    var userid = Xrm.Page.context.getUserId();

//    // 构建查询字符串
//    var query = "?$filter=epdt_name eq '" + api + "' and epdt_api_body eq '" + userid.replace("{", "").replace("}", "") + "' and epdt_api_statuscode eq '进行中'&$select=epdt_name,epdt_api_body,epdt_api_statuscode";

//    // 使用 Xrm.WebApi 查询数据
//    var response = await Xrm.WebApi.retrieveMultipleRecords("epdt_t_api_log", query);

//    if (response.entities.length > 0) {
//        // 如果查询到符合条件的记录
//        Xrm.Utility.alertDialog("您已发起同步数据请求,正在执行中...");
//    } else {
//        await createMQ(api, userid);  // 等待 MQ 创建完毕
//    }
//}
//function createMQ(api, userid) {
//    var data =
//    {
//        "epdt_name": api,
//        "epdt_api_name": api,
//        "epdt_api_description": api == "UserOrgDataAll" ? "销售架构数据同步" : "医院销售关系同步",
//        "epdt_api_body": userid,
//        "epdt_api_statuscode": "进行中"
//    }

//    try {
//        // 创建 epdt_t_api_log 记录
//        var result = await Xrm.WebApi.createRecord("epdt_t_api_log", data);
//        console.log("epdt_t_api_log created with ID: " + result.id);

//        // 发起获取数据请求
//        getDataAll(result.id, api);  // 等待数据获取完成
//        Xrm.Utility.alertDialog("发起成功，请耐心等待执行完成");
//    } catch (error) {
//        console.error("创建MQ时发生错误: ", error.message);
//        Xrm.Utility.alertDialog("创建MQ时发生错误: " + error.message);
//    }
//}

////获取请求地址
//function getURL() {
//    var query = "?$filter=onepdt_name eq 'WechatBaseURL'&$select=onepdt_value";

//    try {
//        var response = await Xrm.WebApi.retrieveMultipleRecords("onepdt_t_configuration", query);
//        if (response.entities.length > 0) {
//            BaseURL = response.entities[0].onepdt_value;
//        } else {
//            throw new Error("未找到[WechatBaseURL]参数配置");
//        }
//    } catch (error) {
//        console.error("查询失败: ", error.message);
//        Xrm.Utility.alertDialog("获取URL失败: " + error.message);
//        throw error;  // 重新抛出异常
//    }
//}

//// 获取token
//// 创建 POST 请求的函数
//function createToken() {
//    const url = BaseURL + '/api/auth/createtoken'; // API URL
//    const requestBody = {
//        key: '228592F52FC3CB588B32AC2B1EC495D78AC4D1016C32F392F8258E0586C0D8EA'
//    };

//    try {
//        // 发送 POST 请求
//        const response = await fetch(url, {
//            method: 'POST',
//            headers: {
//                'Content-Type': 'application/json' // 请求头指定发送 JSON 格式数据
//            },
//            body: JSON.stringify(requestBody) // 请求体数据转换为 JSON 字符串
//        });

//        // 确保请求成功
//        if (!response.ok) {
//            Xrm.Utility.alertDialog('Network response was not ok ' + response.statusText);
//            throw new Error('Network response was not ok ' + response.statusText);
//        }

//        // 解析返回的 JSON 数据
//        const responseData = await response.json();

//        // 获取返回的 data
//        AccessToken = responseData.data;
//    } catch (error) {
//        throw new Error('请求出错:', error);
//    }
//}

////请求接口
//function getDataAll(logid, api) {
//    // 构建查询参数
//    const url = `${BaseURL}/Api/${api}?logid=${encodeURIComponent(logid)}`;

//    // 设置 Authorization 头部
//    const headers = {
//        'Content-Type': 'application/json', // 设置请求头
//        'Authorization': 'Bearer ' + AccessToken, // 添加 Authorization 头
//    };

//    // 异步请求 API
//    fetch(url, {
//        method: 'GET', // GET 请求
//        headers: headers
//    })
//        .then(async (response) => {
//            //if (!response.ok) {
//            //    throw new Error(`HTTP 错误! 状态: ${response.status}`);
//            //}

//            //// 解析返回的 JSON 数据
//            //const data = await response.json();

//            //// 打印或处理数据
//            //console.log(data);

//            //// 如果需要，将数据返回给其他调用者
//            //return data;
//        })
//        .catch((error) => {
//            // 错误处理
//            console.error('请求失败:', error);
//        });
//}

function visable() {
    return true;
}

