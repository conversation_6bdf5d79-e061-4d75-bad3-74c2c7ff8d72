using System;
namespace Abt.Epdt.WebApis.Model
{


    public class EmployeeModel
    {

        private static string LogicName = "onepdt_t_employee_basic_mdata";

        public static string getLogicName()
        {
            return LogicName;
        }

        /// <summary>
        /// 数据标识
        /// </summary>
        public string sign { get; set; } = "";
        public string bu { get; set; } = "";
        public string code { get; set; } = "";
        public string code511 { get; set; } = "";
        public string comment { get; set; } = "";
        public string employmentstatus { get; set; } = "";
        public string gender { get; set; } = "";
        public string hiredate { get; set; } = "";
        public string mail { get; set; } = "";
        public string name { get; set; } = "";
        public string phone { get; set; } = "";
        public string position { get; set; } = "";
        public string rolename { get; set; } = "";
        // public Guid temployeebasicmdataid { get; set; }
        public string terminaldate { get; set; } = "";
        public string stdposition { get; set; } = "";
    }

    public class EmployeeInfo
    {
        public string userid { get; set; }
        public string name { get; set; }
        public string mobile { get; set; }
        public string gender { get; set; }
        public string email { get; set; }
        public string position { get; set; }
        public string department { get; set; }
        public string status { get; set; }
    }
}